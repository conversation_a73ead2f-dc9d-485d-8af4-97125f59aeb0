import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import {
  BarChart3,
  Download,
  Filter,
  Calendar,
  Users,
  Mail,
  Activity,
  TrendingUp,
  Eye,
  MousePointer,
  AlertCircle,
  ArrowLeft
} from 'lucide-react';
import { toast } from 'react-hot-toast';
import { apiHelpers, endpoints } from '../../services/api';

const ComprehensiveReports = () => {
  const { t, i18n } = useTranslation();
  const navigate = useNavigate();
  const isRTL = i18n.language === 'ar';

  const [loading, setLoading] = useState(false);
  const [activeReport, setActiveReport] = useState('interactions');
  const [dateRange, setDateRange] = useState({
    from: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
    to: new Date().toISOString().split('T')[0]
  });
  const [filters, setFilters] = useState({
    message_id: '',
    client_id: '',
    status: '',
    type: ''
  });
  const [reportData, setReportData] = useState(null);
  const [dataCache, setDataCache] = useState({});

  const reportTypes = [
    {
      id: 'interactions',
      name: isRTL ? 'تقرير التفاعلات' : 'Interactions Report',
      icon: Activity,
      description: isRTL ? 'جميع التفاعلات مع الرسائل' : 'All message interactions'
    },
    {
      id: 'messages',
      name: isRTL ? 'تقرير الرسائل' : 'Messages Report',
      icon: Mail,
      description: isRTL ? 'إحصائيات الرسائل المرسلة' : 'Sent messages statistics'
    },
    {
      id: 'clients',
      name: isRTL ? 'تقرير العملاء' : 'Clients Report',
      icon: Users,
      description: isRTL ? 'نشاط وتفاعل العملاء' : 'Client activity and engagement'
    },
    {
      id: 'performance',
      name: isRTL ? 'تقرير الأداء' : 'Performance Report',
      icon: TrendingUp,
      description: isRTL ? 'معدلات الفتح والنقر' : 'Open and click rates'
    }
  ];

  useEffect(() => {
    const timeoutId = setTimeout(() => {
      loadReportData();
    }, 300); // Debounce for 300ms

    return () => clearTimeout(timeoutId);
  }, [activeReport, dateRange.from, dateRange.to]);

  const loadReportData = async () => {
    try {
      setLoading(true);

      // Create cache key
      const cacheKey = `${activeReport}-${dateRange.from}-${dateRange.to}-${JSON.stringify(filters)}`;

      // Check cache first
      if (dataCache[cacheKey]) {
        setReportData(dataCache[cacheKey]);
        setLoading(false);
        return;
      }

      let endpoint = '';
      let params = {
        date_from: dateRange.from,
        date_to: dateRange.to,
        ...filters
      };

      switch (activeReport) {
        case 'interactions':
          endpoint = endpoints.reports.interactions;
          break;
        case 'messages':
          endpoint = endpoints.messages.list;
          break;
        case 'clients':
          endpoint = endpoints.clients.list;
          break;
        case 'performance':
          endpoint = endpoints.reports.emailPerformance;
          break;
        default:
          endpoint = endpoints.reports.interactions;
      }

      const result = await apiHelpers.get(endpoint, { params });

      if (result.success) {
        // Handle different data structures
        const data = result.data?.data || result.data || [];
        const processedData = Array.isArray(data) ? data : [];

        setReportData(processedData);

        // Cache the data
        setDataCache(prev => ({
          ...prev,
          [cacheKey]: processedData
        }));

        console.log('Report data loaded:', processedData);
      } else {
        toast.error(t('reports.error_loading'));
        setReportData([]);
      }
    } catch (error) {
      console.error('Error loading report data:', error);
      toast.error(t('reports.error_loading'));
    } finally {
      setLoading(false);
    }
  };

  const exportReport = async () => {
    try {
      const csvData = generateCSV();
      const blob = new Blob([csvData], { type: 'text/csv;charset=utf-8;' });
      const link = document.createElement('a');
      const url = URL.createObjectURL(blob);
      link.setAttribute('href', url);
      link.setAttribute('download', `${activeReport}_report_${dateRange.from}_${dateRange.to}.csv`);
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      
      toast.success(isRTL ? 'تم تصدير التقرير بنجاح' : 'Report exported successfully');
    } catch (error) {
      console.error('Error exporting report:', error);
      toast.error(isRTL ? 'فشل في تصدير التقرير' : 'Failed to export report');
    }
  };

  const generateCSV = () => {
    if (!reportData) return '';

    let headers = [];
    let rows = [];

    switch (activeReport) {
      case 'interactions':
        headers = ['ID', 'Client ID', 'Message ID', 'Type', 'Status', 'Sent At', 'Opened At', 'Clicked At'];
        rows = (reportData.interactions || []).map(item => [
          item.id,
          item.client_id,
          item.message_id,
          item.type,
          item.status,
          item.sent_at || '',
          item.opened_at || '',
          item.clicked_at || ''
        ]);
        break;
      case 'messages':
        headers = ['ID', 'Title', 'Type', 'Status', 'Language', 'Created At'];
        rows = (reportData.messages || []).map(item => [
          item.id,
          item.title,
          item.type,
          item.status,
          item.language || '',
          item.created_at
        ]);
        break;
      case 'clients':
        headers = ['ID', 'Name', 'Email', 'Category', 'Language', 'Status', 'Created At'];
        rows = (reportData.clients || []).map(item => [
          item.id,
          item.name,
          item.email,
          item.category,
          item.language || '',
          item.status,
          item.created_at
        ]);
        break;
      case 'performance':
        headers = ['Metric', 'Value'];
        rows = [
          ['Total Sent', reportData.sent || 0],
          ['Total Opened', reportData.opened || 0],
          ['Total Clicked', reportData.clicked || 0],
          ['Open Rate', `${reportData.rates?.open_rate || 0}%`],
          ['Click Rate', `${reportData.rates?.click_rate || 0}%`],
          ['Bounce Rate', `${reportData.rates?.bounce_rate || 0}%`]
        ];
        break;
    }

    const csvContent = [
      headers.join(','),
      ...rows.map(row => row.map(cell => `"${cell}"`).join(','))
    ].join('\n');

    return '\uFEFF' + csvContent; // Add BOM for UTF-8
  };

  const renderReportContent = () => {
    if (loading) {
      return (
        <div className="flex items-center justify-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
          <span className="ml-2">{isRTL ? 'جاري التحميل...' : 'Loading...'}</span>
        </div>
      );
    }

    if (!reportData) {
      return (
        <div className="text-center py-12">
          <AlertCircle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <p className="text-gray-500">{isRTL ? 'لا توجد بيانات' : 'No data available'}</p>
        </div>
      );
    }

    switch (activeReport) {
      case 'interactions':
        return renderInteractionsReport();
      case 'messages':
        return renderMessagesReport();
      case 'clients':
        return renderClientsReport();
      case 'performance':
        return renderPerformanceReport();
      default:
        return null;
    }
  };

  const renderInteractionsReport = () => {
    const interactions = Array.isArray(reportData) ? reportData : (reportData?.interactions || []);
    
    return (
      <div className="overflow-x-auto">
        <table className="table">
          <thead className="table-header">
            <tr>
              <th>ID</th>
              <th>{isRTL ? 'العميل' : 'Client'}</th>
              <th>{isRTL ? 'الرسالة' : 'Message'}</th>
              <th>{isRTL ? 'النوع' : 'Type'}</th>
              <th>{isRTL ? 'الحالة' : 'Status'}</th>
              <th>{isRTL ? 'تاريخ الإرسال' : 'Sent At'}</th>
              <th>{isRTL ? 'تاريخ الفتح' : 'Opened At'}</th>
              <th>{isRTL ? 'تاريخ النقر' : 'Clicked At'}</th>
            </tr>
          </thead>
          <tbody>
            {interactions.length === 0 ? (
              <tr>
                <td colSpan="8" className="text-center py-8 text-gray-500">
                  {isRTL ? 'لا توجد تفاعلات' : 'No interactions found'}
                </td>
              </tr>
            ) : (
              interactions.map((interaction) => (
                <tr key={interaction.id} className="table-row-hover">
                  <td className="font-medium">{interaction.id}</td>
                  <td>
                    <div>
                      <div className="font-medium">{interaction.client_name || `Client ${interaction.client_id}`}</div>
                      <div className="text-sm text-gray-500">{interaction.client_email}</div>
                    </div>
                  </td>
                  <td>
                    <div className="font-medium">{interaction.message_title || `Message ${interaction.message_id}`}</div>
                  </td>
                  <td>
                    <span className={`badge ${
                      interaction.type === 'email_sent' ? 'badge-info' :
                      interaction.type === 'email' ? 'badge-primary' :
                      'badge-secondary'
                    }`}>
                      {interaction.type}
                    </span>
                  </td>
                  <td>
                    <span className={`badge ${
                      interaction.status === 'sent' ? 'badge-success' :
                      interaction.status === 'opened' ? 'badge-primary' :
                      interaction.status === 'clicked' ? 'badge-warning' :
                      interaction.status === 'failed' ? 'badge-danger' :
                      'badge-secondary'
                    }`}>
                      {interaction.status}
                    </span>
                  </td>
                  <td className="text-sm text-gray-500">
                    {interaction.sent_at ? new Date(interaction.sent_at).toLocaleString() :
                     (interaction.type === 'email_sent' || interaction.status === 'sent') ?
                     new Date(interaction.created_at).toLocaleString() : '-'}
                  </td>
                  <td className="text-sm text-gray-500">
                    {interaction.opened_at ? new Date(interaction.opened_at).toLocaleString() :
                     (interaction.status === 'opened') ?
                     new Date(interaction.created_at).toLocaleString() : '-'}
                  </td>
                  <td className="text-sm text-gray-500">
                    {interaction.clicked_at ? new Date(interaction.clicked_at).toLocaleString() :
                     (interaction.status === 'clicked') ?
                     new Date(interaction.created_at).toLocaleString() : '-'}
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>
    );
  };

  const renderMessagesReport = () => {
    const messages = Array.isArray(reportData) ? reportData : (reportData?.messages || []);
    
    return (
      <div className="overflow-x-auto">
        <table className="table">
          <thead className="table-header">
            <tr>
              <th>ID</th>
              <th>{isRTL ? 'العنوان' : 'Title'}</th>
              <th>{isRTL ? 'النوع' : 'Type'}</th>
              <th>{isRTL ? 'الحالة' : 'Status'}</th>
              <th>{isRTL ? 'اللغة' : 'Language'}</th>
              <th>{isRTL ? 'تاريخ الإنشاء' : 'Created At'}</th>
            </tr>
          </thead>
          <tbody>
            {messages.length === 0 ? (
              <tr>
                <td colSpan="6" className="text-center py-8 text-gray-500">
                  {isRTL ? 'لا توجد رسائل' : 'No messages found'}
                </td>
              </tr>
            ) : (
              messages.map((message) => (
                <tr key={message.id} className="table-row-hover">
                  <td className="font-medium">{message.id}</td>
                  <td>{message.title}</td>
                  <td>
                    <span className="badge badge-primary">{message.type}</span>
                  </td>
                  <td>
                    <span className={`badge ${
                      message.status === 'published' ? 'badge-success' :
                      message.status === 'draft' ? 'badge-secondary' :
                      'badge-warning'
                    }`}>
                      {message.status}
                    </span>
                  </td>
                  <td>{message.language || '-'}</td>
                  <td className="text-sm text-gray-500">
                    {new Date(message.created_at).toLocaleString()}
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>
    );
  };

  const renderClientsReport = () => {
    const clients = Array.isArray(reportData) ? reportData : (reportData?.clients || []);
    
    return (
      <div className="overflow-x-auto">
        <table className="table">
          <thead className="table-header">
            <tr>
              <th>ID</th>
              <th>{isRTL ? 'الاسم' : 'Name'}</th>
              <th>{isRTL ? 'البريد الإلكتروني' : 'Email'}</th>
              <th>{isRTL ? 'الفئة' : 'Category'}</th>
              <th>{isRTL ? 'اللغة' : 'Language'}</th>
              <th>{isRTL ? 'الحالة' : 'Status'}</th>
              <th>{isRTL ? 'تاريخ التسجيل' : 'Created At'}</th>
            </tr>
          </thead>
          <tbody>
            {clients.length === 0 ? (
              <tr>
                <td colSpan="7" className="text-center py-8 text-gray-500">
                  {isRTL ? 'لا توجد عملاء' : 'No clients found'}
                </td>
              </tr>
            ) : (
              clients.map((client) => (
                <tr key={client.id} className="table-row-hover">
                  <td className="font-medium">{client.id}</td>
                  <td>{client.name}</td>
                  <td>{client.email}</td>
                  <td>
                    <span className="badge badge-primary">{client.category}</span>
                  </td>
                  <td>{client.language || '-'}</td>
                  <td>
                    <span className={`badge ${
                      client.status === 'active' ? 'badge-success' :
                      client.status === 'inactive' ? 'badge-secondary' :
                      'badge-warning'
                    }`}>
                      {client.status}
                    </span>
                  </td>
                  <td className="text-sm text-gray-500">
                    {new Date(client.created_at).toLocaleString()}
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>
    );
  };

  const renderPerformanceReport = () => {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="card">
          <div className="card-body text-center">
            <Mail className="h-8 w-8 text-blue-600 mx-auto mb-2" />
            <div className="text-3xl font-bold text-blue-600">
              {reportData.sent || 0}
            </div>
            <div className="text-sm text-gray-600 mt-1">
              {isRTL ? 'إجمالي المرسل' : 'Total Sent'}
            </div>
          </div>
        </div>
        
        <div className="card">
          <div className="card-body text-center">
            <Eye className="h-8 w-8 text-green-600 mx-auto mb-2" />
            <div className="text-3xl font-bold text-green-600">
              {reportData.opened || 0}
            </div>
            <div className="text-sm text-gray-600 mt-1">
              {isRTL ? 'إجمالي المفتوح' : 'Total Opened'}
            </div>
            <div className="text-xs text-gray-500 mt-1">
              {reportData.rates?.open_rate || 0}%
            </div>
          </div>
        </div>
        
        <div className="card">
          <div className="card-body text-center">
            <MousePointer className="h-8 w-8 text-orange-600 mx-auto mb-2" />
            <div className="text-3xl font-bold text-orange-600">
              {reportData.clicked || 0}
            </div>
            <div className="text-sm text-gray-600 mt-1">
              {isRTL ? 'إجمالي النقرات' : 'Total Clicks'}
            </div>
            <div className="text-xs text-gray-500 mt-1">
              {reportData.rates?.click_rate || 0}%
            </div>
          </div>
        </div>
        
        <div className="card">
          <div className="card-body text-center">
            <AlertCircle className="h-8 w-8 text-red-600 mx-auto mb-2" />
            <div className="text-3xl font-bold text-red-600">
              {reportData.bounced || 0}
            </div>
            <div className="text-sm text-gray-600 mt-1">
              {isRTL ? 'إجمالي المرتد' : 'Total Bounced'}
            </div>
            <div className="text-xs text-gray-500 mt-1">
              {reportData.rates?.bounce_rate || 0}%
            </div>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4 rtl:space-x-reverse">
          <button
            onClick={() => navigate('/reports')}
            className="btn btn-secondary flex items-center space-x-2 rtl:space-x-reverse"
          >
            <ArrowLeft className="h-4 w-4" />
            <span>{isRTL ? 'رجوع' : 'Back'}</span>
          </button>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">
              {isRTL ? 'التقارير الشاملة' : 'Comprehensive Reports'}
            </h1>
            <p className="text-gray-600 mt-1">
              {isRTL ? 'تقارير مفصلة لجميع البيانات' : 'Detailed reports for all data'}
            </p>
          </div>
        </div>
        
        <button
          onClick={exportReport}
          disabled={loading || !reportData}
          className="btn btn-primary flex items-center space-x-2 rtl:space-x-reverse"
        >
          <Download className="h-4 w-4" />
          <span>{isRTL ? 'تصدير CSV' : 'Export CSV'}</span>
        </button>
      </div>

      {/* Report Type Selection */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {reportTypes.map((report) => {
          const Icon = report.icon;
          return (
            <button
              key={report.id}
              onClick={() => setActiveReport(report.id)}
              className={`p-4 rounded-lg border-2 transition-all ${
                activeReport === report.id
                  ? 'border-primary-500 bg-primary-50'
                  : 'border-gray-200 hover:border-gray-300'
              }`}
            >
              <Icon className={`h-6 w-6 mx-auto mb-2 ${
                activeReport === report.id ? 'text-primary-600' : 'text-gray-600'
              }`} />
              <h3 className="font-medium text-sm">{report.name}</h3>
              <p className="text-xs text-gray-500 mt-1">{report.description}</p>
            </button>
          );
        })}
      </div>

      {/* Filters */}
      <div className="card">
        <div className="card-header">
          <h3 className="text-lg font-medium text-gray-900 flex items-center">
            <Filter className="h-5 w-5 mr-2" />
            {isRTL ? 'المرشحات' : 'Filters'}
          </h3>
        </div>
        <div className="card-body">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                {isRTL ? 'من تاريخ' : 'From Date'}
              </label>
              <input
                type="date"
                className="input"
                value={dateRange.from}
                onChange={(e) => setDateRange(prev => ({ ...prev, from: e.target.value }))}
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                {isRTL ? 'إلى تاريخ' : 'To Date'}
              </label>
              <input
                type="date"
                className="input"
                value={dateRange.to}
                onChange={(e) => setDateRange(prev => ({ ...prev, to: e.target.value }))}
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                {isRTL ? 'رقم الرسالة' : 'Message ID'}
              </label>
              <input
                type="number"
                className="input"
                placeholder={isRTL ? 'رقم الرسالة' : 'Message ID'}
                value={filters.message_id}
                onChange={(e) => setFilters(prev => ({ ...prev, message_id: e.target.value }))}
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                {isRTL ? 'رقم العميل' : 'Client ID'}
              </label>
              <input
                type="number"
                className="input"
                placeholder={isRTL ? 'رقم العميل' : 'Client ID'}
                value={filters.client_id}
                onChange={(e) => setFilters(prev => ({ ...prev, client_id: e.target.value }))}
              />
            </div>
          </div>
        </div>
      </div>

      {/* Report Content */}
      <div className="card">
        <div className="card-header">
          <h3 className="text-lg font-medium text-gray-900 flex items-center">
            <BarChart3 className="h-5 w-5 mr-2" />
            {reportTypes.find(r => r.id === activeReport)?.name}
          </h3>
        </div>
        <div className="card-body">
          {renderReportContent()}
        </div>
      </div>
    </div>
  );
};

export default ComprehensiveReports;
