const express = require('express');
const router = express.Router();
const { db } = require('../config/database');
const crypto = require('crypto');

// Track email open (pixel tracking)
router.get('/pixel/:trackingId', async (req, res) => {
  try {
    const { trackingId } = req.params;
    const userAgent = req.get('User-Agent') || '';
    const ipAddress = req.ip || req.connection.remoteAddress || '';

    console.log(`📧 Email open tracked - Tracking ID: ${trackingId.substring(0, 8)}...`);

    // Find the interaction record using metadata
    const interaction = await new Promise((resolve, reject) => {
      db.get(
        `SELECT * FROM interactions WHERE metadata LIKE ?`,
        [`%"tracking_id":"${trackingId}"%`],
        (err, row) => {
          if (err) reject(err);
          else resolve(row);
        }
      );
    });

    if (interaction) {
      console.log(`✅ Found interaction for client ${interaction.client_id}, message ${interaction.message_id}`);

      // Update interaction record
      await new Promise((resolve, reject) => {
        db.run(
          `UPDATE interactions SET
            opened_at = CURRENT_TIMESTAMP,
            open_count = open_count + 1,
            last_opened_at = CURRENT_TIMESTAMP,
            ip_address = ?,
            user_agent = ?,
            status = 'opened'
           WHERE tracking_id = ?`,
          [ipAddress, userAgent, trackingId],
          function(err) {
            if (err) reject(err);
            else resolve();
          }
        );
      });

      // Update client engagement score
      await new Promise((resolve, reject) => {
        db.run(
          `UPDATE clients SET 
            total_emails_opened = total_emails_opened + 1,
            engagement_score = engagement_score + 5,
            last_activity = CURRENT_TIMESTAMP
           WHERE id = ?`,
          [interaction.client_id],
          function(err) {
            if (err) reject(err);
            else resolve();
          }
        );
      });

      console.log(`Email opened: tracking_id=${trackingId}, client_id=${interaction.client_id}`);
    }

    // Return 1x1 transparent pixel
    const pixel = Buffer.from(
      'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==',
      'base64'
    );

    res.set({
      'Content-Type': 'image/png',
      'Content-Length': pixel.length,
      'Cache-Control': 'no-cache, no-store, must-revalidate',
      'Pragma': 'no-cache',
      'Expires': '0'
    });

    res.send(pixel);

  } catch (error) {
    console.error('Error tracking email open:', error);
    
    // Still return pixel even on error
    const pixel = Buffer.from(
      'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==',
      'base64'
    );
    
    res.set({
      'Content-Type': 'image/png',
      'Content-Length': pixel.length
    });
    
    res.send(pixel);
  }
});

// Track link click
router.get('/click/:trackingId/:linkId', async (req, res) => {
  try {
    const { trackingId, linkId } = req.params;
    const { url } = req.query;
    const userAgent = req.get('User-Agent') || '';
    const ipAddress = req.ip || req.connection.remoteAddress || '';

    console.log(`🔗 Link click tracked - Tracking ID: ${trackingId.substring(0, 8)}..., URL: ${url}`);

    // Find the interaction record using metadata
    const interaction = await new Promise((resolve, reject) => {
      db.get(
        `SELECT * FROM interactions WHERE metadata LIKE ?`,
        [`%"tracking_id":"${trackingId}"%`],
        (err, row) => {
          if (err) reject(err);
          else resolve(row);
        }
      );
    });

    if (interaction) {
      console.log(`✅ Found interaction for client ${interaction.client_id}, recording click`);

      // Update interaction record (simplified for existing columns)
      await new Promise((resolve, reject) => {
        db.run(
          `UPDATE interactions SET
            clicked_at = CURRENT_TIMESTAMP,
            status = 'clicked'
           WHERE id = ?`,
          [interaction.id],
          function(err) {
            if (err) reject(err);
            else resolve();
          }
        );
      });

      // Update client engagement score (simplified for existing columns)
      await new Promise((resolve, reject) => {
        db.run(
          `UPDATE clients SET
            updated_at = CURRENT_TIMESTAMP
           WHERE id = ?`,
          [interaction.client_id],
          function(err) {
            if (err) reject(err);
            else resolve();
          }
        );
      });

      console.log(`Link clicked: tracking_id=${trackingId}, client_id=${interaction.client_id}, url=${url}`);
    }

    // Redirect to the original URL
    if (url) {
      res.redirect(decodeURIComponent(url));
    } else {
      res.status(400).json({
        success: false,
        error: 'No URL provided'
      });
    }

  } catch (error) {
    console.error('Error tracking link click:', error);
    
    // Redirect to URL even on error
    if (req.query.url) {
      res.redirect(decodeURIComponent(req.query.url));
    } else {
      res.status(500).json({
        success: false,
        error: 'Tracking error'
      });
    }
  }
});

// Unsubscribe endpoint
router.get('/unsubscribe/:token', async (req, res) => {
  try {
    const { token } = req.params;

    // Find client by unsubscribe token
    const client = await new Promise((resolve, reject) => {
      db.get(
        'SELECT * FROM clients WHERE unsubscribe_token = ?',
        [token],
        (err, row) => {
          if (err) reject(err);
          else resolve(row);
        }
      );
    });

    if (!client) {
      return res.status(404).send(`
        <!DOCTYPE html>
        <html>
        <head>
            <title>Unsubscribe</title>
            <meta charset="UTF-8">
            <style>
                body { font-family: Arial, sans-serif; text-align: center; padding: 50px; }
                .container { max-width: 500px; margin: 0 auto; }
                .error { color: #dc3545; }
            </style>
        </head>
        <body>
            <div class="container">
                <h2 class="error">Invalid Unsubscribe Link</h2>
                <p>The unsubscribe link is invalid or has expired.</p>
            </div>
        </body>
        </html>
      `);
    }

    if (client.unsubscribed) {
      return res.send(`
        <!DOCTYPE html>
        <html>
        <head>
            <title>Already Unsubscribed</title>
            <meta charset="UTF-8">
            <style>
                body { font-family: Arial, sans-serif; text-align: center; padding: 50px; }
                .container { max-width: 500px; margin: 0 auto; }
                .success { color: #28a745; }
            </style>
        </head>
        <body>
            <div class="container">
                <h2 class="success">Already Unsubscribed</h2>
                <p>You have already been unsubscribed from our mailing list.</p>
            </div>
        </body>
        </html>
      `);
    }

    // Update client to unsubscribed
    await new Promise((resolve, reject) => {
      db.run(
        `UPDATE clients SET 
          unsubscribed = 1,
          unsubscribed_at = CURRENT_TIMESTAMP
         WHERE unsubscribe_token = ?`,
        [token],
        function(err) {
          if (err) reject(err);
          else resolve();
        }
      );
    });

    // Return success page
    res.send(`
      <!DOCTYPE html>
      <html>
      <head>
          <title>Unsubscribed Successfully</title>
          <meta charset="UTF-8">
          <style>
              body { font-family: Arial, sans-serif; text-align: center; padding: 50px; }
              .container { max-width: 500px; margin: 0 auto; }
              .success { color: #28a745; }
          </style>
      </head>
      <body>
          <div class="container">
              <h2 class="success">Unsubscribed Successfully</h2>
              <p>You have been successfully unsubscribed from our mailing list.</p>
              <p>You will no longer receive emails from us.</p>
          </div>
      </body>
      </html>
    `);

    console.log(`Client unsubscribed: ${client.email}`);

  } catch (error) {
    console.error('Error processing unsubscribe:', error);
    res.status(500).send(`
      <!DOCTYPE html>
      <html>
      <head>
          <title>Error</title>
          <meta charset="UTF-8">
          <style>
              body { font-family: Arial, sans-serif; text-align: center; padding: 50px; }
              .container { max-width: 500px; margin: 0 auto; }
              .error { color: #dc3545; }
          </style>
      </head>
      <body>
          <div class="container">
              <h2 class="error">Error</h2>
              <p>An error occurred while processing your unsubscribe request.</p>
          </div>
      </body>
      </html>
    `);
  }
});

// Generate tracking URLs for email content
router.post('/generate-tracking', async (req, res) => {
  try {
    const { messageId, clientId, htmlContent } = req.body;

    if (!messageId || !clientId || !htmlContent) {
      return res.status(400).json({
        success: false,
        error: 'Message ID, client ID, and HTML content are required'
      });
    }

    // Generate unique tracking ID
    const trackingId = crypto.randomBytes(32).toString('hex');

    // Create interaction record
    await new Promise((resolve, reject) => {
      db.run(
        `INSERT INTO interactions (
          client_id, message_id, type, tracking_id, status
        ) VALUES (?, ?, 'email', ?, 'sent')`,
        [clientId, messageId, trackingId],
        function(err) {
          if (err) reject(err);
          else resolve({ id: this.lastID });
        }
      );
    });

    // Generate tracking pixel URL
    const trackingPixelUrl = `${req.protocol}://${req.get('host')}/api/tracking/pixel/${trackingId}`;

    // Process HTML content to add tracking to links
    let trackedHtml = htmlContent;
    let linkId = 1;

    // Replace all links with tracking links
    trackedHtml = trackedHtml.replace(/<a\s+([^>]*href\s*=\s*["']([^"']+)["'][^>]*)>/gi, (match, attributes, url) => {
      if (url.startsWith('mailto:') || url.startsWith('#') || url.includes('/api/tracking/')) {
        return match; // Don't track mailto links, anchors, or already tracked links
      }

      const trackingUrl = `${req.protocol}://${req.get('host')}/api/tracking/click/${trackingId}/${linkId}?url=${encodeURIComponent(url)}`;
      linkId++;

      return `<a ${attributes.replace(/href\s*=\s*["'][^"']+["']/i, `href="${trackingUrl}"`)}>`; 
    });

    res.json({
      success: true,
      data: {
        trackingId,
        trackingPixelUrl,
        trackedHtml
      }
    });

  } catch (error) {
    console.error('Error generating tracking:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to generate tracking'
    });
  }
});

module.exports = router;
