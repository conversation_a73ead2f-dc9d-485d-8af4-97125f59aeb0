import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Link } from 'react-router-dom';
import {
  Download,
  Calendar,
  BarChart3,
  TrendingUp,
  Users,
  Mail,
  Eye,
  MousePointer,
  Filter
} from 'lucide-react';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, BarChart, Bar, PieChart, Pie, Cell } from 'recharts';
import { useForm } from 'react-hook-form';
import LoadingSpinner from '../../components/UI/LoadingSpinner';
import { apiHelpers, endpoints } from '../../services/api';
import toast from 'react-hot-toast';

const Reports = () => {
  const { t, i18n } = useTranslation();
  const [loading, setLoading] = useState(true);
  const [dashboardData, setDashboardData] = useState(null);
  const [emailPerformance, setEmailPerformance] = useState([]);
  const [clientEngagement, setClientEngagement] = useState([]);
  const [timelineData, setTimelineData] = useState([]);
  const [activeReport, setActiveReport] = useState('dashboard');

  const isRTL = i18n.language === 'ar';

  const { register, watch, handleSubmit } = useForm({
    defaultValues: {
      date_from: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      date_to: new Date().toISOString().split('T')[0],
      interval: 'day'
    }
  });

  const filters = watch();

  // Load reports on component mount
  useEffect(() => {
    loadReportsData();
  }, []);

  // Load reports when filters change (with debounce)
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      loadReportsData();
    }, 500); // 500ms debounce for reports

    return () => clearTimeout(timeoutId);
  }, [filters.dateFrom, filters.dateTo, filters.interval]);

  const loadReportsData = async () => {
    try {
      if (loading) {
        console.log('⚠️ Skipping duplicate request - already loading reports');
        return;
      }
      setLoading(true);

      // Load dashboard data
      const dashboardResult = await apiHelpers.get(endpoints.reports.dashboard);
      if (dashboardResult.success) {
        setDashboardData(dashboardResult.data.data);
      }

      // Load email performance
      const performanceParams = new URLSearchParams(filters).toString();
      const performanceResult = await apiHelpers.get(`${endpoints.reports.emailPerformance}?${performanceParams}`);
      if (performanceResult.success) {
        setEmailPerformance(performanceResult.data.data);
      }

      // Load client engagement
      const engagementParams = new URLSearchParams(filters).toString();
      const engagementResult = await apiHelpers.get(`${endpoints.reports.clientEngagement}?${engagementParams}`);
      if (engagementResult.success) {
        setClientEngagement(engagementResult.data.data);
      }

      // Load timeline data
      const timelineParams = new URLSearchParams(filters).toString();
      const timelineResult = await apiHelpers.get(`${endpoints.reports.timeline}?${timelineParams}`);
      if (timelineResult.success) {
        setTimelineData(timelineResult.data.data);
      }

    } catch (error) {
      console.error('Error loading reports data:', error);
      toast.error(t('reports.error_loading'));
    } finally {
      setLoading(false);
    }
  };

  const handleExportReport = async (type, format = 'csv') => {
    try {
      const result = await apiHelpers.download(
        `${endpoints.reports.export(type)}?format=${format}&${new URLSearchParams(filters).toString()}`,
        `${type}_report.${format}`
      );

      if (result.success) {
        toast.success(t('messages_ui.success_export'));
      }
    } catch (error) {
      toast.error(t('messages_ui.error_export'));
    }
  };

  // Default empty data if API data is not available
  const mockDashboardData = {
    clients: { total: 0, active: 0, recent: 0 },
    messages: { total: 0, sent: 0 },
    interactions: { total: 0, sent: 0, opened: 0, clicked: 0, bounced: 0 },
    performance: { open_rate: 0, click_rate: 0, bounce_rate: 0 }
  };

  const mockTimelineData = [];

  const displayDashboardData = dashboardData || mockDashboardData;
  const displayTimelineData = timelineData.length > 0 ? timelineData : mockTimelineData;

  const reportTypes = [
    {
      id: 'dashboard',
      title: t('reports.dashboard_report'),
      description: isRTL ? 'نظرة عامة على الأداء' : 'Overall performance overview',
      icon: BarChart3
    },
    {
      id: 'email_performance',
      title: t('reports.email_performance'),
      description: isRTL ? 'أداء الرسائل الإلكترونية' : 'Email campaign performance',
      icon: Mail
    },
    {
      id: 'client_engagement',
      title: t('reports.client_engagement'),
      description: isRTL ? 'تفاعل العملاء' : 'Client engagement metrics',
      icon: Users
    },
    {
      id: 'time_analytics',
      title: t('reports.time_analytics'),
      description: isRTL ? 'التحليلات الزمنية' : 'Time-based analytics',
      icon: TrendingUp
    }
  ];

  // Performance metrics for pie chart
  const performanceMetrics = [
    { name: isRTL ? 'مفتوح' : 'Opened', value: displayDashboardData.interactions.opened, color: '#10b981' },
    { name: isRTL ? 'منقور' : 'Clicked', value: displayDashboardData.interactions.clicked, color: '#3b82f6' },
    { name: isRTL ? 'مرتد' : 'Bounced', value: displayDashboardData.interactions.bounced, color: '#ef4444' },
    {
      name: isRTL ? 'غير مفتوح' : 'Not Opened',
      value: displayDashboardData.interactions.sent - displayDashboardData.interactions.opened,
      color: '#6b7280'
    }
  ];

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="large" text={t('common.loading')} />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">
            {t('reports.title')}
          </h1>
          <p className="mt-2 text-gray-600">
            {isRTL ? 'تحليلات وتقارير شاملة للنظام' : 'Comprehensive system analytics and reports'}
          </p>
        </div>
        <div className="flex items-center space-x-3 rtl:space-x-reverse">
          <Link
            to="/reports/comprehensive"
            className="btn btn-secondary flex items-center space-x-2 rtl:space-x-reverse"
          >
            <BarChart3 className="h-4 w-4" />
            <span>{isRTL ? 'التقارير الشاملة' : 'Comprehensive Reports'}</span>
          </Link>
          <button
            onClick={() => handleExportReport('dashboard', 'csv')}
            className="btn btn-outline flex items-center space-x-2 rtl:space-x-reverse"
          >
            <Download className="h-4 w-4" />
            <span>{t('reports.export_report')}</span>
          </button>
        </div>
      </div>

      {/* Filters */}
      <div className="card">
        <div className="card-body">
          <form className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                {t('reports.from_date')}
              </label>
              <input
                type="date"
                className="input"
                {...register('date_from')}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                {t('reports.to_date')}
              </label>
              <input
                type="date"
                className="input"
                {...register('date_to')}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                {isRTL ? 'الفترة الزمنية' : 'Time Interval'}
              </label>
              <select className="input" {...register('interval')}>
                <option value="hour">{isRTL ? 'ساعة' : 'Hour'}</option>
                <option value="day">{isRTL ? 'يوم' : 'Day'}</option>
                <option value="week">{isRTL ? 'أسبوع' : 'Week'}</option>
                <option value="month">{isRTL ? 'شهر' : 'Month'}</option>
              </select>
            </div>

            <div className="flex items-end">
              <button
                type="button"
                onClick={loadReportsData}
                className="btn btn-primary w-full flex items-center justify-center space-x-2 rtl:space-x-reverse"
              >
                <Filter className="h-4 w-4" />
                <span>{t('common.apply_filters')}</span>
              </button>
            </div>
          </form>
        </div>
      </div>

      {/* Report Type Selector */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        {reportTypes.map((report) => {
          const Icon = report.icon;
          return (
            <button
              key={report.id}
              onClick={() => setActiveReport(report.id)}
              className={`
                card text-left rtl:text-right p-4 transition-all hover:shadow-md
                ${activeReport === report.id ? 'ring-2 ring-primary-500 bg-primary-50' : ''}
              `}
            >
              <div className="flex items-center space-x-3 rtl:space-x-reverse">
                <div className={`
                  h-10 w-10 rounded-lg flex items-center justify-center
                  ${activeReport === report.id ? 'bg-primary-100' : 'bg-gray-100'}
                `}>
                  <Icon className={`
                    h-5 w-5
                    ${activeReport === report.id ? 'text-primary-600' : 'text-gray-600'}
                  `} />
                </div>
                <div>
                  <h3 className="font-medium text-gray-900">
                    {report.title}
                  </h3>
                  <p className="text-sm text-gray-500 mt-1">
                    {report.description}
                  </p>
                </div>
              </div>
            </button>
          );
        })}
      </div>

      {/* Report Content */}
      <div className="space-y-6">
        {activeReport === 'dashboard' && (
          <>
            {/* Key Metrics */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
              <div className="card">
                <div className="card-body text-center">
                  <div className="h-12 w-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                    <Users className="h-6 w-6 text-blue-600" />
                  </div>
                  <div className="text-2xl font-bold text-gray-900">
                    {displayDashboardData.clients.total.toLocaleString()}
                  </div>
                  <div className="text-sm text-gray-600 mt-1">
                    {t('dashboard.total_clients')}
                  </div>
                  <div className="text-xs text-green-600 mt-1">
                    +{displayDashboardData.clients.recent} {isRTL ? 'هذا الشهر' : 'this month'}
                  </div>
                </div>
              </div>

              <div className="card">
                <div className="card-body text-center">
                  <div className="h-12 w-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                    <Mail className="h-6 w-6 text-green-600" />
                  </div>
                  <div className="text-2xl font-bold text-gray-900">
                    {displayDashboardData.interactions.sent.toLocaleString()}
                  </div>
                  <div className="text-sm text-gray-600 mt-1">
                    {t('reports.total_sent')}
                  </div>
                </div>
              </div>

              <div className="card">
                <div className="card-body text-center">
                  <div className="h-12 w-12 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                    <Eye className="h-6 w-6 text-purple-600" />
                  </div>
                  <div className="text-2xl font-bold text-gray-900">
                    {displayDashboardData.performance.open_rate}%
                  </div>
                  <div className="text-sm text-gray-600 mt-1">
                    {t('dashboard.open_rate')}
                  </div>
                </div>
              </div>

              <div className="card">
                <div className="card-body text-center">
                  <div className="h-12 w-12 bg-orange-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                    <MousePointer className="h-6 w-6 text-orange-600" />
                  </div>
                  <div className="text-2xl font-bold text-gray-900">
                    {displayDashboardData.performance.click_rate}%
                  </div>
                  <div className="text-sm text-gray-600 mt-1">
                    {t('dashboard.click_rate')}
                  </div>
                </div>
              </div>
            </div>

            {/* Charts */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Timeline Chart */}
              <div className="card">
                <div className="card-header">
                  <h3 className="text-lg font-medium text-gray-900">
                    {t('reports.time_analytics')}
                  </h3>
                </div>
                <div className="card-body">
                  <ResponsiveContainer width="100%" height={300}>
                    <LineChart data={displayTimelineData}>
                      <CartesianGrid strokeDasharray="3 3" stroke="#6b7280" strokeOpacity={0.3} />
                      <XAxis
                        dataKey="period"
                        tick={{ fontSize: 12, fill: '#9ca3af' }}
                        tickFormatter={(value) => new Date(value).toLocaleDateString(isRTL ? 'ar-SA' : 'en-US', { month: 'short', day: 'numeric' })}
                      />
                      <YAxis tick={{ fontSize: 12, fill: '#9ca3af' }} />
                      <Tooltip
                        contentStyle={{
                          backgroundColor: '#374151',
                          border: '1px solid #6b7280',
                          borderRadius: '6px',
                          color: '#f9fafb'
                        }}
                        labelFormatter={(value) => new Date(value).toLocaleDateString(isRTL ? 'ar-SA' : 'en-US')}
                        formatter={(value, name) => [
                          value,
                          name === 'sent' ? (isRTL ? 'مرسل' : 'Sent') :
                          name === 'opened' ? (isRTL ? 'مفتوح' : 'Opened') :
                          name === 'clicked' ? (isRTL ? 'منقور' : 'Clicked') :
                          name === 'bounced' ? (isRTL ? 'مرتد' : 'Bounced') : name
                        ]}
                      />
                      <Line type="monotone" dataKey="sent" stroke="#3b82f6" strokeWidth={2} />
                      <Line type="monotone" dataKey="opened" stroke="#10b981" strokeWidth={2} />
                      <Line type="monotone" dataKey="clicked" stroke="#f59e0b" strokeWidth={2} />
                      <Line type="monotone" dataKey="bounced" stroke="#ef4444" strokeWidth={2} />
                    </LineChart>
                  </ResponsiveContainer>
                </div>
              </div>

              {/* Performance Distribution */}
              <div className="card">
                <div className="card-header">
                  <h3 className="text-lg font-medium text-gray-900">
                    {isRTL ? 'توزيع الأداء' : 'Performance Distribution'}
                  </h3>
                </div>
                <div className="card-body">
                  <ResponsiveContainer width="100%" height={300}>
                    <PieChart>
                      <Pie
                        data={performanceMetrics}
                        cx="50%"
                        cy="50%"
                        outerRadius={80}
                        fill="#8884d8"
                        dataKey="value"
                        label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                      >
                        {performanceMetrics.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={entry.color} />
                        ))}
                      </Pie>
                      <Tooltip />
                    </PieChart>
                  </ResponsiveContainer>
                </div>
              </div>
            </div>
          </>
        )}

        {activeReport === 'email_performance' && (
          <div className="card">
            <div className="card-header">
              <h3 className="text-lg font-medium text-gray-900">
                {t('reports.email_performance')}
              </h3>
            </div>
            <div className="overflow-x-auto">
              <table className="table">
                <thead className="table-header">
                  <tr>
                    <th>{t('messages.message_title')}</th>
                    <th>{t('reports.total_sent')}</th>
                    <th>{t('reports.total_opened')}</th>
                    <th>{t('reports.total_clicked')}</th>
                    <th>{t('dashboard.open_rate')}</th>
                    <th>{t('dashboard.click_rate')}</th>
                    <th>{t('messages.message_created')}</th>
                  </tr>
                </thead>
                <tbody className="table-body">
                  {emailPerformance.length > 0 ? emailPerformance.map((message, index) => (
                    <tr key={index} className="table-row-hover">
                      <td className="font-medium">{message.title}</td>
                      <td>{message.sent_count}</td>
                      <td>{message.opened_count}</td>
                      <td>{message.clicked_count}</td>
                      <td>
                        <span className="text-green-600 font-medium">
                          {message.open_rate}%
                        </span>
                      </td>
                      <td>
                        <span className="text-blue-600 font-medium">
                          {message.click_rate}%
                        </span>
                      </td>
                      <td className="text-sm text-gray-500">
                        {new Date(message.message_created).toLocaleDateString(isRTL ? 'ar-SA' : 'en-US')}
                      </td>
                    </tr>
                  )) : (
                    // No data available
                    <tr>
                      <td colSpan="7" className="text-center py-8 text-gray-500">
                        {t('common.no_data_available')}
                      </td>
                    </tr>
                  )}
                  {false && [].map((message, index) => (
                      <tr key={index} className="table-row-hover">
                        <td className="font-medium">{message.title}</td>
                        <td>{message.sent_count}</td>
                        <td>{message.opened_count}</td>
                        <td>{message.clicked_count}</td>
                        <td>
                          <span className="text-green-600 font-medium">
                            {message.open_rate}%
                          </span>
                        </td>
                        <td>
                          <span className="text-blue-600 font-medium">
                            {message.click_rate}%
                          </span>
                        </td>
                        <td className="text-sm text-gray-500">
                          {new Date(message.message_created).toLocaleDateString(isRTL ? 'ar-SA' : 'en-US')}
                        </td>
                      </tr>
                    ))
                  }
                </tbody>
              </table>
            </div>
          </div>
        )}

        {activeReport === 'client_engagement' && (
          <div className="card">
            <div className="card-header">
              <h3 className="text-lg font-medium text-gray-900">
                {t('reports.client_engagement')}
              </h3>
            </div>
            <div className="overflow-x-auto">
              <table className="table">
                <thead className="table-header">
                  <tr>
                    <th>{t('clients.client_name')}</th>
                    <th>{t('clients.client_email')}</th>
                    <th>{isRTL ? 'الرسائل المستلمة' : 'Emails Received'}</th>
                    <th>{isRTL ? 'الرسائل المفتوحة' : 'Emails Opened'}</th>
                    <th>{isRTL ? 'النقرات' : 'Clicks'}</th>
                    <th>{t('reports.engagement_rate')}</th>
                  </tr>
                </thead>
                <tbody className="table-body">
                  {clientEngagement.length > 0 ? clientEngagement.map((client, index) => (
                    <tr key={index} className="table-row-hover">
                      <td className="font-medium">{client.name}</td>
                      <td>{client.email}</td>
                      <td>{client.emails_received}</td>
                      <td>{client.emails_opened}</td>
                      <td>{client.emails_clicked}</td>
                      <td>
                        <span className="text-primary-600 font-medium">
                          {client.engagement_rate}%
                        </span>
                      </td>
                    </tr>
                  )) : (
                    // No data available
                    <tr>
                      <td colSpan="6" className="text-center py-8 text-gray-500">
                        {t('common.no_data_available')}
                      </td>
                    </tr>
                  )}
                  {false && [].map((client, index) => (
                      <tr key={index} className="table-row-hover">
                        <td className="font-medium">{client.name}</td>
                        <td>{client.email}</td>
                        <td>{client.emails_received}</td>
                        <td>{client.emails_opened}</td>
                        <td>{client.emails_clicked}</td>
                        <td>
                          <span className="text-primary-600 font-medium">
                            {client.engagement_rate}%
                          </span>
                        </td>
                      </tr>
                    ))
                  )}
                </tbody>
              </table>
            </div>
          </div>
        )}

        {activeReport === 'time_analytics' && (
          <div className="card">
            <div className="card-header">
              <h3 className="text-lg font-medium text-gray-900">
                {t('reports.time_analytics')}
              </h3>
            </div>
            <div className="card-body">
              <ResponsiveContainer width="100%" height={400}>
                <BarChart data={displayTimelineData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis
                    dataKey="period"
                    tick={{ fontSize: 12 }}
                    tickFormatter={(value) => new Date(value).toLocaleDateString(isRTL ? 'ar-SA' : 'en-US', { month: 'short', day: 'numeric' })}
                  />
                  <YAxis tick={{ fontSize: 12 }} />
                  <Tooltip
                    labelFormatter={(value) => new Date(value).toLocaleDateString(isRTL ? 'ar-SA' : 'en-US')}
                    formatter={(value, name) => [
                      value,
                      name === 'sent' ? (isRTL ? 'مرسل' : 'Sent') :
                      name === 'opened' ? (isRTL ? 'مفتوح' : 'Opened') :
                      name === 'clicked' ? (isRTL ? 'منقور' : 'Clicked') :
                      name === 'bounced' ? (isRTL ? 'مرتد' : 'Bounced') : name
                    ]}
                  />
                  <Bar dataKey="sent" fill="#3b82f6" />
                  <Bar dataKey="opened" fill="#10b981" />
                  <Bar dataKey="clicked" fill="#f59e0b" />
                  <Bar dataKey="bounced" fill="#ef4444" />
                </BarChart>
              </ResponsiveContainer>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default Reports;