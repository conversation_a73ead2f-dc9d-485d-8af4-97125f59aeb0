const express = require('express');
const router = express.Router();
const emailService = require('../utils/emailService');
const { auth } = require('../middleware/auth');

// Get supported email providers
router.get('/providers', auth, async (req, res) => {
  try {
    const providers = emailService.getSupportedProviders();
    
    res.json({
      success: true,
      data: {
        providers,
        current: emailService.getStatus()
      },
      message: 'Email providers retrieved successfully',
      message_ar: 'تم استرداد مزودي البريد الإلكتروني بنجاح'
    });
  } catch (error) {
    console.error('Error getting email providers:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get email providers',
      error_ar: 'فشل في الحصول على مزودي البريد الإلكتروني'
    });
  }
});

// Auto-detect email provider
router.post('/detect', auth, async (req, res) => {
  try {
    const { email } = req.body;

    if (!email) {
      return res.status(400).json({
        success: false,
        error: 'Email address is required',
        error_ar: 'عنوان البريد الإلكتروني مطلوب'
      });
    }

    const provider = emailService.detectEmailProvider(email);
    
    if (!provider) {
      return res.json({
        success: true,
        data: {
          detected: false,
          message: 'Provider not auto-detected, manual configuration required',
          message_ar: 'لم يتم اكتشاف المزود تلقائياً، يتطلب تكوين يدوي',
          suggestions: {
            host: `smtp.${email.split('@')[1]}`,
            port: 587,
            secure: false
          }
        }
      });
    }

    res.json({
      success: true,
      data: {
        detected: true,
        provider: provider.name,
        key: provider.key,
        configuration: {
          host: provider.host,
          port: provider.port,
          secure: provider.secure
        },
        requiresAppPassword: provider.requiresAppPassword,
        setupInstructions: emailService.getSetupInstructions(provider.key),
        message: `Detected ${provider.name}`,
        message_ar: `تم اكتشاف ${provider.name}`
      }
    });
  } catch (error) {
    console.error('Error detecting email provider:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to detect email provider',
      error_ar: 'فشل في اكتشاف مزود البريد الإلكتروني'
    });
  }
});

// Test email configuration
router.post('/test', auth, async (req, res) => {
  try {
    const { email, password, provider } = req.body;

    if (!email || !password) {
      return res.status(400).json({
        success: false,
        error: 'Email and password are required',
        error_ar: 'البريد الإلكتروني وكلمة المرور مطلوبان'
      });
    }

    const result = await emailService.testProviderConfiguration(email, password, provider);
    
    if (result.success) {
      res.json({
        success: true,
        data: result,
        message: 'Email configuration test successful',
        message_ar: 'اختبار تكوين البريد الإلكتروني نجح'
      });
    } else {
      res.status(400).json({
        success: false,
        error: result.error,
        error_ar: 'فشل اختبار التكوين',
        data: {
          provider: result.provider,
          suggestions: result.suggestions
        }
      });
    }
  } catch (error) {
    console.error('Error testing email configuration:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to test email configuration',
      error_ar: 'فشل في اختبار تكوين البريد الإلكتروني'
    });
  }
});

// Send test email
router.post('/test-send', auth, async (req, res) => {
  try {
    const { testEmail, subject, message } = req.body;

    if (!testEmail) {
      return res.status(400).json({
        success: false,
        error: 'Test email address is required',
        error_ar: 'عنوان البريد الإلكتروني للاختبار مطلوب'
      });
    }

    if (!emailService.isConfigured) {
      return res.status(400).json({
        success: false,
        error: 'Email service not configured',
        error_ar: 'خدمة البريد الإلكتروني غير مكونة'
      });
    }

    const testSubject = subject || 'Test Email - اختبار البريد الإلكتروني';
    const testMessage = message || `
      <h2>Test Email / رسالة اختبار</h2>
      <p>This is a test email from the Email Marketing System.</p>
      <p>هذه رسالة اختبار من نظام التسويق الإلكتروني.</p>
      <p>If you received this email, your configuration is working correctly!</p>
      <p>إذا وصلتك هذه الرسالة، فإن التكوين يعمل بشكل صحيح!</p>
      <hr>
      <small>Sent at: ${new Date().toLocaleString()}</small>
    `;

    const result = await emailService.sendEmail(
      testEmail,
      testSubject,
      emailService.generateEmailTemplate(testMessage, 'ar')
    );

    res.json({
      success: true,
      data: result,
      message: 'Test email sent successfully',
      message_ar: 'تم إرسال رسالة الاختبار بنجاح'
    });
  } catch (error) {
    console.error('Error sending test email:', error);
    res.status(500).json({
      success: false,
      error: error.message,
      error_ar: 'فشل في إرسال رسالة الاختبار'
    });
  }
});

// Get current email service status
router.get('/status', auth, async (req, res) => {
  try {
    const status = emailService.getStatus();
    
    res.json({
      success: true,
      data: status,
      message: 'Email service status retrieved',
      message_ar: 'تم استرداد حالة خدمة البريد الإلكتروني'
    });
  } catch (error) {
    console.error('Error getting email service status:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get email service status',
      error_ar: 'فشل في الحصول على حالة خدمة البريد الإلكتروني'
    });
  }
});

// Get setup guide for specific provider
router.get('/guide/:provider', auth, async (req, res) => {
  try {
    const { provider } = req.params;
    const { language = 'ar' } = req.query;
    
    const instructions = emailService.getSetupInstructions(provider);
    const providerInfo = emailService.getSupportedProviders().find(p => p.key === provider);
    
    if (!providerInfo) {
      return res.status(404).json({
        success: false,
        error: 'Provider not found',
        error_ar: 'المزود غير موجود'
      });
    }

    res.json({
      success: true,
      data: {
        provider: providerInfo,
        instructions: instructions[language] || instructions.en,
        language
      },
      message: 'Setup guide retrieved',
      message_ar: 'تم استرداد دليل الإعداد'
    });
  } catch (error) {
    console.error('Error getting setup guide:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get setup guide',
      error_ar: 'فشل في الحصول على دليل الإعداد'
    });
  }
});

// Advanced cPanel diagnosis with detailed error reporting
router.post('/diagnose-cpanel', auth, async (req, res) => {
  try {
    console.log('📥 Received cPanel diagnosis request');
    console.log('Request body:', { ...req.body, password: '***' });

    const { domain, email, password } = req.body;

    if (!domain || !email || !password) {
      console.log('❌ Missing required fields:', { domain: !!domain, email: !!email, password: !!password });
      return res.status(400).json({
        success: false,
        error: 'Domain, email, and password are required',
        error_ar: 'النطاق والبريد الإلكتروني وكلمة المرور مطلوبة'
      });
    }

    console.log(`🔍 Starting advanced cPanel diagnosis for: ${email} @ ${domain}`);

    const testConfigurations = [
      // Test the exact configuration provided by user first
      { host: domain, port: 465, secure: true, name: `${domain}:465 (SSL) - User Config` },
      { host: domain, port: 587, secure: false, name: `${domain}:587 (TLS) - User Config` },

      // Standard cPanel configurations
      { host: `mail.${domain}`, port: 465, secure: true, name: 'mail.domain.com:465 (SSL)' },
      { host: `mail.${domain}`, port: 587, secure: false, name: 'mail.domain.com:587 (TLS)' },
      { host: `mail.${domain}`, port: 25, secure: false, name: 'mail.domain.com:25 (Plain)' },
      { host: `server.${domain}`, port: 465, secure: true, name: 'server.domain.com:465 (SSL)' },
      { host: `server.${domain}`, port: 587, secure: false, name: 'server.domain.com:587 (TLS)' },
      { host: `smtp.${domain}`, port: 465, secure: true, name: 'smtp.domain.com:465 (SSL)' },
      { host: `smtp.${domain}`, port: 587, secure: false, name: 'smtp.domain.com:587 (TLS)' }
    ];

    const results = [];
    let successfulConfig = null;

    for (const config of testConfigurations) {
      const startTime = Date.now();
      let detailedError = {};

      try {
        console.log(`🧪 Testing: ${config.name}`);

        const testConfig = {
          host: config.host,
          port: config.port,
          secure: config.secure,
          auth: {
            user: email,
            pass: password
          },
          tls: {
            rejectUnauthorized: false,
            ciphers: 'SSLv3',
            secureProtocol: 'TLSv1_method'
          },
          connectionTimeout: 15000,
          greetingTimeout: 10000,
          socketTimeout: 15000,
          logger: false, // Disable nodemailer logging to avoid clutter
          debug: false
        };

        const transporter = nodemailer.createTransporter(testConfig);

        // Test connection
        await transporter.verify();

        const responseTime = Date.now() - startTime;

        results.push({
          config: config.name,
          status: 'success',
          message: 'Connection successful',
          message_ar: 'الاتصال ناجح',
          settings: config,
          responseTime: `${responseTime}ms`,
          details: {
            host: config.host,
            port: config.port,
            secure: config.secure,
            responseTime
          }
        });

        if (!successfulConfig) {
          successfulConfig = results[results.length - 1];
        }

        console.log(`✅ ${config.name} - Success (${responseTime}ms)`);

      } catch (error) {
        const responseTime = Date.now() - startTime;

        // Analyze error in detail
        detailedError = analyzeEmailError(error, config);

        results.push({
          config: config.name,
          status: 'failed',
          message: error.message,
          settings: config,
          responseTime: `${responseTime}ms`,
          errorCode: error.code,
          errorType: detailedError.type,
          suggestion: detailedError.suggestion,
          suggestion_ar: detailedError.suggestion_ar,
          details: {
            host: config.host,
            port: config.port,
            secure: config.secure,
            responseTime,
            errno: error.errno,
            syscall: error.syscall,
            address: error.address,
            ...detailedError
          }
        });

        console.log(`❌ ${config.name} - Failed (${responseTime}ms): ${error.code || error.message}`);
      }
    }

    // Additional network diagnostics
    const networkDiagnostics = await performNetworkDiagnostics(domain);

    res.json({
      success: true,
      data: {
        domain,
        email,
        results,
        recommendation: successfulConfig,
        networkDiagnostics,
        summary: generateDiagnosisSummary(results, networkDiagnostics),
        message: successfulConfig
          ? 'Found working configuration'
          : 'No working configuration found - see detailed analysis',
        message_ar: successfulConfig
          ? 'تم العثور على إعدادات تعمل'
          : 'لم يتم العثور على إعدادات تعمل - راجع التحليل المفصل'
      }
    });

  } catch (error) {
    console.error('cPanel diagnosis error:', error);
    res.status(500).json({
      success: false,
      error: 'Diagnosis failed',
      error_ar: 'فشل التشخيص',
      details: error.message
    });
  }
});

// Helper function to analyze email errors
function analyzeEmailError(error, config) {
  const errorCode = error.code;
  const errorMessage = error.message.toLowerCase();

  let type = 'unknown';
  let suggestion = 'Check with hosting provider';
  let suggestion_ar = 'تحقق مع مزود الاستضافة';
  let severity = 'medium';

  if (errorCode === 'ECONNREFUSED' || errorMessage.includes('connection refused')) {
    type = 'connection_refused';
    severity = 'high';
    suggestion = `Port ${config.port} is blocked or service not running. Try port ${config.port === 587 ? '465 with SSL' : '587 with TLS'}`;
    suggestion_ar = `المنفذ ${config.port} محجوب أو الخدمة لا تعمل. جرب المنفذ ${config.port === 587 ? '465 مع SSL' : '587 مع TLS'}`;
  } else if (errorCode === 'ENOTFOUND' || errorMessage.includes('not found')) {
    type = 'host_not_found';
    severity = 'high';
    suggestion = `Server ${config.host} does not exist. Check domain spelling or try different server names`;
    suggestion_ar = `الخادم ${config.host} غير موجود. تحقق من إملاء النطاق أو جرب أسماء خوادم أخرى`;
  } else if (errorCode === 'ETIMEDOUT' || errorMessage.includes('timeout')) {
    type = 'timeout';
    severity = 'medium';
    suggestion = 'Connection timeout. Server may be slow or firewall blocking connection';
    suggestion_ar = 'انتهت مهلة الاتصال. قد يكون الخادم بطيء أو جدار الحماية يحجب الاتصال';
  } else if (errorMessage.includes('authentication') || errorMessage.includes('login') || errorMessage.includes('535')) {
    type = 'authentication_failed';
    severity = 'high';
    suggestion = 'Username or password incorrect. Verify credentials in cPanel';
    suggestion_ar = 'اسم المستخدم أو كلمة المرور خاطئة. تحقق من البيانات في cPanel';
  } else if (errorMessage.includes('certificate') || errorMessage.includes('ssl') || errorMessage.includes('tls')) {
    type = 'ssl_certificate_error';
    severity = 'medium';
    suggestion = 'SSL/TLS certificate issue. Try disabling SSL or using different port';
    suggestion_ar = 'مشكلة في شهادة SSL/TLS. جرب إلغاء SSL أو استخدام منفذ مختلف';
  } else if (errorMessage.includes('relay') || errorMessage.includes('550')) {
    type = 'relay_denied';
    severity = 'medium';
    suggestion = 'SMTP relay denied. Check if SMTP is enabled for this account';
    suggestion_ar = 'تم رفض ترحيل SMTP. تحقق من تفعيل SMTP لهذا الحساب';
  }

  return {
    type,
    suggestion,
    suggestion_ar,
    severity,
    errorCode,
    timestamp: new Date().toISOString()
  };
}

// Helper function for network diagnostics
async function performNetworkDiagnostics(domain) {
  const dns = require('dns').promises;
  const net = require('net');

  const diagnostics = {
    domain,
    timestamp: new Date().toISOString(),
    dns: {},
    ports: {}
  };

  try {
    // DNS lookups
    try {
      const mxRecords = await dns.resolveMx(domain);
      diagnostics.dns.mx = mxRecords;
      diagnostics.dns.hasMX = mxRecords.length > 0;
    } catch (error) {
      diagnostics.dns.mx = [];
      diagnostics.dns.hasMX = false;
      diagnostics.dns.mxError = error.message;
    }

    try {
      const aRecords = await dns.resolve4(`mail.${domain}`);
      diagnostics.dns.mailSubdomain = aRecords;
    } catch (error) {
      diagnostics.dns.mailSubdomainError = error.message;
    }

    // Port connectivity tests
    const portsToTest = [25, 587, 465, 993, 995];
    const hosts = [`mail.${domain}`, `server.${domain}`, domain];

    for (const host of hosts) {
      diagnostics.ports[host] = {};

      for (const port of portsToTest) {
        try {
          const isOpen = await testPortConnectivity(host, port, 5000);
          diagnostics.ports[host][port] = isOpen ? 'open' : 'closed';
        } catch (error) {
          diagnostics.ports[host][port] = 'error';
        }
      }
    }

  } catch (error) {
    diagnostics.error = error.message;
  }

  return diagnostics;
}

// Helper function to test port connectivity
function testPortConnectivity(host, port, timeout = 5000) {
  return new Promise((resolve) => {
    const socket = new (require('net')).Socket();

    socket.setTimeout(timeout);

    socket.on('connect', () => {
      socket.destroy();
      resolve(true);
    });

    socket.on('timeout', () => {
      socket.destroy();
      resolve(false);
    });

    socket.on('error', () => {
      socket.destroy();
      resolve(false);
    });

    socket.connect(port, host);
  });
}

// Helper function to generate diagnosis summary
function generateDiagnosisSummary(results, networkDiagnostics) {
  const summary = {
    totalTests: results.length,
    successful: results.filter(r => r.status === 'success').length,
    failed: results.filter(r => r.status === 'failed').length,
    commonErrors: {},
    recommendations: []
  };

  // Analyze common error patterns
  const errorTypes = {};
  results.filter(r => r.status === 'failed').forEach(result => {
    const errorType = result.errorType || 'unknown';
    errorTypes[errorType] = (errorTypes[errorType] || 0) + 1;
  });

  summary.commonErrors = errorTypes;

  // Generate recommendations
  if (summary.successful === 0) {
    if (errorTypes.connection_refused > 0) {
      summary.recommendations.push({
        type: 'port_issue',
        message: 'All ports seem to be blocked. Contact hosting provider to enable SMTP',
        message_ar: 'جميع المنافذ محجوبة. اتصل بمزود الاستضافة لتفعيل SMTP'
      });
    }

    if (errorTypes.host_not_found > 0) {
      summary.recommendations.push({
        type: 'dns_issue',
        message: 'Mail servers not found. Check domain configuration',
        message_ar: 'خوادم البريد غير موجودة. تحقق من إعدادات النطاق'
      });
    }

    if (errorTypes.authentication_failed > 0) {
      summary.recommendations.push({
        type: 'auth_issue',
        message: 'Authentication failed. Verify email and password in cPanel',
        message_ar: 'فشل المصادقة. تحقق من البريد الإلكتروني وكلمة المرور في cPanel'
      });
    }
  }

  return summary;
}

// Direct test endpoint for specific configuration
router.post('/test-direct', auth, async (req, res) => {
  try {
    const { host, port, email, password, secure } = req.body;

    console.log('🧪 Direct test with configuration:', {
      host,
      port,
      email,
      secure,
      passwordLength: password?.length
    });

    const testConfig = {
      host: host,
      port: parseInt(port),
      secure: secure === true || secure === 'true',
      auth: {
        user: email,
        pass: password
      },
      tls: {
        rejectUnauthorized: false,
        ciphers: 'SSLv3',
        secureProtocol: 'TLSv1_method'
      },
      connectionTimeout: 30000,
      greetingTimeout: 15000,
      socketTimeout: 30000
    };

    console.log('📧 Testing configuration:', {
      ...testConfig,
      auth: { user: testConfig.auth.user, pass: '***' }
    });

    const transporter = nodemailer.createTransporter(testConfig);

    const startTime = Date.now();
    await transporter.verify();
    const responseTime = Date.now() - startTime;

    console.log(`✅ Direct test successful in ${responseTime}ms`);

    res.json({
      success: true,
      data: {
        message: 'Connection successful',
        message_ar: 'الاتصال ناجح',
        responseTime: `${responseTime}ms`,
        config: {
          host,
          port: parseInt(port),
          secure: testConfig.secure
        }
      }
    });

  } catch (error) {
    console.error('❌ Direct test failed:', error.message);

    res.json({
      success: false,
      error: error.message,
      errorCode: error.code,
      details: {
        errno: error.errno,
        syscall: error.syscall,
        address: error.address,
        port: error.port
      }
    });
  }
});

module.exports = router;
