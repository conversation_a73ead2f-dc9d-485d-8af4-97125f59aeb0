const express = require('express');
const router = express.Router();
const UserSmtpSettings = require('../models/UserSmtpSettings');
const nodemailer = require('nodemailer');

// Get user SMTP settings
router.get('/', async (req, res) => {
  try {
    const userId = req.user?.id || 1; // Default to user 1 for now
    const settings = await UserSmtpSettings.getByUserId(userId);
    
    if (settings) {
      // Don't send password in response
      const { smtp_password, ...safeSettings } = settings;
      res.json({
        success: true,
        data: safeSettings,
        message: 'SMTP settings retrieved successfully',
        message_ar: 'تم استرجاع إعدادات SMTP بنجاح'
      });
    } else {
      res.json({
        success: true,
        data: null,
        message: 'No SMTP settings found',
        message_ar: 'لم يتم العثور على إعدادات SMTP'
      });
    }
  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'Failed to get SMTP settings',
      message_ar: 'فشل في استرجاع إعدادات SMTP'
    });
  }
});

// Save or update user SMTP settings
router.post('/', async (req, res) => {
  try {
    const userId = req.user?.id || 1; // Default to user 1 for now
    const smtpData = req.body;

    // Validate required fields
    const requiredFields = ['smtp_host', 'smtp_port', 'smtp_username', 'smtp_password', 'from_email', 'from_name'];
    for (const field of requiredFields) {
      if (!smtpData[field]) {
        return res.status(400).json({
          success: false,
          error: `${field} is required`,
          message_ar: `${field} مطلوب`
        });
      }
    }

    // Check if settings already exist
    const existingSettings = await UserSmtpSettings.getByUserId(userId);
    
    let result;
    if (existingSettings) {
      result = await UserSmtpSettings.update(userId, smtpData);
    } else {
      result = await UserSmtpSettings.create(userId, smtpData);
    }

    res.json({
      success: true,
      data: result,
      message: 'SMTP settings saved successfully',
      message_ar: 'تم حفظ إعدادات SMTP بنجاح'
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'Failed to save SMTP settings',
      message_ar: 'فشل في حفظ إعدادات SMTP'
    });
  }
});

// Test SMTP settings
router.post('/test', async (req, res) => {
  try {
    const userId = req.user?.id || 1; // Default to user 1 for now
    const { test_email } = req.body;

    if (!test_email) {
      return res.status(400).json({
        success: false,
        error: 'Test email is required',
        message_ar: 'عنوان البريد الإلكتروني للاختبار مطلوب'
      });
    }

    // Get user SMTP settings
    const settings = await UserSmtpSettings.getByUserId(userId);
    
    if (!settings) {
      return res.status(400).json({
        success: false,
        error: 'No SMTP settings found',
        message_ar: 'لم يتم العثور على إعدادات SMTP'
      });
    }

    // Create transporter
    const transporter = nodemailer.createTransporter({
      host: settings.smtp_host,
      port: settings.smtp_port,
      secure: settings.smtp_secure,
      auth: {
        user: settings.smtp_username,
        pass: settings.smtp_password
      }
    });

    // Send test email
    await transporter.sendMail({
      from: `${settings.from_name} <${settings.from_email}>`,
      to: test_email,
      subject: 'SMTP Test Email',
      html: `
        <h2>SMTP Configuration Test</h2>
        <p>This is a test email to verify your SMTP configuration.</p>
        <p>If you received this email, your SMTP settings are working correctly.</p>
        <hr>
        <p><small>Sent from Email Marketing System</small></p>
      `
    });

    res.json({
      success: true,
      message: 'Test email sent successfully',
      message_ar: 'تم إرسال رسالة الاختبار بنجاح'
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'Failed to send test email: ' + error.message,
      message_ar: 'فشل في إرسال رسالة الاختبار: ' + error.message
    });
  }
});

// Delete user SMTP settings
router.delete('/', async (req, res) => {
  try {
    const userId = req.user?.id || 1; // Default to user 1 for now
    
    const result = await UserSmtpSettings.delete(userId);
    
    res.json({
      success: true,
      data: result,
      message: 'SMTP settings deleted successfully',
      message_ar: 'تم حذف إعدادات SMTP بنجاح'
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'Failed to delete SMTP settings',
      message_ar: 'فشل في حذف إعدادات SMTP'
    });
  }
});

module.exports = router;
