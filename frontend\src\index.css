@tailwind base;
@tailwind components;
@tailwind utilities;

/* CSS Variables for dynamic theming */
:root {
  --font-family: 'Tahoma', 'Arial', sans-serif;
  --primary-color: #3b82f6;
  --secondary-color: #64748b;
  --success-color: #10b981;
  --warning-color: #f59e0b;
  --error-color: #ef4444;
  --background-color: #f8fafc;
  --surface-color: #ffffff;
  --text-primary: #1e293b;
  --text-secondary: #64748b;
  --border-color: #e2e8f0;
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
}

/* Base styles */
* {
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  font-family: var(--font-family);
  background-color: var(--background-color);
  color: var(--text-primary);
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* RTL/LTR specific styles */
html[dir="rtl"] {
  --font-family: 'Tahoma', 'Arial', sans-serif;
}

html[dir="ltr"] {
  --font-family: 'Inter', system-ui, sans-serif;
}

/* RTL specific adjustments */
.rtl {
  direction: rtl;
  text-align: right;
}

.ltr {
  direction: ltr;
  text-align: left;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Focus styles */
.focus-ring {
  @apply focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2;
}

/* Button styles */
.btn {
  @apply inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm transition-colors duration-200 focus-ring disabled:opacity-50 disabled:cursor-not-allowed;
}

.btn-primary {
  @apply bg-primary-600 text-white hover:bg-primary-700 active:bg-primary-800;
}

.btn-secondary {
  @apply bg-secondary-600 text-white hover:bg-secondary-700 active:bg-secondary-800;
}

.btn-success {
  @apply bg-green-600 text-white hover:bg-green-700 active:bg-green-800;
}

.btn-warning {
  @apply bg-yellow-600 text-white hover:bg-yellow-700 active:bg-yellow-800;
}

.btn-danger {
  @apply bg-red-600 text-white hover:bg-red-700 active:bg-red-800;
}

.btn-outline {
  @apply bg-transparent border-gray-300 text-gray-700 hover:bg-gray-50 active:bg-gray-100;
}

.btn-ghost {
  @apply bg-transparent border-transparent text-gray-700 hover:bg-gray-100 active:bg-gray-200;
}

/* Input styles */
.input {
  @apply block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus-ring focus:border-primary-500 sm:text-sm;
}

.input-error {
  @apply border-red-300 text-red-900 placeholder-red-300 focus:border-red-500 focus:ring-red-500;
}

/* Input with better icon spacing */
.input-with-left-icon {
  @apply pl-12 pr-4;
}

.input-with-right-icon {
  @apply pr-12 pl-4;
}

.input-with-both-icons {
  @apply px-12;
}

/* Card styles */
.card {
  @apply bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden;
}

.card-header {
  @apply px-6 py-4 border-b border-gray-200 bg-gray-50;
}

.card-body {
  @apply px-6 py-4;
}

.card-footer {
  @apply px-6 py-4 border-t border-gray-200 bg-gray-50;
}

/* Table styles */
.table {
  @apply min-w-full divide-y divide-gray-200;
}

.table-header {
  @apply bg-gray-50;
}

.table-header th {
  @apply px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider;
}

.table-body {
  @apply bg-white divide-y divide-gray-200;
}

.table-body td {
  @apply px-6 py-4 whitespace-nowrap text-sm text-gray-900;
}

.table-row-hover {
  @apply hover:bg-gray-50 transition-colors duration-150;
}

/* Badge styles */
.badge {
  @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
}

.badge-primary {
  @apply bg-primary-100 text-primary-800;
}

.badge-secondary {
  @apply bg-gray-100 text-gray-800;
}

.badge-success {
  @apply bg-green-100 text-green-800;
}

.badge-warning {
  @apply bg-yellow-100 text-yellow-800;
}

.badge-danger {
  @apply bg-red-100 text-red-800;
}

/* Alert styles */
.alert {
  @apply p-4 rounded-md border;
}

.alert-success {
  @apply bg-green-50 border-green-200 text-green-800;
}

.alert-warning {
  @apply bg-yellow-50 border-yellow-200 text-yellow-800;
}

.alert-error {
  @apply bg-red-50 border-red-200 text-red-800;
}

.alert-info {
  @apply bg-blue-50 border-blue-200 text-blue-800;
}

/* Loading spinner */
.spinner {
  @apply animate-spin rounded-full border-2 border-gray-300 border-t-primary-600;
}

/* Fade in animation */
.fade-in {
  @apply animate-fade-in;
}

/* Slide in animation */
.slide-in {
  @apply animate-slide-in;
}

/* Responsive text alignment for RTL/LTR */
.text-start {
  text-align: start;
}

.text-end {
  text-align: end;
}

/* RTL-specific margin and padding utilities */
.rtl .ms-auto {
  margin-right: auto;
}

.rtl .me-auto {
  margin-left: auto;
}

.ltr .ms-auto {
  margin-left: auto;
}

.ltr .me-auto {
  margin-right: auto;
}

/* Custom utilities for better RTL support */
.border-start {
  border-inline-start-width: 1px;
}

.border-end {
  border-inline-end-width: 1px;
}

.ps-4 {
  padding-inline-start: 1rem;
}

.pe-4 {
  padding-inline-end: 1rem;
}

.ms-4 {
  margin-inline-start: 1rem;
}

.me-4 {
  margin-inline-end: 1rem;
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }
  
  .print-break {
    page-break-before: always;
  }
  
  .print-avoid-break {
    page-break-inside: avoid;
  }
}

/* Dark mode support (future enhancement) */
@media (prefers-color-scheme: dark) {
  /* Dark mode styles can be added here */
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  :root {
    --border-color: #000000;
    --text-secondary: #000000;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}
