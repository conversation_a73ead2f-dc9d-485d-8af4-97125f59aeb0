import React, { createContext, useContext, useReducer, useEffect } from 'react';
import { useTranslation } from 'react-i18next';

// Initial state
const initialState = {
  theme: localStorage.getItem('theme') || 'light',
  language: localStorage.getItem('i18nextLng') || 'ar',
  direction: localStorage.getItem('i18nextLng') === 'en' ? 'ltr' : 'rtl',
  fontSize: localStorage.getItem('fontSize') || 'medium',
  primaryColor: localStorage.getItem('primaryColor') || 'blue',
  borderRadius: localStorage.getItem('borderRadius') || 'md',
  sidebarCollapsed: localStorage.getItem('sidebarCollapsed') === 'true',
  preferences: {
    animations: localStorage.getItem('animations') !== 'false',
    sounds: localStorage.getItem('sounds') !== 'false',
    notifications: localStorage.getItem('notifications') !== 'false',
    autoSave: localStorage.getItem('autoSave') !== 'false'
  }
};

// Action types
const THEME_ACTIONS = {
  SET_THEME: 'SET_THEME',
  SET_LANGUAGE: 'SET_LANGUAGE',
  SET_FONT_SIZE: 'SET_FONT_SIZE',
  SET_PRIMARY_COLOR: 'SET_PRIMARY_COLOR',
  SET_BORDER_RADIUS: 'SET_BORDER_RADIUS',
  TOGGLE_SIDEBAR: 'TOGGLE_SIDEBAR',
  SET_SIDEBAR_COLLAPSED: 'SET_SIDEBAR_COLLAPSED',
  UPDATE_PREFERENCES: 'UPDATE_PREFERENCES',
  RESET_PREFERENCES: 'RESET_PREFERENCES'
};

// Reducer
const themeReducer = (state, action) => {
  switch (action.type) {
    case THEME_ACTIONS.SET_THEME:
      return {
        ...state,
        theme: action.payload
      };

    case THEME_ACTIONS.SET_LANGUAGE:
      return {
        ...state,
        language: action.payload,
        direction: action.payload === 'ar' ? 'rtl' : 'ltr'
      };

    case THEME_ACTIONS.SET_FONT_SIZE:
      return {
        ...state,
        fontSize: action.payload
      };

    case THEME_ACTIONS.SET_PRIMARY_COLOR:
      return {
        ...state,
        primaryColor: action.payload
      };

    case THEME_ACTIONS.SET_BORDER_RADIUS:
      return {
        ...state,
        borderRadius: action.payload
      };

    case THEME_ACTIONS.TOGGLE_SIDEBAR:
      return {
        ...state,
        sidebarCollapsed: !state.sidebarCollapsed
      };

    case THEME_ACTIONS.SET_SIDEBAR_COLLAPSED:
      return {
        ...state,
        sidebarCollapsed: action.payload
      };

    case THEME_ACTIONS.UPDATE_PREFERENCES:
      return {
        ...state,
        preferences: {
          ...state.preferences,
          ...action.payload
        }
      };

    case THEME_ACTIONS.RESET_PREFERENCES:
      return {
        ...initialState,
        language: state.language,
        direction: state.direction
      };

    default:
      return state;
  }
};

// Create context
const ThemeContext = createContext();

// Theme provider component
export const ThemeProvider = ({ children }) => {
  const [state, dispatch] = useReducer(themeReducer, initialState);
  const { i18n } = useTranslation();

  // Apply theme changes to document
  useEffect(() => {
    const root = document.documentElement;

    // Apply theme class to both html and body
    root.className = root.className
      .replace(/\btheme-\w+\b/g, '')
      .trim();
    root.classList.add(`theme-${state.theme}`);

    document.body.className = document.body.className
      .replace(/\btheme-\w+\b/g, '')
      .trim();
    document.body.classList.add(`theme-${state.theme}`);

    // Apply font size class
    document.body.className = document.body.className
      .replace(/\bfont-size-\w+\b/g, '')
      .trim();
    document.body.classList.add(`font-size-${state.fontSize}`);

    // Apply direction and language classes
    document.body.className = document.body.className
      .replace(/\b(rtl|ltr)\b/g, '')
      .trim();
    document.body.classList.add(state.direction);

    // Apply primary color as CSS custom property
    const colorMap = {
      blue: '#3B82F6',
      green: '#10B981',
      purple: '#8B5CF6',
      red: '#EF4444',
      orange: '#F97316',
      pink: '#EC4899',
      indigo: '#6366F1',
      teal: '#14B8A6',
      cyan: '#06B6D4',
      emerald: '#059669',
      lime: '#84CC16',
      amber: '#F59E0B'
    };

    root.style.setProperty('--primary-color', colorMap[state.primaryColor] || colorMap.blue);

    // Apply border radius
    const radiusMap = {
      none: '0px',
      sm: '0.125rem',
      md: '0.375rem',
      lg: '0.5rem'
    };

    root.style.setProperty('--border-radius', radiusMap[state.borderRadius] || radiusMap.md);

    // Store preferences in localStorage
    localStorage.setItem('theme', state.theme);
    localStorage.setItem('fontSize', state.fontSize);
    localStorage.setItem('primaryColor', state.primaryColor);
    localStorage.setItem('borderRadius', state.borderRadius);
    localStorage.setItem('sidebarCollapsed', state.sidebarCollapsed.toString());
    
    Object.entries(state.preferences).forEach(([key, value]) => {
      localStorage.setItem(key, value.toString());
    });
  }, [state]);

  // Listen for language changes from i18n
  useEffect(() => {
    const handleLanguageChange = (event) => {
      const { language } = event.detail;
      dispatch({
        type: THEME_ACTIONS.SET_LANGUAGE,
        payload: language
      });
    };

    window.addEventListener('languageChanged', handleLanguageChange);
    
    return () => {
      window.removeEventListener('languageChanged', handleLanguageChange);
    };
  }, []);

  // Set theme
  const setTheme = (theme) => {
    dispatch({
      type: THEME_ACTIONS.SET_THEME,
      payload: theme
    });
  };

  // Set language
  const setLanguage = (language) => {
    i18n.changeLanguage(language);
    dispatch({
      type: THEME_ACTIONS.SET_LANGUAGE,
      payload: language
    });
  };

  // Set font size
  const setFontSize = (fontSize) => {
    dispatch({
      type: THEME_ACTIONS.SET_FONT_SIZE,
      payload: fontSize
    });
  };

  // Set primary color
  const setPrimaryColor = (color) => {
    dispatch({
      type: THEME_ACTIONS.SET_PRIMARY_COLOR,
      payload: color
    });
  };

  // Set border radius
  const setBorderRadius = (radius) => {
    dispatch({
      type: THEME_ACTIONS.SET_BORDER_RADIUS,
      payload: radius
    });
  };

  // Toggle sidebar
  const toggleSidebar = () => {
    dispatch({ type: THEME_ACTIONS.TOGGLE_SIDEBAR });
  };

  // Set sidebar collapsed state
  const setSidebarCollapsed = (collapsed) => {
    dispatch({
      type: THEME_ACTIONS.SET_SIDEBAR_COLLAPSED,
      payload: collapsed
    });
  };

  // Update preferences
  const updatePreferences = (preferences) => {
    dispatch({
      type: THEME_ACTIONS.UPDATE_PREFERENCES,
      payload: preferences
    });
  };

  // Reset all preferences
  const resetPreferences = () => {
    dispatch({ type: THEME_ACTIONS.RESET_PREFERENCES });
  };

  // Get CSS classes for current theme
  const getThemeClasses = () => {
    return {
      theme: `theme-${state.theme}`,
      direction: state.direction,
      fontSize: `font-size-${state.fontSize}`,
      sidebar: state.sidebarCollapsed ? 'sidebar-collapsed' : 'sidebar-expanded'
    };
  };

  // Check if dark mode is active
  const isDarkMode = () => {
    return state.theme === 'dark';
  };

  // Check if RTL is active
  const isRTL = () => {
    return state.direction === 'rtl';
  };

  // Get available themes
  const getAvailableThemes = () => {
    return [
      { value: 'light', label: 'Light', label_ar: 'فاتح' },
      { value: 'dark', label: 'Dark', label_ar: 'داكن' },
      { value: 'auto', label: 'Auto', label_ar: 'تلقائي' }
    ];
  };

  // Get available font sizes
  const getAvailableFontSizes = () => {
    return [
      { value: 'small', label: 'Small', label_ar: 'صغير' },
      { value: 'medium', label: 'Medium', label_ar: 'متوسط' },
      { value: 'large', label: 'Large', label_ar: 'كبير' },
      { value: 'extra-large', label: 'Extra Large', label_ar: 'كبير جداً' }
    ];
  };

  // Get available languages
  const getAvailableLanguages = () => {
    return [
      { value: 'ar', label: 'العربية', flag: '🇸🇦' },
      { value: 'en', label: 'English', flag: '🇺🇸' }
    ];
  };

  // Context value
  const value = {
    // State
    theme: state.theme,
    language: state.language,
    direction: state.direction,
    fontSize: state.fontSize,
    primaryColor: state.primaryColor,
    borderRadius: state.borderRadius,
    sidebarCollapsed: state.sidebarCollapsed,
    preferences: state.preferences,

    // Actions
    setTheme,
    setLanguage,
    setFontSize,
    setPrimaryColor,
    setBorderRadius,
    toggleSidebar,
    setSidebarCollapsed,
    updatePreferences,
    resetPreferences,

    // Utilities
    getThemeClasses,
    isDarkMode,
    isRTL,
    getAvailableThemes,
    getAvailableFontSizes,
    getAvailableLanguages
  };

  return (
    <ThemeContext.Provider value={value}>
      {children}
    </ThemeContext.Provider>
  );
};

// Custom hook to use theme context
export const useTheme = () => {
  const context = useContext(ThemeContext);
  
  if (!context) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  
  return context;
};

export default ThemeContext;
