const { db } = require('../config/database');

class Message {
  // Get all messages with pagination and filtering
  static getAll(page = 1, limit = 10, filters = {}) {
    return new Promise((resolve, reject) => {
      const offset = (page - 1) * limit;
      let query = 'SELECT * FROM messages WHERE 1=1';
      let countQuery = 'SELECT COUNT(*) as total FROM messages WHERE 1=1';
      const params = [];

      // Apply filters
      if (filters.type) {
        query += ' AND type = ?';
        countQuery += ' AND type = ?';
        params.push(filters.type);
      }

      if (filters.category) {
        query += ' AND category = ?';
        countQuery += ' AND category = ?';
        params.push(filters.category);
      }

      if (filters.language) {
        query += ' AND language = ?';
        countQuery += ' AND language = ?';
        params.push(filters.language);
      }

      if (filters.status) {
        query += ' AND status = ?';
        countQuery += ' AND status = ?';
        params.push(filters.status);
      }

      if (filters.search) {
        query += ' AND (title LIKE ? OR title_ar LIKE ? OR title_en LIKE ?)';
        countQuery += ' AND (title LIKE ? OR title_ar LIKE ? OR title_en LIKE ?)';
        const searchTerm = `%${filters.search}%`;
        params.push(searchTerm, searchTerm, searchTerm);
      }

      query += ' ORDER BY created_at DESC LIMIT ? OFFSET ?';
      params.push(limit, offset);

      // Get total count
      db.get(countQuery, params.slice(0, -2), (err, countResult) => {
        if (err) {
          reject(err);
          return;
        }

        // Get messages
        db.all(query, params, (err, rows) => {
          if (err) {
            reject(err);
          } else {
            resolve({
              messages: rows,
              total: countResult.total,
              page,
              limit,
              totalPages: Math.ceil(countResult.total / limit)
            });
          }
        });
      });
    });
  }

  // Get message by ID
  static getById(id) {
    return new Promise((resolve, reject) => {
      console.log('🔍 Searching for message with ID:', id);
      db.get('SELECT * FROM messages WHERE id = ?', [id], (err, row) => {
        if (err) {
          console.error('❌ Database error in getById:', err);
          reject(err);
        } else {
          if (row) {
            // Parse JSON fields
            if (row.target_clients) {
              try {
                row.target_clients = JSON.parse(row.target_clients);
              } catch (e) {
                console.error('Error parsing target_clients:', e);
                row.target_clients = [];
              }
            } else {
              row.target_clients = [];
            }

            if (row.target_criteria) {
              try {
                row.target_criteria = JSON.parse(row.target_criteria);
              } catch (e) {
                console.error('Error parsing target_criteria:', e);
                row.target_criteria = {};
              }
            }

            console.log(`📧 Message ${id} loaded with ${row.target_clients.length} target clients`);
          } else {
            console.log(`❌ No message found with ID: ${id}`);
          }
          resolve(row);
        }
      });
    });
  }

  // Create new message
  static create(messageData) {
    return new Promise((resolve, reject) => {
      const {
        title, title_ar, title_en, content, content_ar, content_en,
        type, category, language, status, scheduled_at, created_by
      } = messageData;

      const query = `
        INSERT INTO messages (
          title, title_ar, title_en, content, content_ar, content_en,
          type, category, language, status, scheduled_at, created_by
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `;

      db.run(query, [
        title, title_ar, title_en, content, content_ar, content_en,
        type || 'manual', category, language || 'ar', 
        status || 'draft', scheduled_at, created_by
      ], function(err) {
        if (err) {
          reject(err);
        } else {
          resolve({ id: this.lastID, ...messageData });
        }
      });
    });
  }

  // Update message
  static update(id, messageData) {
    return new Promise((resolve, reject) => {
      // Extract all possible fields
      const {
        title, title_ar, title_en, content, content_ar, content_en,
        type, category, language, status, scheduled_at,
        subject, subject_ar, template_id, total_recipients, target_clients
      } = messageData;

      console.log('💾 Updating message in database:', {
        id,
        target_clients: target_clients ? 'provided' : 'not provided',
        total_recipients
      });

      const query = `
        UPDATE messages
        SET title = ?, title_ar = ?, title_en = ?, content = ?, content_ar = ?, content_en = ?,
            type = ?, category = ?, language = ?, status = ?, scheduled_at = ?,
            subject = ?, subject_ar = ?, template_id = ?, total_recipients = ?, target_clients = ?,
            updated_at = CURRENT_TIMESTAMP
        WHERE id = ?
      `;

      db.run(query, [
        title, title_ar, title_en, content, content_ar, content_en,
        type, category, language, status, scheduled_at,
        subject, subject_ar, template_id, total_recipients, target_clients,
        id
      ], function(err) {
        if (err) {
          console.error('❌ Error updating message:', err);
          reject(err);
        } else {
          console.log('✅ Message updated successfully, changes:', this.changes);
          resolve({ id, changes: this.changes });
        }
      });
    });
  }

  // Delete message
  static delete(id) {
    return new Promise((resolve, reject) => {
      db.run('DELETE FROM messages WHERE id = ?', [id], function(err) {
        if (err) {
          reject(err);
        } else {
          resolve({ id, changes: this.changes });
        }
      });
    });
  }

  // Get message statistics
  static getStatistics() {
    return new Promise((resolve, reject) => {
      console.log('📊 Getting message statistics...');
      const queries = {
        total: 'SELECT COUNT(*) as count FROM messages',
        draft: 'SELECT COUNT(*) as count FROM messages WHERE status = "draft"',
        published: 'SELECT COUNT(*) as count FROM messages WHERE status = "published"',
        scheduled: 'SELECT COUNT(*) as count FROM messages WHERE status = "scheduled"',
        byType: 'SELECT type, COUNT(*) as count FROM messages GROUP BY type',
        byLanguage: 'SELECT language, COUNT(*) as count FROM messages GROUP BY language',
        recent: 'SELECT COUNT(*) as count FROM messages WHERE created_at >= datetime("now", "-30 days")'
      };

      const results = {};
      let completed = 0;
      const totalQueries = Object.keys(queries).length;

      Object.entries(queries).forEach(([key, query]) => {
        if (key === 'byType' || key === 'byLanguage') {
          db.all(query, (err, rows) => {
            if (err) {
              reject(err);
              return;
            }
            results[key] = rows;
            completed++;
            if (completed === totalQueries) {
              resolve(results);
            }
          });
        } else {
          db.get(query, (err, row) => {
            if (err) {
              reject(err);
              return;
            }
            results[key] = row.count;
            completed++;
            if (completed === totalQueries) {
              console.log('✅ Message statistics completed:', results);
              resolve(results);
            }
          });
        }
      });
    });
  }

  // Get messages ready for sending (scheduled messages)
  static getScheduledMessages() {
    return new Promise((resolve, reject) => {
      const query = `
        SELECT * FROM messages 
        WHERE status = 'scheduled' 
        AND scheduled_at <= datetime('now')
        ORDER BY scheduled_at ASC
      `;

      db.all(query, (err, rows) => {
        if (err) {
          reject(err);
        } else {
          resolve(rows);
        }
      });
    });
  }

  // Update message status
  static updateStatus(id, status) {
    return new Promise((resolve, reject) => {
      const query = 'UPDATE messages SET status = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?';
      
      db.run(query, [status, id], function(err) {
        if (err) {
          reject(err);
        } else {
          resolve({ id, status, changes: this.changes });
        }
      });
    });
  }
}

module.exports = Message;
