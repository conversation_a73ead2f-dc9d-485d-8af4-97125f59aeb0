const crypto = require('crypto');
const { db } = require('../config/database');

/**
 * Generate unsubscribe tokens for clients that don't have them
 */
async function generateUnsubscribeTokens() {
  try {
    console.log('🔄 Checking for clients without unsubscribe tokens...');
    
    // Find clients without unsubscribe tokens
    const clientsWithoutTokens = await new Promise((resolve, reject) => {
      db.all(
        'SELECT id, email FROM clients WHERE unsubscribe_token IS NULL OR unsubscribe_token = ""',
        [],
        (err, rows) => {
          if (err) reject(err);
          else resolve(rows);
        }
      );
    });

    if (clientsWithoutTokens.length === 0) {
      console.log('✅ All clients already have unsubscribe tokens');
      return;
    }

    console.log(`🔧 Found ${clientsWithoutTokens.length} clients without unsubscribe tokens`);

    // Generate tokens for each client
    for (const client of clientsWithoutTokens) {
      const token = crypto.randomBytes(32).toString('hex');
      
      await new Promise((resolve, reject) => {
        db.run(
          'UPDATE clients SET unsubscribe_token = ? WHERE id = ?',
          [token, client.id],
          function(err) {
            if (err) {
              console.error(`❌ Error updating token for client ${client.email}:`, err);
              reject(err);
            } else {
              console.log(`✅ Generated unsubscribe token for ${client.email}`);
              resolve();
            }
          }
        );
      });
    }

    console.log(`✅ Successfully generated unsubscribe tokens for ${clientsWithoutTokens.length} clients`);
    
  } catch (error) {
    console.error('❌ Error generating unsubscribe tokens:', error);
    throw error;
  }
}

/**
 * Generate a single unsubscribe token for a specific client
 */
async function generateTokenForClient(clientId) {
  try {
    const token = crypto.randomBytes(32).toString('hex');
    
    await new Promise((resolve, reject) => {
      db.run(
        'UPDATE clients SET unsubscribe_token = ? WHERE id = ?',
        [token, clientId],
        function(err) {
          if (err) reject(err);
          else resolve();
        }
      );
    });

    return token;
  } catch (error) {
    console.error(`Error generating token for client ${clientId}:`, error);
    throw error;
  }
}

module.exports = {
  generateUnsubscribeTokens,
  generateTokenForClient
};
