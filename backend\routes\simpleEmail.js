const express = require('express');
const nodemailer = require('nodemailer');
const router = express.Router();
const { auth } = require('../middleware/auth');
const { db } = require('../config/database');

// Get saved SMTP settings
router.get('/settings', auth, async (req, res) => {
  try {
    //console.log('📥 Loading saved SMTP settings');

    // First check if settings table exists
    db.get(
      "SELECT name FROM sqlite_master WHERE type='table' AND name='settings'",
      [],
      (err, table) => {
        if (err) {
          console.error('❌ Error checking settings table:', err);
          return res.status(500).json({
            success: false,
            error: 'Database error: ' + err.message,
            error_ar: 'خطأ في قاعدة البيانات: ' + err.message
          });
        }

        if (!table) {
          console.log('⚠️ Settings table does not exist');
          return res.json({
            success: true,
            data: {
              host: '',
              port: 465,
              email: '',
              password: '',
              secure: true,
              testEmail: '<EMAIL>'
            },
            hasSettings: false,
            message: 'Settings table not found, using defaults',
            message_ar: 'جدول الإعدادات غير موجود، استخدام الافتراضية'
          });
        }

        //console.log('✅ Settings table exists, querying SMTP settings');

        // Now query SMTP settings
        db.all(
          "SELECT key, value FROM settings WHERE key LIKE 'smtp_%'",
          [],
          (err, rows) => {
            if (err) {
              console.error('❌ Database error loading SMTP settings:', err);
              return res.status(500).json({
                success: false,
                error: 'Database error: ' + err.message,
                error_ar: 'خطأ في قاعدة البيانات: ' + err.message
              });
            }

            /*console.log('📊 Raw database rows:', rows);*/

            const settings = {};
            if (rows && rows.length > 0) {
              rows.forEach(row => {
                settings[row.key] = row.value;
              });
            }

           /*console.log('✅ SMTP settings loaded from database:', {
              host: settings.smtp_host || 'Not set',
              port: settings.smtp_port || 'Not set',
              username: settings.smtp_username || 'Not set',
              secure: settings.smtp_secure || 'Not set',
              totalRows: rows ? rows.length : 0
            });*/

            // Convert to form format
            const formData = {
              host: settings.smtp_host || '',
              port: settings.smtp_port ? parseInt(settings.smtp_port) : 465,
              email: settings.smtp_username || '',
              password: settings.smtp_password || '',
              secure: settings.smtp_secure === 'true' || settings.smtp_secure === true || settings.smtp_secure === '1' || settings.smtp_secure === 1,
              testEmail: '<EMAIL>' // Default test email
            };

            /*console.log('� Secure value conversion:', {
              original: settings.smtp_secure,
              type: typeof settings.smtp_secure,
              converted: formData.secure
            });

            console.log('�📋 Converted form data:', {
              ...formData,
              password: formData.password ? '***' : 'Not set'
            });*/

            res.json({
              success: true,
              data: formData,
              hasSettings: rows && rows.length > 0,
              message: 'SMTP settings loaded successfully',
              message_ar: 'تم تحميل إعدادات SMTP بنجاح'
            });
          }
        );
      }
    );

  } catch (error) {
    console.error('❌ Unexpected error loading SMTP settings:', error);
    res.status(500).json({
      success: false,
      error: 'Unexpected error: ' + error.message,
      error_ar: 'خطأ غير متوقع: ' + error.message
    });
  }
});

// Save SMTP settings
router.post('/save-settings', auth, async (req, res) => {
  try {
    console.log('💾 Saving SMTP settings');
    console.log('Request body:', { ...req.body, password: '***' });

    const { host, port, email, password, secure, testEmail } = req.body;

    // Validate required fields
    if (!host || !port || !email || !password) {
      console.log('❌ Missing required fields for saving');
      return res.status(400).json({
        success: false,
        error: 'Missing required fields: host, port, email, password',
        error_ar: 'حقول مطلوبة مفقودة: الخادم، المنفذ، البريد الإلكتروني، كلمة المرور'
      });
    }

    // Prepare settings to save
    const settingsToSave = {
      smtp_host: host,
      smtp_port: port.toString(),
      smtp_username: email,
      smtp_password: password,
      smtp_secure: secure ? '1' : '0',
      smtp_configured: 'true'
    };

    console.log('📝 Settings to save:', {
      ...settingsToSave,
      smtp_password: '***'
    });

    // Save each setting
    const savePromises = Object.entries(settingsToSave).map(([key, value]) => {
      return new Promise((resolve, reject) => {
        db.run(
          'INSERT OR REPLACE INTO settings (key, value, updated_at) VALUES (?, ?, CURRENT_TIMESTAMP)',
          [key, value],
          function(err) {
            if (err) {
              console.error(`❌ Error saving ${key}:`, err);
              reject(err);
            } else {
              console.log(`✅ Saved ${key}: ${key === 'smtp_password' ? '***' : value}`);
              resolve();
            }
          }
        );
      });
    });

    // Wait for all settings to be saved
    await Promise.all(savePromises);

    console.log('✅ All SMTP settings saved successfully');

    res.json({
      success: true,
      message: 'SMTP settings saved successfully',
      message_ar: 'تم حفظ إعدادات SMTP بنجاح',
      data: {
        host: settingsToSave.smtp_host,
        port: parseInt(settingsToSave.smtp_port),
        email: settingsToSave.smtp_username,
        secure: settingsToSave.smtp_secure === '1',
        configured: true
      }
    });

  } catch (error) {
    console.error('❌ Error saving SMTP settings:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to save SMTP settings: ' + error.message,
      error_ar: 'فشل في حفظ إعدادات SMTP: ' + error.message
    });
  }
});

// Simple email test endpoint
router.post('/test-connection', auth, async (req, res) => {
  try {
    console.log('🚀 Simple email test started');
    console.log('Request body:', { ...req.body, password: '***' });

    const { host, port, email, password, secure, testEmail } = req.body;

    console.log('📋 Extracted values:', {
      host,
      port,
      email,
      secure,
      testEmail,
      hasTestEmail: !!testEmail
    });

    // Validate required fields
    if (!host || !port || !email || !password) {
      console.log('❌ Missing required fields');
      return res.status(400).json({
        success: false,
        error: 'Missing required fields: host, port, email, password',
        error_ar: 'حقول مطلوبة مفقودة: الخادم، المنفذ، البريد الإلكتروني، كلمة المرور'
      });
    }

    // Create transporter configuration
    const transporterConfig = {
      host: host,
      port: parseInt(port),
      secure: secure === true || secure === 'true' || port == 465,
      auth: {
        user: email,
        pass: password
      },
      tls: {
        rejectUnauthorized: false,
        ciphers: 'SSLv3',
        secureProtocol: 'TLSv1_method'
      },
      connectionTimeout: 60000,
      greetingTimeout: 30000,
      socketTimeout: 60000,
      debug: false,
      logger: false
    };

    console.log('🔧 Transporter config:', {
      host: transporterConfig.host,
      port: transporterConfig.port,
      secure: transporterConfig.secure,
      user: transporterConfig.auth.user
    });

    // Create transporter
    const transporter = nodemailer.createTransport(transporterConfig);

    // Test connection
    console.log('🔍 Testing connection...');
    const startTime = Date.now();
    
    await transporter.verify();
    
    const connectionTime = Date.now() - startTime;
    console.log(`✅ Connection successful in ${connectionTime}ms`);

    // Send test email if testEmail is provided
    let emailSent = false;
    let emailError = null;
    let messageId = null;

    if (testEmail && testEmail.trim() !== '') {
      try {
        console.log(`📧 Sending test email to: ${testEmail}`);
        
        const mailOptions = {
          from: `"Test Email" <${email}>`,
          to: testEmail,
          subject: 'Test Email - اختبار البريد الإلكتروني',
          html: `
            <h2>Test Email Successful! ✅</h2>
            <h2 style="direction: rtl;">نجح اختبار البريد الإلكتروني! ✅</h2>
            <p>Server: ${host}</p>
            <p>Port: ${port}</p>
            <p>Security: ${transporterConfig.secure ? 'SSL/TLS' : 'None'}</p>
            <p>Connection Time: ${connectionTime}ms</p>
            <p>Time: ${new Date().toLocaleString()}</p>
          `,
          text: `Test Email Successful!\nServer: ${host}\nPort: ${port}\nConnection Time: ${connectionTime}ms`
        };

        const emailStartTime = Date.now();
        const info = await transporter.sendMail(mailOptions);
        const emailTime = Date.now() - emailStartTime;
        
        emailSent = true;
        messageId = info.messageId;
        
        console.log(`✅ Test email sent successfully in ${emailTime}ms`);
        console.log(`📧 Message ID: ${messageId}`);
      } catch (error) {
        console.error('❌ Failed to send test email:', error.message);
        emailError = error.message;
      }
    }

    res.json({
      success: true,
      data: {
        message: 'Connection test successful',
        message_ar: 'نجح اختبار الاتصال',
        connectionTime: `${connectionTime}ms`,
        emailSent: emailSent,
        emailError: emailError,
        messageId: messageId,
        config: {
          host: transporterConfig.host,
          port: transporterConfig.port,
          secure: transporterConfig.secure,
          user: transporterConfig.auth.user
        }
      }
    });

  } catch (error) {
    console.error('❌ Connection test failed:', error);

    let errorMessage = error.message;
    let suggestion = '';
    let suggestion_ar = '';

    // Provide helpful error messages
    if (error.code === 'ECONNREFUSED') {
      suggestion = 'Connection refused. Check if the SMTP server and port are correct.';
      suggestion_ar = 'تم رفض الاتصال. تحقق من صحة خادم SMTP والمنفذ.';
    } else if (error.code === 'EAUTH') {
      suggestion = 'Authentication failed. Check your email and password.';
      suggestion_ar = 'فشل في المصادقة. تحقق من البريد الإلكتروني وكلمة المرور.';
    } else if (error.code === 'ETIMEDOUT') {
      suggestion = 'Connection timeout. Try a different port or check your internet connection.';
      suggestion_ar = 'انتهت مهلة الاتصال. جرب منفذ مختلف أو تحقق من اتصال الإنترنت.';
    }

    res.json({
      success: false,
      error: errorMessage,
      suggestion: suggestion,
      suggestion_ar: suggestion_ar,
      errorCode: error.code
    });
  }
});

// Email delivery test - Using form data
router.post('/delivery-test', auth, async (req, res) => {
  try {
    console.log('🚀 Starting delivery test with form data');
    console.log('Request body:', { ...req.body, password: '***' });

    const { host, port, email, password, secure, testEmail } = req.body;

    // Validate required fields
    if (!host || !port || !email || !password) {
      console.log('❌ Missing required fields for delivery test');
      return res.status(400).json({
        success: false,
        error: 'Missing required fields: host, port, email, password',
        error_ar: 'حقول مطلوبة مفقودة: الخادم، المنفذ، البريد الإلكتروني، كلمة المرور'
      });
    }

    const testResults = {
      timestamp: new Date().toISOString(),
      tests: [],
      summary: {},
      recommendations: []
    };

    // Use form data for test
    const targetEmail = testEmail || '<EMAIL>';

    // Create configuration from form data
    const config = {
      name: `${host}:${port}`,
      host: host,
      port: parseInt(port),
      secure: secure === true || secure === 'true' || port == 465,
      auth: {
        user: email,
        pass: password
      }
    };

    // Single test with form data
    const testName = `${config.name} -> ${targetEmail}`;
    console.log(`🧪 Testing: ${testName}`);

    try {
      const transporter = nodemailer.createTransport({
        host: config.host,
        port: config.port,
        secure: config.secure,
        auth: config.auth,
        tls: { rejectUnauthorized: false },
        connectionTimeout: 60000,
        greetingTimeout: 30000,
        socketTimeout: 60000
      });

      // Test connection
      const connStart = Date.now();
      await transporter.verify();
      const connTime = Date.now() - connStart;

      // Send test email
      const mailOptions = {
        from: `"Delivery Test" <${config.auth.user}>`,
        to: targetEmail,
        subject: `🧪 Delivery Test - ${config.name} - ${new Date().toLocaleString()}`,
        text: `
Delivery Test Results

Configuration: ${config.name}
Server: ${config.host}:${config.port}
Security: ${config.secure ? 'SSL/TLS' : 'STARTTLS'}
Target: ${targetEmail}
Time: ${new Date().toLocaleString()}
Connection Time: ${connTime}ms

If you receive this email, delivery is working for this configuration.
        `,
        html: `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 2px solid #3498db; border-radius: 10px;">
            <h2 style="color: #3498db;">🧪 Email Delivery Test</h2>
            <div style="background: #ecf0f1; padding: 15px; border-radius: 5px; margin: 20px 0;">
              <h3>Test Configuration:</h3>
              <ul>
                <li><strong>Name:</strong> ${config.name}</li>
                <li><strong>Server:</strong> ${config.host}:${config.port}</li>
                <li><strong>Security:</strong> ${config.secure ? 'SSL/TLS' : 'STARTTLS'}</li>
                <li><strong>Target:</strong> ${targetEmail}</li>
                <li><strong>Connection Time:</strong> ${connTime}ms</li>
                <li><strong>Test Time:</strong> ${new Date().toLocaleString()}</li>
              </ul>
            </div>
            <p style="color: #27ae60; font-weight: bold;">✅ If you receive this email, delivery is working for this configuration!</p>
            <p style="font-size: 12px; color: #7f8c8d;">This is an automated delivery test from the Email Marketing System.</p>
          </div>
        `
      };

      const sendStart = Date.now();
      const info = await transporter.sendMail(mailOptions);
      const sendTime = Date.now() - sendStart;

      testResults.tests.push({
        name: testName,
        status: 'success',
        config: config.name,
        target: targetEmail,
        connectionTime: `${connTime}ms`,
        sendTime: `${sendTime}ms`,
        messageId: info.messageId,
        response: info.response,
        accepted: info.accepted,
        rejected: info.rejected
      });

      console.log(`✅ ${testName} - Success`);

    } catch (error) {
      testResults.tests.push({
        name: testName,
        status: 'failed',
        config: config.name,
        target: targetEmail,
        error: error.message,
        errorCode: error.code
      });

      console.log(`❌ ${testName} - Failed: ${error.message}`);
    }

    // Generate summary
    const successful = testResults.tests.filter(t => t.status === 'success');
    const failed = testResults.tests.filter(t => t.status === 'failed');

    testResults.summary = {
      total: testResults.tests.length,
      successful: successful.length,
      failed: failed.length,
      successRate: `${Math.round((successful.length / testResults.tests.length) * 100)}%`
    };

    // Generate recommendations
    if (successful.length === 0) {
      testResults.recommendations.push({
        type: 'critical',
        message: 'No emails were delivered successfully. Check server configuration and credentials.',
        message_ar: 'لم يتم تسليم أي رسائل بنجاح. تحقق من إعدادات الخادم وبيانات الاعتماد.'
      });
    } else if (successful.length < testResults.tests.length) {
      testResults.recommendations.push({
        type: 'warning',
        message: 'Some configurations failed. Use the working configurations for production.',
        message_ar: 'فشلت بعض الإعدادات. استخدم الإعدادات التي تعمل للإنتاج.'
      });
    } else {
      testResults.recommendations.push({
        type: 'success',
        message: 'All tests passed! Check recipient email folders including spam.',
        message_ar: 'نجحت جميع الاختبارات! تحقق من مجلدات البريد الإلكتروني للمستلم بما في ذلك الرسائل المزعجة.'
      });
    }

    // Add delivery troubleshooting
    testResults.recommendations.push({
      type: 'troubleshooting',
      message: 'If emails are sent but not received: 1) Check spam folders, 2) Verify recipient email, 3) Check server reputation, 4) Configure SPF/DKIM records',
      message_ar: 'إذا تم إرسال الرسائل ولكن لم يتم استلامها: 1) تحقق من مجلدات الرسائل المزعجة، 2) تحقق من بريد المستلم، 3) تحقق من سمعة الخادم، 4) قم بتكوين سجلات SPF/DKIM'
    });

    console.log('📊 Delivery test completed:', testResults.summary);

    res.json({
      success: true,
      data: testResults
    });

  } catch (error) {
    console.error('❌ Delivery test failed:', error);

    res.json({
      success: false,
      error: error.message
    });
  }
});

// Email delivery test - Using saved settings from database
router.post('/delivery-test2', auth, async (req, res) => {
  try {
    console.log('🚀 Starting delivery test with saved settings from database');

    const { testEmail } = req.body; // البريد المستهدف من الطلب
    const targetEmail = testEmail || '<EMAIL>';

    console.log('📧 Target email:', targetEmail);

    // استدعاء الإعدادات المحفوظة من قاعدة البيانات (نفس طريقة GET /settings)
    const settingsPromises = [
      'smtp_host',
      'smtp_port',
      'smtp_username',
      'smtp_password',
      'smtp_secure'
    ].map(key => {
      return new Promise((resolve, reject) => {
        db.get(
          'SELECT value FROM settings WHERE key = ?',
          [key],
          (err, row) => {
            if (err) {
              reject(err);
            } else {
              resolve({ key, value: row ? row.value : null });
            }
          }
        );
      });
    });

    const settingsResults = await Promise.all(settingsPromises);
    const settings = {};
    settingsResults.forEach(result => {
      settings[result.key] = result.value;
    });

    const host = settings.smtp_host;
    const port = settings.smtp_port;
    const email = settings.smtp_username;
    const password = settings.smtp_password;
    const secure = settings.smtp_secure === '1';

    console.log('📋 Retrieved settings from database:', { host, port, email, secure, password: password ? '***' : 'null' });

    // التحقق من وجود الإعدادات المطلوبة
    if (!host || !port || !email || !password) {
      console.log('❌ Incomplete saved settings');
      return res.status(400).json({
        success: false,
        error: 'Saved settings are incomplete. Please update your SMTP settings.',
        error_ar: 'الإعدادات المحفوظة غير مكتملة. يرجى تحديث إعدادات SMTP.'
      });
    }

    const testResults = {
      timestamp: new Date().toISOString(),
      tests: [],
      summary: {},
      recommendations: [],
      usingSavedSettings: true
    };

    // Create configuration from saved settings
    const config = {
      name: `${host}:${port} (Saved)`,
      host: host,
      port: parseInt(port),
      secure: secure === true || secure === 'true' || port == 465,
      auth: {
        user: email,
        pass: password
      }
    };

    // Single test with saved settings
    const testName = `${config.name} -> ${targetEmail}`;
    console.log(`🧪 Testing with saved settings: ${testName}`);

    try {
      const transporter = nodemailer.createTransport({
        host: config.host,
        port: config.port,
        secure: config.secure,
        auth: config.auth,
        tls: { rejectUnauthorized: false },
        connectionTimeout: 60000,
        greetingTimeout: 30000,
        socketTimeout: 60000
      });

      // Test connection
      const connStart = Date.now();
      await transporter.verify();
      const connTime = Date.now() - connStart;

      // Send test email
      const mailOptions = {
        from: `"Saved Settings Test" <${config.auth.user}>`,
        to: targetEmail,
        subject: `🔧 Saved Settings Test - ${config.name} - ${new Date().toLocaleString()}`,
        text: `
Saved Settings Delivery Test

Configuration: ${config.name}
Server: ${config.host}:${config.port}
Security: ${config.secure ? 'SSL/TLS' : 'STARTTLS'}
Target: ${targetEmail}
Time: ${new Date().toLocaleString()}
Connection Time: ${connTime}ms

This test used your saved SMTP settings from the database.
If you receive this email, your saved configuration is working correctly.
        `,
        html: `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 2px solid #27ae60; border-radius: 10px;">
            <h2 style="color: #27ae60;">🔧 Saved Settings Test</h2>
            <div style="background: #ecf0f1; padding: 15px; border-radius: 5px; margin: 20px 0;">
              <h3>Saved Configuration:</h3>
              <ul>
                <li><strong>Name:</strong> ${config.name}</li>
                <li><strong>Server:</strong> ${config.host}:${config.port}</li>
                <li><strong>Security:</strong> ${config.secure ? 'SSL/TLS' : 'STARTTLS'}</li>
                <li><strong>Target:</strong> ${targetEmail}</li>
                <li><strong>Connection Time:</strong> ${connTime}ms</li>
                <li><strong>Test Time:</strong> ${new Date().toLocaleString()}</li>
              </ul>
            </div>
            <p style="color: #27ae60; font-weight: bold;">✅ Your saved SMTP settings are working correctly!</p>
            <p style="font-size: 12px; color: #7f8c8d;">This test used your saved SMTP configuration from the database.</p>
          </div>
        `
      };

      const sendStart = Date.now();
      const info = await transporter.sendMail(mailOptions);
      const sendTime = Date.now() - sendStart;

      testResults.tests.push({
        name: testName,
        status: 'success',
        config: config.name,
        target: targetEmail,
        connectionTime: `${connTime}ms`,
        sendTime: `${sendTime}ms`,
        messageId: info.messageId,
        response: info.response,
        accepted: info.accepted,
        rejected: info.rejected
      });

      console.log(`✅ ${testName} - Success with saved settings`);

    } catch (error) {
      testResults.tests.push({
        name: testName,
        status: 'failed',
        config: config.name,
        target: targetEmail,
        error: error.message,
        errorCode: error.code
      });

      console.log(`❌ ${testName} - Failed: ${error.message}`);
    }

    // Generate summary
    const successful = testResults.tests.filter(t => t.status === 'success');
    const failed = testResults.tests.filter(t => t.status === 'failed');

    testResults.summary = {
      total: testResults.tests.length,
      successful: successful.length,
      failed: failed.length,
      successRate: `${Math.round((successful.length / testResults.tests.length) * 100)}%`
    };

    // Generate recommendations
    if (successful.length === 0) {
      testResults.recommendations.push({
        type: 'critical',
        message: 'No emails were delivered successfully. Check server configuration and credentials.',
        message_ar: 'لم يتم تسليم أي رسائل بنجاح. تحقق من إعدادات الخادم وبيانات الاعتماد.'
      });
    } else if (successful.length < testResults.tests.length) {
      testResults.recommendations.push({
        type: 'warning',
        message: 'Some configurations failed. Use the working configurations for production.',
        message_ar: 'فشلت بعض الإعدادات. استخدم الإعدادات التي تعمل للإنتاج.'
      });
    } else {
      testResults.recommendations.push({
        type: 'success',
        message: 'All tests passed! Check recipient email folders including spam.',
        message_ar: 'نجحت جميع الاختبارات! تحقق من مجلدات البريد الإلكتروني للمستلم بما في ذلك الرسائل المزعجة.'
      });
    }

    // Add delivery troubleshooting
    testResults.recommendations.push({
      type: 'troubleshooting',
      message: 'If emails are sent but not received: 1) Check spam folders, 2) Verify recipient email, 3) Check server reputation, 4) Configure SPF/DKIM records',
      message_ar: 'إذا تم إرسال الرسائل ولكن لم يتم استلامها: 1) تحقق من مجلدات الرسائل المزعجة، 2) تحقق من بريد المستلم، 3) تحقق من سمعة الخادم، 4) قم بتكوين سجلات SPF/DKIM'
    });

    console.log('📊 Delivery test completed:', testResults.summary);

    res.json({
      success: true,
      data: testResults
    });

  } catch (error) {
    console.error('❌ Delivery test failed:', error);

    res.json({
      success: false,
      error: error.message
    });
  }
});

module.exports = router;
