/* Dynamic Theme Styles */
:root {
  --primary-color: #3B82F6;
  --border-radius: 0.375rem;
}

/* Base theme styles */
html.theme-dark {
  background-color: #1f2937 !important;
  color: #f9fafb !important;
}

html.theme-light {
  background-color: #ffffff !important;
  color: #111827 !important;
}

body.theme-dark {
  background-color: #1f2937 !important;
  color: #f9fafb !important;
}

body.theme-light {
  background-color: #ffffff !important;
  color: #111827 !important;
}

/* Primary Color Applications */
.bg-primary {
  background-color: var(--primary-color) !important;
}

.text-primary {
  color: var(--primary-color) !important;
}

.border-primary {
  border-color: var(--primary-color) !important;
}

.ring-primary {
  --tw-ring-color: var(--primary-color) !important;
}

/* Button Styles with Primary Color */
.btn-primary {
  background-color: var(--primary-color) !important;
  border-color: var(--primary-color) !important;
  color: white !important;
}

.btn-primary:hover {
  background-color: var(--primary-color) !important;
  opacity: 0.9;
}

.btn-primary:focus {
  --tw-ring-color: var(--primary-color) !important;
}

/* Border Radius Applications */
.rounded-dynamic {
  border-radius: var(--border-radius) !important;
}

.rounded-t-dynamic {
  border-top-left-radius: var(--border-radius) !important;
  border-top-right-radius: var(--border-radius) !important;
}

.rounded-b-dynamic {
  border-bottom-left-radius: var(--border-radius) !important;
  border-bottom-right-radius: var(--border-radius) !important;
}

.rounded-l-dynamic {
  border-top-left-radius: var(--border-radius) !important;
  border-bottom-left-radius: var(--border-radius) !important;
}

.rounded-r-dynamic {
  border-top-right-radius: var(--border-radius) !important;
  border-bottom-right-radius: var(--border-radius) !important;
}

/* Override default blue colors with primary color */
.text-blue-600 {
  color: var(--primary-color) !important;
}

.bg-blue-600 {
  background-color: var(--primary-color) !important;
}

.bg-blue-500 {
  background-color: var(--primary-color) !important;
}

.border-blue-500 {
  border-color: var(--primary-color) !important;
}

.ring-blue-500 {
  --tw-ring-color: var(--primary-color) !important;
}

.focus\:ring-blue-500:focus {
  --tw-ring-color: var(--primary-color) !important;
}

.hover\:bg-blue-700:hover {
  background-color: var(--primary-color) !important;
  opacity: 0.9;
}

.focus\:border-blue-500:focus {
  border-color: var(--primary-color) !important;
}

/* Card and Component Styles */
.card {
  border-radius: var(--border-radius) !important;
}

.rounded-lg {
  border-radius: var(--border-radius) !important;
}

.rounded-md {
  border-radius: calc(var(--border-radius) * 0.8) !important;
}

.rounded-sm {
  border-radius: calc(var(--border-radius) * 0.5) !important;
}

/* Theme-specific styles */
.theme-dark {
  color-scheme: dark;
  background-color: #1f2937 !important;
  color: #f9fafb !important;
}

.theme-dark * {
  color: #f9fafb !important;
}

.theme-dark .bg-white {
  background-color: #374151 !important;
}

.theme-dark .bg-gray-50 {
  background-color: #4b5563 !important;
}

.theme-dark .bg-gray-100 {
  background-color: #6b7280 !important;
}

.theme-dark .text-gray-900 {
  color: #f9fafb !important;
}

.theme-dark .text-gray-800 {
  color: #e5e7eb !important;
}

.theme-dark .text-gray-700 {
  color: #d1d5db !important;
}

.theme-dark .text-gray-600 {
  color: #9ca3af !important;
}

.theme-dark .text-gray-500 {
  color: #6b7280 !important;
}

.theme-dark .border-gray-200 {
  border-color: #4b5563 !important;
}

.theme-dark .border-gray-300 {
  border-color: #6b7280 !important;
}

.theme-dark .shadow-sm {
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3) !important;
}

.theme-dark .shadow-lg {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.2) !important;
}

.theme-dark input, .theme-dark textarea, .theme-dark select {
  background-color: #4b5563 !important;
  border-color: #6b7280 !important;
  color: #f9fafb !important;
}

.theme-dark input::placeholder, .theme-dark textarea::placeholder {
  color: #9ca3af !important;
}

.theme-dark .card {
  background-color: #374151 !important;
  border-color: #4b5563 !important;
}

.theme-dark .bg-blue-50 {
  background-color: #1e3a8a !important;
}

.theme-dark .bg-primary-100 {
  background-color: #1e3a8a !important;
}

.theme-dark h1, .theme-dark h2, .theme-dark h3, .theme-dark h4, .theme-dark h5, .theme-dark h6 {
  color: #f9fafb !important;
}

.theme-dark p {
  color: #d1d5db !important;
}

.theme-dark label {
  color: #e5e7eb !important;
}

.theme-light {
  color-scheme: light;
  background-color: #ffffff !important;
  color: #111827 !important;
}

/* Font size classes */
.font-size-small {
  font-size: 14px;
}

.font-size-medium {
  font-size: 16px;
}

.font-size-large {
  font-size: 18px;
}

.font-size-small h1 { font-size: 1.8rem; }
.font-size-small h2 { font-size: 1.5rem; }
.font-size-small h3 { font-size: 1.2rem; }

.font-size-medium h1 { font-size: 2rem; }
.font-size-medium h2 { font-size: 1.75rem; }
.font-size-medium h3 { font-size: 1.5rem; }

.font-size-large h1 { font-size: 2.5rem; }
.font-size-large h2 { font-size: 2rem; }
.font-size-large h3 { font-size: 1.75rem; }

/* Sidebar and Navigation */
.sidebar-item.active {
  background-color: var(--primary-color) !important;
  color: white !important;
}

.sidebar-item:hover {
  background-color: var(--primary-color) !important;
  opacity: 0.8;
  color: white !important;
}

/* Dark theme sidebar */
.theme-dark .bg-white {
  background-color: #374151 !important;
}

.theme-dark .border-gray-200 {
  border-color: #4b5563 !important;
}

.theme-dark .text-gray-600 {
  color: #d1d5db !important;
}

.theme-dark .hover\:bg-gray-50:hover {
  background-color: #4b5563 !important;
}

.theme-dark .hover\:text-gray-900:hover {
  color: #f9fafb !important;
}

/* Dark theme buttons */
.theme-dark button {
  background-color: #4b5563 !important;
  border-color: #6b7280 !important;
  color: #f9fafb !important;
}

.theme-dark .btn-primary {
  background-color: var(--primary-color) !important;
  border-color: var(--primary-color) !important;
  color: white !important;
}

.theme-dark .hover\:border-gray-300:hover {
  border-color: #6b7280 !important;
}

/* Dark theme tables and headers */
.theme-dark table {
  background-color: #374151 !important;
  color: #f9fafb !important;
}

.theme-dark thead {
  background-color: #4b5563 !important;
  color: #f9fafb !important;
}

.theme-dark th {
  background-color: #4b5563 !important;
  color: #f9fafb !important;
  border-color: #6b7280 !important;
}

.theme-dark td {
  background-color: #374151 !important;
  color: #f9fafb !important;
  border-color: #6b7280 !important;
}

.theme-dark tr:nth-child(even) {
  background-color: #4b5563 !important;
}

.theme-dark tr:hover {
  background-color: #6b7280 !important;
}

/* Dark theme for topbar/header sections */
.theme-dark .bg-gray-50 {
  background-color: #4b5563 !important;
}

.theme-dark .bg-gray-100 {
  background-color: #6b7280 !important;
}

.theme-dark .bg-gray-200 {
  background-color: #6b7280 !important;
}

/* Dark theme for cards and containers */
.theme-dark .card-header {
  background-color: #4b5563 !important;
  border-color: #6b7280 !important;
}

.theme-dark .card-body {
  background-color: #374151 !important;
}

.theme-dark .card-footer {
  background-color: #4b5563 !important;
  border-color: #6b7280 !important;
}

/* Dark theme for charts and icons */
.theme-dark svg {
  color: #f9fafb !important;
}

.theme-dark .chart-container {
  background-color: #374151 !important;
  border-color: #6b7280 !important;
}

.theme-dark .icon-container {
  background-color: #4b5563 !important;
  border-color: #6b7280 !important;
}

/* Dark theme for chart backgrounds */
.theme-dark .recharts-wrapper {
  background-color: #374151 !important;
}

.theme-dark .recharts-cartesian-grid line {
  stroke: #6b7280 !important;
}

.theme-dark .recharts-text {
  fill: #f9fafb !important;
}

.theme-dark .recharts-legend-wrapper {
  color: #f9fafb !important;
}

/* Dark theme for dashboard widgets */
.theme-dark .widget {
  background-color: #374151 !important;
  border-color: #6b7280 !important;
}

.theme-dark .widget-header {
  background-color: #4b5563 !important;
  color: #f9fafb !important;
}

.theme-dark .widget-body {
  background-color: #374151 !important;
  color: #f9fafb !important;
}

/* Dark theme for statistics cards */
.theme-dark .stat-card {
  background-color: #374151 !important;
  border-color: #6b7280 !important;
}

.theme-dark .stat-icon {
  background-color: #4b5563 !important;
  color: #f9fafb !important;
}

/* Dark theme for navigation and breadcrumbs */
.theme-dark .breadcrumb {
  background-color: #4b5563 !important;
  color: #f9fafb !important;
}

.theme-dark .nav-tabs {
  border-color: #6b7280 !important;
}

.theme-dark .nav-tabs .nav-link {
  background-color: #4b5563 !important;
  border-color: #6b7280 !important;
  color: #f9fafb !important;
}

.theme-dark .nav-tabs .nav-link.active {
  background-color: #374151 !important;
  border-color: #6b7280 !important;
  color: #f9fafb !important;
}

/* Dark theme for specific chart libraries */
.theme-dark .apexcharts-canvas {
  background-color: #374151 !important;
}

.theme-dark .apexcharts-text {
  fill: #f9fafb !important;
}

.theme-dark .apexcharts-gridline {
  stroke: #6b7280 !important;
}

.theme-dark .apexcharts-legend-text {
  color: #f9fafb !important;
}

/* Dark theme for Chart.js */
.theme-dark canvas {
  background-color: #374151 !important;
}

/* Dark theme for any white backgrounds that should be dark */
.theme-dark [style*="background-color: white"],
.theme-dark [style*="background-color: #ffffff"],
.theme-dark [style*="background-color: #fff"] {
  background-color: #374151 !important;
}

.theme-dark [style*="color: black"],
.theme-dark [style*="color: #000000"],
.theme-dark [style*="color: #000"] {
  color: #f9fafb !important;
}

/* Dark theme for modals and overlays */
.theme-dark .modal-content {
  background-color: #374151 !important;
  border-color: #6b7280 !important;
}

.theme-dark .modal-header {
  background-color: #4b5563 !important;
  border-color: #6b7280 !important;
}

.theme-dark .modal-footer {
  background-color: #4b5563 !important;
  border-color: #6b7280 !important;
}

/* Dark theme for dropdowns */
.theme-dark .dropdown-menu {
  background-color: #374151 !important;
  border-color: #6b7280 !important;
}

.theme-dark .dropdown-item {
  color: #f9fafb !important;
}

.theme-dark .dropdown-item:hover {
  background-color: #4b5563 !important;
  color: #f9fafb !important;
}

/* Dark theme for icon backgrounds in cards */
.theme-dark .bg-blue-100 {
  background-color: #1e3a8a !important;
}

.theme-dark .bg-green-100 {
  background-color: #14532d !important;
}

.theme-dark .bg-purple-100 {
  background-color: #581c87 !important;
}

.theme-dark .bg-orange-100 {
  background-color: #9a3412 !important;
}

.theme-dark .bg-red-100 {
  background-color: #991b1b !important;
}

.theme-dark .bg-yellow-100 {
  background-color: #92400e !important;
}

.theme-dark .bg-pink-100 {
  background-color: #831843 !important;
}

.theme-dark .bg-indigo-100 {
  background-color: #3730a3 !important;
}

.theme-dark .bg-teal-100 {
  background-color: #134e4a !important;
}

.theme-dark .bg-cyan-100 {
  background-color: #164e63 !important;
}

/* Dark theme for icon colors */
.theme-dark .text-blue-600 {
  color: #60a5fa !important;
}

.theme-dark .text-green-600 {
  color: #4ade80 !important;
}

.theme-dark .text-purple-600 {
  color: #a78bfa !important;
}

.theme-dark .text-orange-600 {
  color: #fb923c !important;
}

.theme-dark .text-red-600 {
  color: #f87171 !important;
}

.theme-dark .text-yellow-600 {
  color: #fbbf24 !important;
}

.theme-dark .text-pink-600 {
  color: #f472b6 !important;
}

.theme-dark .text-indigo-600 {
  color: #818cf8 !important;
}

.theme-dark .text-teal-600 {
  color: #2dd4bf !important;
}

.theme-dark .text-cyan-600 {
  color: #22d3ee !important;
}

/* Dark theme for ring colors */
.theme-dark .ring-primary-500 {
  --tw-ring-color: var(--primary-color) !important;
}

.theme-dark .bg-primary-50 {
  background-color: rgba(59, 130, 246, 0.1) !important;
}

.theme-dark .bg-primary-100 {
  background-color: rgba(59, 130, 246, 0.2) !important;
}

/* Dark theme for Recharts specifically */
.theme-dark .recharts-wrapper {
  background-color: transparent !important;
}

.theme-dark .recharts-surface {
  background-color: transparent !important;
}

.theme-dark .recharts-cartesian-grid-horizontal line,
.theme-dark .recharts-cartesian-grid-vertical line {
  stroke: #6b7280 !important;
  stroke-opacity: 0.5 !important;
}

.theme-dark .recharts-text {
  fill: #d1d5db !important;
}

.theme-dark .recharts-default-tooltip {
  background-color: #374151 !important;
  border: 1px solid #6b7280 !important;
  border-radius: 6px !important;
  color: #f9fafb !important;
}

.theme-dark .recharts-tooltip-wrapper {
  background-color: #374151 !important;
  border: 1px solid #6b7280 !important;
  border-radius: 6px !important;
}

.theme-dark .recharts-tooltip-label {
  color: #f9fafb !important;
}

.theme-dark .recharts-tooltip-item {
  color: #f9fafb !important;
}

/* Dark theme for chart containers */
.theme-dark .card .card-body {
  background-color: #374151 !important;
}

.theme-dark .card .card-header {
  background-color: #4b5563 !important;
  border-bottom: 1px solid #6b7280 !important;
}

/* Force chart backgrounds to be transparent */
.theme-dark svg {
  background-color: transparent !important;
}

.theme-dark .recharts-responsive-container {
  background-color: transparent !important;
}

/* Dark theme - preserve color swatches in appearance settings */
.theme-dark .bg-blue-500 {
  background-color: #3b82f6 !important;
}

.theme-dark .bg-green-500 {
  background-color: #10b981 !important;
}

.theme-dark .bg-purple-500 {
  background-color: #8b5cf6 !important;
}

.theme-dark .bg-red-500 {
  background-color: #ef4444 !important;
}

.theme-dark .bg-orange-500 {
  background-color: #f97316 !important;
}

.theme-dark .bg-pink-500 {
  background-color: #ec4899 !important;
}

.theme-dark .bg-indigo-500 {
  background-color: #6366f1 !important;
}

.theme-dark .bg-teal-500 {
  background-color: #14b8a6 !important;
}

.theme-dark .bg-cyan-500 {
  background-color: #06b6d4 !important;
}

.theme-dark .bg-emerald-500 {
  background-color: #059669 !important;
}

.theme-dark .bg-lime-500 {
  background-color: #84cc16 !important;
}

.theme-dark .bg-amber-500 {
  background-color: #f59e0b !important;
}

/* Dark theme - preserve theme preview boxes */
.theme-dark .bg-gradient-to-br {
  background: linear-gradient(to bottom right, #3b82f6, #8b5cf6) !important;
}

/* Dark theme - fix white theme preview */
.theme-dark .bg-white.border.border-gray-300 {
  background-color: #ffffff !important;
  border-color: #d1d5db !important;
}

/* Dark theme - preserve gray colors for theme previews */
.theme-dark .bg-gray-800 {
  background-color: #1f2937 !important;
}

.theme-dark .bg-gray-300 {
  background-color: #d1d5db !important;
}

/* Dark theme - specific overrides for appearance settings page */
.theme-dark .appearance-settings .bg-white {
  background-color: #ffffff !important;
}

.theme-dark .appearance-settings .bg-gray-800 {
  background-color: #1f2937 !important;
}

.theme-dark .appearance-settings .border-gray-300 {
  border-color: #d1d5db !important;
}

/* Dark theme - color picker buttons should keep their original colors */
.theme-dark button[class*="bg-blue-500"],
.theme-dark button[class*="bg-green-500"],
.theme-dark button[class*="bg-purple-500"],
.theme-dark button[class*="bg-red-500"],
.theme-dark button[class*="bg-orange-500"],
.theme-dark button[class*="bg-pink-500"],
.theme-dark button[class*="bg-indigo-500"],
.theme-dark button[class*="bg-teal-500"],
.theme-dark button[class*="bg-cyan-500"],
.theme-dark button[class*="bg-emerald-500"],
.theme-dark button[class*="bg-lime-500"],
.theme-dark button[class*="bg-amber-500"] {
  color: white !important;
}

/* Dark theme - theme preview buttons */
.theme-dark .theme-preview-light {
  background-color: #ffffff !important;
  border-color: #d1d5db !important;
}

.theme-dark .theme-preview-dark {
  background-color: #1f2937 !important;
  border-color: #374151 !important;
}

.theme-dark .theme-preview-auto {
  background: linear-gradient(to bottom right, #3b82f6, #8b5cf6) !important;
}

/* Dark theme - override for appearance settings to preserve original colors */
.theme-dark .appearance-settings button {
  background-color: inherit !important;
}

.theme-dark .appearance-settings .bg-blue-500 {
  background-color: #3b82f6 !important;
}

.theme-dark .appearance-settings .bg-green-500 {
  background-color: #10b981 !important;
}

.theme-dark .appearance-settings .bg-purple-500 {
  background-color: #8b5cf6 !important;
}

.theme-dark .appearance-settings .bg-red-500 {
  background-color: #ef4444 !important;
}

.theme-dark .appearance-settings .bg-orange-500 {
  background-color: #f97316 !important;
}

.theme-dark .appearance-settings .bg-pink-500 {
  background-color: #ec4899 !important;
}

.theme-dark .appearance-settings .bg-indigo-500 {
  background-color: #6366f1 !important;
}

.theme-dark .appearance-settings .bg-teal-500 {
  background-color: #14b8a6 !important;
}

.theme-dark .appearance-settings .bg-cyan-500 {
  background-color: #06b6d4 !important;
}

.theme-dark .appearance-settings .bg-emerald-500 {
  background-color: #059669 !important;
}

.theme-dark .appearance-settings .bg-lime-500 {
  background-color: #84cc16 !important;
}

.theme-dark .appearance-settings .bg-amber-500 {
  background-color: #f59e0b !important;
}

/* Dark theme - ensure color buttons maintain their colors */
.theme-dark .appearance-settings .w-12.h-12 {
  border: 2px solid #6b7280 !important;
}

.theme-dark .appearance-settings .w-12.h-12:hover {
  transform: scale(1.05) !important;
  border-color: #9ca3af !important;
}

/* Dark theme - preserve border radius preview colors */
.theme-dark .appearance-settings .w-6.h-6.bg-gray-300 {
  background-color: #d1d5db !important;
}

/* Dark theme - ensure text in appearance settings is visible */
.theme-dark .appearance-settings .text-xs {
  color: #d1d5db !important;
}

.theme-dark .appearance-settings .text-sm {
  color: #e5e7eb !important;
}

/* Dark theme - fix button text in appearance settings */
.theme-dark .appearance-settings button .text-sm {
  color: #f9fafb !important;
}

.theme-dark .appearance-settings button .text-xs {
  color: #d1d5db !important;
}

/* Form Elements */
input:focus, textarea:focus, select:focus {
  border-color: var(--primary-color) !important;
  --tw-ring-color: var(--primary-color) !important;
}

/* Checkbox and Radio */
input[type="checkbox"]:checked {
  background-color: var(--primary-color) !important;
  border-color: var(--primary-color) !important;
}

input[type="radio"]:checked {
  background-color: var(--primary-color) !important;
  border-color: var(--primary-color) !important;
}

/* Progress and Loading */
.progress-bar {
  background-color: var(--primary-color) !important;
}

.loading-spinner {
  border-top-color: var(--primary-color) !important;
}

/* Links */
a {
  color: var(--primary-color) !important;
}

a:hover {
  color: var(--primary-color) !important;
  opacity: 0.8;
}

/* Badges and Tags */
.badge-primary {
  background-color: var(--primary-color) !important;
  color: white !important;
}

/* Tabs */
.tab-active {
  border-bottom-color: var(--primary-color) !important;
  color: var(--primary-color) !important;
}

/* Animations */
.theme-transition {
  transition: all 0.3s ease-in-out;
}

/* RTL Support */
.rtl {
  direction: rtl;
}

.ltr {
  direction: ltr;
}

/* Dark theme - Enhanced test results styling for SMTP settings */
.theme-dark .test-result-success {
  background-color: #14532d !important;
  border-color: #16a34a !important;
  color: #f0fdf4 !important;
}

.theme-dark .test-result-success .font-medium {
  color: #4ade80 !important;
}

.theme-dark .test-result-success .text-sm {
  color: #bbf7d0 !important;
}

.theme-dark .test-result-success p {
  color: #bbf7d0 !important;
}

.theme-dark .test-result-success strong {
  color: #4ade80 !important;
  font-weight: 600;
}

.theme-dark .test-result-error {
  background-color: #7f1d1d !important;
  border-color: #dc2626 !important;
  color: #fef2f2 !important;
}

.theme-dark .test-result-error .font-medium {
  color: #f87171 !important;
}

.theme-dark .test-result-error .text-sm {
  color: #fecaca !important;
}

.theme-dark .test-result-error p {
  color: #fecaca !important;
}

.theme-dark .test-result-error strong {
  color: #f87171 !important;
  font-weight: 600;
}

.theme-dark .test-result-info {
  background-color: #1e3a8a !important;
  border-color: #3b82f6 !important;
  color: #eff6ff !important;
}

.theme-dark .test-result-info .font-medium {
  color: #60a5fa !important;
}

.theme-dark .test-result-info .text-sm {
  color: #dbeafe !important;
}

.theme-dark .test-result-info p {
  color: #dbeafe !important;
}

.theme-dark .test-result-info strong {
  color: #60a5fa !important;
  font-weight: 600;
}

/* Dark theme - Icons in test results */
.theme-dark .test-result-success .text-green-600 {
  color: #4ade80 !important;
}

.theme-dark .test-result-error .text-red-600 {
  color: #f87171 !important;
}

.theme-dark .test-result-info .text-blue-600 {
  color: #60a5fa !important;
}

/* Dark theme - Override specific color classes in test results */
.theme-dark .bg-green-50 {
  background-color: #14532d !important;
}

.theme-dark .bg-red-50 {
  background-color: #7f1d1d !important;
}

.theme-dark .border-green-200 {
  border-color: #16a34a !important;
}

.theme-dark .border-red-200 {
  border-color: #dc2626 !important;
}

.theme-dark .border-blue-200 {
  border-color: #3b82f6 !important;
}

.theme-dark .text-green-800 {
  color: #4ade80 !important;
}

.theme-dark .text-green-700 {
  color: #bbf7d0 !important;
}

.theme-dark .text-red-800 {
  color: #f87171 !important;
}

.theme-dark .text-red-700 {
  color: #fecaca !important;
}

.theme-dark .text-blue-800 {
  color: #60a5fa !important;
}

.theme-dark .text-blue-700 {
  color: #dbeafe !important;
}

/* Dark theme - Ensure all text in test results is visible */
.theme-dark .space-y-1 p {
  color: inherit !important;
}

.theme-dark .space-y-1 strong {
  color: #f9fafb !important;
  font-weight: 600;
}

/* Dark theme - Strong text in test results */
.theme-dark strong {
  color: #f9fafb !important;
  font-weight: 600;
}

.theme-dark .text-sm strong {
  color: #e5e7eb !important;
}

/* Dark theme - Client category badges */
.theme-dark .bg-blue-100 {
  background-color: #1e3a8a !important;
}

.theme-dark .text-blue-800 {
  color: #60a5fa !important;
}

.theme-dark .bg-green-100 {
  background-color: #14532d !important;
}

.theme-dark .text-green-800 {
  color: #4ade80 !important;
}

.theme-dark .bg-yellow-100 {
  background-color: #92400e !important;
}

.theme-dark .text-yellow-800 {
  color: #fbbf24 !important;
}

.theme-dark .bg-red-100 {
  background-color: #7f1d1d !important;
}

.theme-dark .text-red-800 {
  color: #f87171 !important;
}

.theme-dark .bg-purple-100 {
  background-color: #581c87 !important;
}

.theme-dark .text-purple-800 {
  color: #c084fc !important;
}

.theme-dark .bg-gray-100 {
  background-color: #374151 !important;
}

.theme-dark .text-gray-800 {
  color: #d1d5db !important;
}

/* Dark theme - Table elements */
.theme-dark .table-header {
  background-color: #374151 !important;
  color: #f9fafb !important;
}

.theme-dark .table-body {
  background-color: #1f2937 !important;
}

.theme-dark .table-row-hover:hover {
  background-color: #374151 !important;
}

.theme-dark .text-gray-900 {
  color: #f9fafb !important;
}

.theme-dark .text-gray-600 {
  color: #d1d5db !important;
}

.theme-dark .text-gray-500 {
  color: #9ca3af !important;
}

.theme-dark .text-gray-400 {
  color: #6b7280 !important;
}

/* Dark theme - Primary colors */
.theme-dark .bg-primary-100 {
  background-color: #1e3a8a !important;
}

.theme-dark .text-primary-700 {
  color: #60a5fa !important;
}

/* Dark theme - Buttons */
.theme-dark .btn-success {
  background-color: #059669 !important;
  border-color: #059669 !important;
  color: #ffffff !important;
}

.theme-dark .btn-success:hover {
  background-color: #047857 !important;
  border-color: #047857 !important;
}

/* Dark theme - Cards and borders */
.theme-dark .border-gray-200 {
  border-color: #374151 !important;
}

.theme-dark .border-gray-300 {
  border-color: #4b5563 !important;
}

.theme-dark .divide-gray-200 > :not([hidden]) ~ :not([hidden]) {
  border-color: #374151 !important;
}

/* Dark theme - Fix for white text on white background */
.theme-dark .bg-white {
  background-color: #1f2937 !important;
}

.theme-dark .text-white {
  color: #f9fafb !important;
}

/* Dark theme - Specific fixes for badges and labels */
.theme-dark .inline-flex.items-center.px-2\.5.py-0\.5.rounded-full.text-xs.font-medium {
  color: #f9fafb !important;
}

.theme-dark .inline-flex.items-center.px-2\.5.py-0\.5.rounded-full.text-xs.font-medium.bg-gray-100 {
  background-color: #374151 !important;
  color: #f9fafb !important;
}

.theme-dark .inline-flex.items-center.px-2\.5.py-0\.5.rounded-full.text-xs.font-medium.bg-blue-100 {
  background-color: #1e3a8a !important;
  color: #93c5fd !important;
}

.theme-dark .inline-flex.items-center.px-2\.5.py-0\.5.rounded-full.text-xs.font-medium.bg-green-100 {
  background-color: #14532d !important;
  color: #86efac !important;
}

.theme-dark .inline-flex.items-center.px-2\.5.py-0\.5.rounded-full.text-xs.font-medium.bg-red-100 {
  background-color: #7f1d1d !important;
  color: #fca5a5 !important;
}

.theme-dark .inline-flex.items-center.px-2\.5.py-0\.5.rounded-full.text-xs.font-medium.bg-yellow-100 {
  background-color: #92400e !important;
  color: #fcd34d !important;
}

.theme-dark .inline-flex.items-center.px-2\.5.py-0\.5.rounded-full.text-xs.font-medium.bg-purple-100 {
  background-color: #581c87 !important;
  color: #d8b4fe !important;
}

.theme-dark .inline-flex.items-center.px-2\.5.py-0\.5.rounded-full.text-xs.font-medium.bg-indigo-100 {
  background-color: #312e81 !important;
  color: #c7d2fe !important;
}

.theme-dark .inline-flex.items-center.px-2\.5.py-0\.5.rounded-full.text-xs.font-medium.bg-teal-100 {
  background-color: #134e4a !important;
  color: #99f6e4 !important;
}

/* Dark theme - Badge classes */
.theme-dark .badge-success {
  background-color: #14532d !important;
  color: #86efac !important;
  border-color: #166534 !important;
}

.theme-dark .badge-secondary {
  background-color: #374151 !important;
  color: #f9fafb !important;
  border-color: #4b5563 !important;
}

.theme-dark .badge-primary {
  background-color: #1e3a8a !important;
  color: #93c5fd !important;
  border-color: #1d4ed8 !important;
}

.theme-dark .badge-warning {
  background-color: #92400e !important;
  color: #fcd34d !important;
  border-color: #d97706 !important;
}

.theme-dark .badge-danger {
  background-color: #7f1d1d !important;
  color: #fca5a5 !important;
  border-color: #dc2626 !important;
}

.theme-dark .badge-error {
  background-color: #7f1d1d !important;
  color: #fca5a5 !important;
  border-color: #dc2626 !important;
}

/* Dark theme - Flex containers with gaps */
.theme-dark .flex.flex-wrap.gap-2 > * {
  color: #f9fafb !important;
}

.theme-dark .flex.flex-wrap.gap-2 .badge {
  background-color: #374151 !important;
  color: #f9fafb !important;
  border-color: #4b5563 !important;
}

/* Dark theme - General badge fixes */
.theme-dark .badge {
  background-color: #374151 !important;
  color: #f9fafb !important;
  border-color: #4b5563 !important;
}

/* Table responsive fixes */
.overflow-x-auto {
  overflow-x: auto;
  max-width: 100%;
  -webkit-overflow-scrolling: touch;
  position: relative;
}

.overflow-x-auto table {
  border-collapse: collapse;
  table-layout: fixed;
  min-width: 900px; /* Minimum width for table */
}

/* Ensure page doesn't scroll horizontally */
body, html {
  overflow-x: hidden;
}

.page-container {
  max-width: 100vw;
  overflow-x: hidden;
}

/* RTL support for tables */
[dir="rtl"] .overflow-x-auto {
  direction: rtl;
}

/* Mobile responsive table */
@media (max-width: 768px) {
  .overflow-x-auto {
    margin: 0 -1rem;
    padding: 0 1rem;
  }

  .overflow-x-auto table {
    min-width: 800px;
  }
}

/* Table cell responsive widths */
.table-cell-name {
  min-width: 150px;
  max-width: 180px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.table-cell-email {
  min-width: 200px;
  max-width: 220px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.table-cell-company {
  min-width: 150px;
  max-width: 160px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.table-cell-category {
  min-width: 120px;
  max-width: 120px;
}
.table-cell-status {
  min-width: 100px;
  max-width: 100px;
}
.table-cell-actions {
  min-width: 80px;
  max-width: 80px;
}

.table-container::-webkit-scrollbar {
  height: 8px;
}

.table-container::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 4px;
}

.theme-dark .table-container::-webkit-scrollbar-track {
  background: #374151;
}

.table-container::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 4px;
}

.theme-dark .table-container::-webkit-scrollbar-thumb {
  background: #6b7280;
}

.table-container::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

.theme-dark .table-container::-webkit-scrollbar-thumb:hover {
  background: #9ca3af;
}

/* Ensure table doesn't break layout */
.table-wrapper {
  min-width: 0;
  width: 100%;
}

/* Responsive table cells */
.table-cell-responsive {
  min-width: 120px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.table-cell-responsive.wide {
  min-width: 200px;
}

.table-cell-responsive.narrow {
  min-width: 80px;
}

/* Page container fixes */
.page-container {
  max-width: 100vw;
  overflow-x: hidden;
}

/* Button group responsive */
.button-group-responsive {
  display: flex;
  flex-wrap: wrap;
  gap: 0.75rem;
  align-items: center;
}

@media (max-width: 768px) {
  .button-group-responsive {
    width: 100%;
    justify-content: stretch;
  }

  .button-group-responsive > button {
    flex: 1;
    min-width: 0;
  }
}

/* Sidebar layout fixes */
.sidebar {
  transition: all 0.3s ease;
  position: fixed;
  top: 0;
  left: 0;
  height: 100vh;
  z-index: 40;
  background: white;
  border-right: 1px solid #e5e7eb;
  flex-shrink: 0;
}

.theme-dark .sidebar {
  background: #1f2937;
  border-right: 1px solid #374151;
}

.sidebar.collapsed {
  width: 4rem;
}

.sidebar.expanded {
  width: 16rem;
}

/* Main content adjustment for sidebar */
.main-content {
  transition: margin-left 0.3s ease;
  min-height: 100vh;
  flex: 1;
  width: calc(100vw - 4rem); /* Default for collapsed sidebar */
}

.main-content.sidebar-collapsed {
  margin-left: 4rem;
  width: calc(100vw - 4rem);
}

.main-content.sidebar-expanded {
  margin-left: 16rem;
  width: calc(100vw - 16rem);
}

/* Fix for active sidebar item highlighting */
.sidebar-item.active {
  background-color: #3b82f6 !important;
  color: white !important;
}

.sidebar-item.active .sidebar-icon {
  color: white !important;
}

.sidebar-item.active .sidebar-text {
  color: white !important;
}

/* Prevent page overlay when sidebar is open */
.app-container {
  display: flex;
  min-height: 100vh;
}

.content-wrapper {
  flex: 1;
  overflow-x: hidden;
}
