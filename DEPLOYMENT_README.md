# 🚀 نظام التسويق الإلكتروني - دليل النشر السريع

## 📋 متطلبات النظام

- **Node.js**: 18.x أو أحدث
- **npm**: 9.x أو أحدث  
- **نظام التشغيل**: Ubuntu 20.04+ أو CentOS 8+ أو Windows Server 2019+
- **الذاكرة**: 2GB RAM كحد أدنى، 4GB مُوصى به
- **التخزين**: 20GB مساحة فارغة كحد أدنى

## ⚡ التثبيت السريع

### 1. تحضير الخادم
```bash
# تحديث النظام
sudo apt update && sudo apt upgrade -y

# تثبيت Node.js
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# تثبيت PM2
sudo npm install -g pm2

# تثبيت Nginx
sudo apt install nginx -y
```

### 2. رفع الملفات
```bash
# إنشاء مجلد التطبيق
sudo mkdir -p /var/www/marketing-email
sudo chown $USER:$USER /var/www/marketing-email
cd /var/www/marketing-email

# رفع ملفات المشروع (استبعد ملفات .md)
# ارفع جميع الملفات عدا:
# - *.md (ملفات الوثائق)
# - node_modules/
# - .git/
```

### 3. تثبيت التبعيات
```bash
# Backend
cd backend
npm install --production

# Frontend
cd ../frontend
npm install
npm run build
```

### 4. إعداد البيئة
```bash
cd /var/www/marketing-email/backend
cp .env.example .env
nano .env
```

**محتوى ملف .env:**
```env
PORT=3000
NODE_ENV=production
DB_PATH=./data/marketing.db
JWT_SECRET=your-super-secret-jwt-key-change-this
FRONTEND_URL=http://your-domain.com
```

### 5. تشغيل التطبيق
```bash
cd /var/www/marketing-email
pm2 start backend/server.js --name marketing-email
pm2 save
pm2 startup
```

### 6. إعداد Nginx
```bash
sudo nano /etc/nginx/sites-available/marketing-email
```

**محتوى ملف Nginx:**
```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    root /var/www/marketing-email/frontend/dist;
    index index.html;
    
    location / {
        try_files $uri $uri/ /index.html;
    }
    
    location /api/ {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}
```

```bash
sudo ln -s /etc/nginx/sites-available/marketing-email /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

## 🔒 إعداد SSL (اختياري)
```bash
sudo apt install snapd
sudo snap install --classic certbot
sudo certbot --nginx -d your-domain.com
```

## ✅ التحقق من التشغيل

1. **تحقق من PM2:**
   ```bash
   pm2 status
   ```

2. **تحقق من التطبيق:**
   ```bash
   curl http://localhost:3000/api/health
   ```

3. **تحقق من الموقع:**
   افتح المتصفح واذهب إلى `http://your-domain.com`

## 🔧 أوامر مفيدة

```bash
# عرض سجلات التطبيق
pm2 logs marketing-email

# إعادة تشغيل التطبيق
pm2 restart marketing-email

# إيقاف التطبيق
pm2 stop marketing-email

# حالة Nginx
sudo systemctl status nginx

# إعادة تحميل Nginx
sudo systemctl reload nginx
```

## 📞 الدعم

في حالة وجود مشاكل:
1. تحقق من سجلات PM2: `pm2 logs marketing-email`
2. تحقق من سجلات Nginx: `sudo tail -f /var/log/nginx/error.log`
3. تأكد من تشغيل جميع الخدمات: `pm2 status` و `sudo systemctl status nginx`

## 🎯 بيانات الدخول الافتراضية

- **اسم المستخدم**: `admin`
- **كلمة المرور**: `admin123`

⚠️ **مهم**: غير كلمة المرور فوراً بعد أول تسجيل دخول!

---

**تم النشر بنجاح! 🎉**
