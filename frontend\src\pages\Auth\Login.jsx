import React, { useState } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { useForm } from 'react-hook-form';
import { Mail, Lock, Eye, EyeOff, Globe } from 'lucide-react';
import { useAuth } from '../../contexts/AuthContext';
import { useTheme } from '../../contexts/ThemeContext';
import LoadingSpinner from '../../components/UI/LoadingSpinner';

const Login = () => {
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const { t, i18n } = useTranslation();
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const { login } = useAuth();
  const { setLanguage, getAvailableLanguages } = useTheme();

  // Get redirect URL from query params
  const redirectTo = searchParams.get('redirect') || '/dashboard';
  
  const {
    register,
    handleSubmit,
    formState: { errors },
    setError
  } = useForm({
    defaultValues: {
      username: '',
      password: '',
      rememberMe: false
    }
  });

  const isRTL = i18n.language === 'ar';
  const availableLanguages = getAvailableLanguages();

  const onSubmit = async (data) => {
    setIsLoading(true);
    
    try {
      const result = await login(data);
      
      if (result.success) {
        // Navigate to the originally requested page or dashboard
        navigate(result.redirectTo || redirectTo, { replace: true });
      } else {
        setError('root', {
          type: 'manual',
          message: result.error
        });
      }
    } catch (error) {
      setError('root', {
        type: 'manual',
        message: t('auth.login_error')
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleLanguageChange = (language) => {
    setLanguage(language);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        {/* Header */}
        <div className="text-center">
          {/* Logo */}
          <div className="mx-auto h-16 w-16 bg-primary-600 rounded-full flex items-center justify-center mb-6">
            <Mail className="h-8 w-8 text-white" />
          </div>
          
          {/* Title */}
          <h2 className="text-3xl font-bold text-gray-900 mb-2">
            {t('auth.login')}
          </h2>

          <p className="text-sm text-gray-600 mb-4">
            {isRTL
              ? 'نظام إدارة التسويق الإلكتروني'
              : 'Email Marketing Management System'
            }
          </p>

          {/* Redirect Notice */}
          {redirectTo !== '/dashboard' && (
            <div className="bg-blue-50 border border-blue-200 rounded-md p-3 mb-4">
              <p className="text-sm text-blue-800 text-center">
                {isRTL
                  ? 'سيتم توجيهك إلى الصفحة المطلوبة بعد تسجيل الدخول'
                  : 'You will be redirected to the requested page after login'
                }
              </p>
            </div>
          )}

          {/* Debug Info in Development */}
          {import.meta.env.DEV && (
            <div className="bg-yellow-50 border border-yellow-200 rounded-md p-3 mb-4">
              <p className="text-xs text-yellow-800 text-center mb-2">
                🔧 Development Mode
              </p>
              <div className="flex justify-center space-x-2 rtl:space-x-reverse">
                <button
                  type="button"
                  onClick={() => {
                    localStorage.clear();
                    window.location.reload();
                  }}
                  className="text-xs bg-yellow-200 hover:bg-yellow-300 px-2 py-1 rounded"
                >
                  Clear Storage
                </button>
                <button
                  type="button"
                  onClick={() => {
                    console.log('LocalStorage:', localStorage);
                    console.log('Token:', localStorage.getItem('token'));
                  }}
                  className="text-xs bg-blue-200 hover:bg-blue-300 px-2 py-1 rounded"
                >
                  Debug Info
                </button>
              </div>
            </div>
          )}
        </div>

        {/* Language Selector */}
        <div className="flex justify-center">
          <div className="flex items-center space-x-2 rtl:space-x-reverse bg-white rounded-lg p-1 shadow-sm border">
            {availableLanguages.map((lang) => (
              <button
                key={lang.value}
                onClick={() => handleLanguageChange(lang.value)}
                className={`
                  flex items-center space-x-1 rtl:space-x-reverse px-3 py-2 rounded-md text-sm font-medium transition-colors
                  ${i18n.language === lang.value
                    ? 'bg-primary-100 text-primary-700'
                    : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                  }
                `}
              >
                <span>{lang.flag}</span>
                <span>{lang.label}</span>
              </button>
            ))}
          </div>
        </div>

        {/* Login Form */}
        <div className="bg-white rounded-lg shadow-lg p-8">
          <form className="space-y-6" onSubmit={handleSubmit(onSubmit)}>
            {/* Username Field */}
            <div>
              <label htmlFor="username" className="block text-sm font-medium text-gray-700 mb-2">
                {t('auth.username')}
              </label>
              <div className="relative">
                <div className={`absolute inset-y-0 ${isRTL ? 'right-0 pr-4' : 'left-0 pl-4'} flex items-center pointer-events-none z-10`}>
                  <Mail className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  id="username"
                  type="text"
                  autoComplete="username"
                  className={`
                    input
                    ${isRTL ? 'pr-12 pl-6' : 'pl-12 pr-6'}
                    ${errors.username ? 'input-error' : ''}
                  `}
                  placeholder={t('auth.username')}
                  {...register('username', {
                    required: t('validation.required_field')
                  })}
                />
              </div>
              {errors.username && (
                <p className="mt-1 text-sm text-red-600">{errors.username.message}</p>
              )}
            </div>

            {/* Password Field */}
            <div>
              <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-2">
                {t('auth.password')}
              </label>
              <div className="relative">
                <div className={`absolute inset-y-0 ${isRTL ? 'right-0 pr-4' : 'left-0 pl-4'} flex items-center pointer-events-none z-10`}>
                  <Lock className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  id="password"
                  type={showPassword ? 'text' : 'password'}
                  autoComplete="current-password"
                  className={`
                    input
                    ${isRTL ? 'pr-12 pl-14' : 'pl-12 pr-14'}
                    ${errors.password ? 'input-error' : ''}
                  `}
                  placeholder={t('auth.password')}
                  {...register('password', {
                    required: t('validation.required_field')
                  })}
                />
                <button
                  type="button"
                  className={`absolute inset-y-0 ${isRTL ? 'left-0 pl-4' : 'right-0 pr-4'} flex items-center z-10 hover:bg-gray-50 rounded-md transition-colors`}
                  onClick={() => setShowPassword(!showPassword)}
                >
                  {showPassword ? (
                    <EyeOff className="h-5 w-5 text-gray-400 hover:text-gray-600" />
                  ) : (
                    <Eye className="h-5 w-5 text-gray-400 hover:text-gray-600" />
                  )}
                </button>
              </div>
              {errors.password && (
                <p className="mt-1 text-sm text-red-600">{errors.password.message}</p>
              )}
            </div>

            {/* Remember Me */}
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <input
                  id="rememberMe"
                  type="checkbox"
                  className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                  {...register('rememberMe')}
                />
                <label htmlFor="rememberMe" className={`${isRTL ? 'mr-2' : 'ml-2'} block text-sm text-gray-900`}>
                  {t('auth.remember_me')}
                </label>
              </div>

              <div className="text-sm">
                <a href="#" className="font-medium text-primary-600 hover:text-primary-500">
                  {t('auth.forgot_password')}
                </a>
              </div>
            </div>

            {/* Error Message */}
            {errors.root && (
              <div className="alert alert-error">
                <p>{errors.root.message}</p>
              </div>
            )}

            {/* Submit Button */}
            <div>
              <button
                type="submit"
                disabled={isLoading}
                className="w-full btn btn-primary flex items-center justify-center"
              >
                {isLoading ? (
                  <>
                    <LoadingSpinner size="small" className="mr-2" />
                    {t('common.loading')}
                  </>
                ) : (
                  t('auth.login')
                )}
              </button>
            </div>
          </form>

          {/* Demo Credentials */}
          <div className="mt-6 p-4 bg-gray-50 rounded-lg">
            <h4 className="text-sm font-medium text-gray-700 mb-2">
              {isRTL ? 'بيانات تجريبية:' : 'Demo Credentials:'}
            </h4>
            <div className="text-sm text-gray-600 space-y-1">
              <p><strong>{t('auth.username')}:</strong> admin</p>
              <p><strong>{t('auth.password')}:</strong> admin123</p>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="text-center text-sm text-gray-500">
          <p>
            {isRTL 
              ? '© 2024 نظام إدارة التسويق الإلكتروني. جميع الحقوق محفوظة.'
              : '© 2024 Email Marketing Management System. All rights reserved.'
            }
          </p>
        </div>
      </div>
    </div>
  );
};

export default Login;
