import React, { useState, useEffect, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { 
  Users, 
  MessageSquare, 
  TrendingUp, 
  Eye, 
  MousePointer, 
  Mail,
  Plus,
  Download,
  Calendar
} from 'lucide-react';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, BarChart, Bar, PieChart, Pie, Cell } from 'recharts';
import LoadingSpinner from '../../components/UI/LoadingSpinner';
import { apiHelpers, endpoints } from '../../services/api';

const Dashboard = () => {
  const { t, i18n } = useTranslation();
  const [loading, setLoading] = useState(false); // Start with false
  const [stats, setStats] = useState(null);
  const [timelineData, setTimelineData] = useState([]);
  const [recentActivity, setRecentActivity] = useState([]);
  const [error, setError] = useState(null);
  const [isRequestInProgress, setIsRequestInProgress] = useState(false); // Add request lock
  const requestTimeoutRef = useRef(null); // Add timeout ref
  const lastRequestTime = useRef(0); // Track last request time
  const [dateRange, setDateRange] = useState({
    from: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
    to: new Date().toISOString().split('T')[0]
  });

  const isRTL = i18n.language === 'ar';

  useEffect(() => {
    // Load data immediately on mount (only once)
    const timer = setTimeout(() => {
      loadDashboardData();
    }, 100); // Small delay to prevent race conditions

    return () => clearTimeout(timer);
  }, []); // Empty dependency array - only run once

  useEffect(() => {
    // Clear any existing timeout
    if (requestTimeoutRef.current) {
      clearTimeout(requestTimeoutRef.current);
    }

    // Debounce for date range changes with stronger protection
    requestTimeoutRef.current = setTimeout(() => {
      if (!loading && !isRequestInProgress) {
        loadDashboardData();
      }
    }, 3000); // 3 seconds debounce to prevent spam

    return () => {
      if (requestTimeoutRef.current) {
        clearTimeout(requestTimeoutRef.current);
      }
    };
  }, [dateRange]);

  const loadDashboardData = async () => {
    // Prevent multiple simultaneous requests with triple protection
    const now = Date.now();
    if (loading || isRequestInProgress || (now - lastRequestTime.current < 3000)) {
      console.log('⚠️ Dashboard already loading or too soon, skipping duplicate request');
      return;
    }

    try {
      setLoading(true);
      setIsRequestInProgress(true);
      lastRequestTime.current = now;
      setError(null);
      console.log('🔄 Loading dashboard data...');

      // Load data sequentially to prevent rate limiting
      console.log('📊 Loading dashboard data sequentially...');
      const timelineUrl = `${endpoints.reports.timeline}?interval=day&date_from=${dateRange.from}&date_to=${dateRange.to}`;

      // Load stats first
      const statsResult = await apiHelpers.get(endpoints.reports.dashboard);
      console.log('✅ Dashboard stats loaded');

      // Small delay between requests
      await new Promise(resolve => setTimeout(resolve, 200));

      // Load timeline data
      const timelineResult = await apiHelpers.get(timelineUrl);
      console.log('✅ Timeline data loaded');

      // Small delay between requests
      await new Promise(resolve => setTimeout(resolve, 200));

      // Load recent activity
      const activityResult = await apiHelpers.get(endpoints.reports.recentActivity, { params: { limit: 5 } });
      console.log('✅ Recent activity loaded');

      // Process results
      if (statsResult.success) {
        console.log('✅ Dashboard stats loaded');
        setStats(statsResult.data.data || statsResult.data);
      } else {
        console.error('❌ Failed to load dashboard stats:', statsResult);
      }

      if (timelineResult.success) {
        console.log('✅ Timeline data loaded');
        setTimelineData(timelineResult.data.data || timelineResult.data);
      }

      if (activityResult.success) {
        console.log('✅ Recent activity loaded');
        setRecentActivity(activityResult.data.data || activityResult.data);
      }

    } catch (error) {
      console.error('Error loading dashboard data:', error);
      setError(error.message);
    } finally {
      setLoading(false);
      setIsRequestInProgress(false); // Release lock
    }
  };

  // Show loading only if we have no data and are actually loading
  if (loading && !stats) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="large" text={t('common.loading')} />
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <div className="text-red-600 mb-4">{t('common.error')}</div>
        <button 
          onClick={loadDashboardData}
          className="btn btn-primary"
        >
          {t('common.refresh')}
        </button>
      </div>
    );
  }

  // Default empty data if API data is not available
  const mockStats = {
    clients: { total: 0, active: 0, byCategory: [], byLanguage: [], recent: 0 },
    messages: { total: 0, draft: 0, published: 0, sent: 0, byType: [], byLanguage: [] },
    interactions: { total: 0, sent: 0, opened: 0, clicked: 0, bounced: 0 },
    performance: { open_rate: 0, click_rate: 0, total_sent: 0, total_opened: 0, total_clicked: 0 }
  };

  const displayStats = stats || mockStats;

  // Generate complete date range with empty data for missing days
  const generateDateRange = (startDate, endDate) => {
    const dates = [];
    const current = new Date(startDate);
    const end = new Date(endDate);

    while (current <= end) {
      dates.push({
        period: current.toISOString().split('T')[0],
        sent: 0,
        opened: 0,
        clicked: 0,
        bounced: 0
      });
      current.setDate(current.getDate() + 1);
    }
    return dates;
  };

  const completeDateRange = generateDateRange(dateRange.from, dateRange.to);

  // Merge actual data with complete date range
  const displayTimelineData = completeDateRange.map(dateItem => {
    const actualData = timelineData.find(item => item.period === dateItem.period);
    return actualData || dateItem;
  });

  // Statistics cards data
  const statsCards = [
    {
      title: t('dashboard.total_clients'),
      value: displayStats.clients.total.toLocaleString(),
      change: `+${displayStats.clients.recent}`,
      changeType: 'positive',
      icon: Users,
      color: 'blue'
    },
    {
      title: t('dashboard.total_messages'),
      value: displayStats.messages.total.toLocaleString(),
      change: `+${displayStats.messages.recent || 0}`,
      changeType: 'positive',
      icon: MessageSquare,
      color: 'green'
    },
    {
      title: t('dashboard.open_rate'),
      value: `${displayStats.performance.open_rate}%`,
      change: '+0%',
      changeType: 'positive',
      icon: Eye,
      color: 'purple'
    },
    {
      title: t('dashboard.click_rate'),
      value: `${displayStats.performance.click_rate}%`,
      change: '+0%',
      changeType: 'positive',
      icon: MousePointer,
      color: 'orange'
    }
  ];

  // Pie chart data for client categories
  const categoryColors = {
    'general': '#3b82f6',
    'premium': '#10b981',
    'vip': '#f59e0b',
    'corporate': '#8b5cf6',
    'individual': '#ef4444',
    'owners': '#06b6d4'
  };

  const getCategoryName = (category) => {
    const categoryKey = category?.toLowerCase();
    switch (categoryKey) {
      case 'general': return t('clients.general');
      case 'premium': return t('clients.premium');
      case 'vip': return t('clients.vip');
      case 'corporate': return t('clients.corporate');
      case 'individual': return t('clients.individual');
      case 'owners': return t('clients.owners');
      default: return category || (isRTL ? 'غير محدد' : 'Unknown');
    }
  };

  const categoryData = displayStats.clients.byCategory?.map((cat, index) => ({
    name: getCategoryName(cat.category),
    value: cat.count,
    color: categoryColors[cat.category] || `hsl(${index * 60}, 70%, 50%)`
  })) || [];

  // Custom label component for better visibility
  const renderCustomLabel = ({ cx, cy, midAngle, innerRadius, outerRadius, percent, name }) => {
    const RADIAN = Math.PI / 180;
    const radius = innerRadius + (outerRadius - innerRadius) * 0.5;
    const x = cx + radius * Math.cos(-midAngle * RADIAN);
    const y = cy + radius * Math.sin(-midAngle * RADIAN);

    if (percent < 0.05) return null; // Don't show labels for very small slices

    return (
      <text
        x={x}
        y={y}
        fill="#000000"
        textAnchor={x > cx ? 'start' : 'end'}
        dominantBaseline="central"
        fontSize="12"
        fontWeight="bold"
        stroke="#ffffff"
        strokeWidth="0.5"
      >
        {`${name} ${(percent * 100).toFixed(0)}%`}
      </text>
    );
  };

  const quickActions = [
    {
      title: t('clients.add_client'),
      description: isRTL ? 'إضافة عميل جديد للنظام' : 'Add a new client to the system',
      icon: Users,
      href: '/clients/new',
      color: 'blue'
    },
    {
      title: t('messages.add_message'),
      description: isRTL ? 'إنشاء رسالة جديدة' : 'Create a new message',
      icon: MessageSquare,
      href: '/messages/new',
      color: 'green'
    },
    {
      title: t('reports.export_report'),
      description: isRTL ? 'تصدير التقارير' : 'Export reports',
      icon: Download,
      href: '/reports/comprehensive',
      color: 'purple'
    }
  ];

  return (
    <div className="space-y-8">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3 rtl:space-x-reverse">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">
              {t('dashboard.title')}
            </h1>
            <p className="mt-2 text-gray-600">
              {t('dashboard.welcome')}
            </p>
          </div>
          {loading && (
            <div className="flex items-center space-x-2 rtl:space-x-reverse text-blue-600">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
              <span className="text-sm">{isRTL ? 'جاري التحديث...' : 'Updating...'}</span>
            </div>
          )}
        </div>
        <div className="flex items-center space-x-3 rtl:space-x-reverse">
          <div className="flex items-center space-x-2 rtl:space-x-reverse bg-gray-50 p-2 rounded-lg">
            <Calendar className="h-4 w-4 text-gray-600" />
            <input
              type="date"
              className="input text-sm border-0 bg-transparent"
              value={dateRange.from}
              onChange={(e) => setDateRange(prev => ({ ...prev, from: e.target.value }))}
            />
            <span className="text-gray-500">-</span>
            <input
              type="date"
              className="input text-sm border-0 bg-transparent"
              value={dateRange.to}
              onChange={(e) => setDateRange(prev => ({ ...prev, to: e.target.value }))}
            />
          </div>
          <button className="btn btn-primary flex items-center space-x-2 rtl:space-x-reverse">
            <Download className="h-4 w-4" />
            <span>{t('reports.export_report')}</span>
          </button>
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {statsCards.map((card, index) => {
          const Icon = card.icon;
          return (
            <div key={index} className="card">
              <div className="card-body">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">
                      {card.title}
                    </p>
                    <p className="text-2xl font-bold text-gray-900 mt-2">
                      {card.value}
                    </p>
                    <div className="flex items-center mt-2">
                      <span className={`
                        text-sm font-medium
                        ${card.changeType === 'positive' ? 'text-green-600' : 'text-red-600'}
                      `}>
                        {card.change}
                      </span>
                      <span className="text-sm text-gray-500 ml-2">
                        {isRTL ? 'من الشهر الماضي' : 'from last month'}
                      </span>
                    </div>
                  </div>
                  <div className={`
                    h-12 w-12 rounded-lg flex items-center justify-center
                    ${card.color === 'blue' ? 'bg-blue-100' : ''}
                    ${card.color === 'green' ? 'bg-green-100' : ''}
                    ${card.color === 'purple' ? 'bg-purple-100' : ''}
                    ${card.color === 'orange' ? 'bg-orange-100' : ''}
                  `}>
                    <Icon className={`
                      h-6 w-6
                      ${card.color === 'blue' ? 'text-blue-600' : ''}
                      ${card.color === 'green' ? 'text-green-600' : ''}
                      ${card.color === 'purple' ? 'text-purple-600' : ''}
                      ${card.color === 'orange' ? 'text-orange-600' : ''}
                    `} />
                  </div>
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {/* Quick Actions */}
      <div className="card">
        <div className="card-header">
          <h3 className="text-lg font-medium text-gray-900">
            {t('dashboard.quick_actions')}
          </h3>
        </div>
        <div className="card-body">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {quickActions.map((action, index) => {
              const Icon = action.icon;
              return (
                <a
                  key={index}
                  href={action.href}
                  className="block p-4 border border-gray-200 rounded-lg hover:border-gray-300 hover:shadow-sm transition-all"
                >
                  <div className="flex items-center space-x-3 rtl:space-x-reverse">
                    <div className={`
                      h-10 w-10 rounded-lg flex items-center justify-center
                      ${action.color === 'blue' ? 'bg-blue-100' : ''}
                      ${action.color === 'green' ? 'bg-green-100' : ''}
                      ${action.color === 'purple' ? 'bg-purple-100' : ''}
                    `}>
                      <Icon className={`
                        h-5 w-5
                        ${action.color === 'blue' ? 'text-blue-600' : ''}
                        ${action.color === 'green' ? 'text-green-600' : ''}
                        ${action.color === 'purple' ? 'text-purple-600' : ''}
                      `} />
                    </div>
                    <div>
                      <h4 className="text-sm font-medium text-gray-900">
                        {action.title}
                      </h4>
                      <p className="text-xs text-gray-500 mt-1">
                        {action.description}
                      </p>
                    </div>
                  </div>
                </a>
              );
            })}
          </div>
        </div>
      </div>

      {/* Charts Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Performance Timeline */}
        <div className="card">
          <div className="card-header">
            <h3 className="text-lg font-medium text-gray-900">
              {t('dashboard.performance_chart')}
            </h3>
          </div>
          <div className="card-body">
            <ResponsiveContainer width="100%" height={300}>
              <LineChart data={displayTimelineData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis 
                  dataKey="period" 
                  tick={{ fontSize: 12 }}
                  tickFormatter={(value) => new Date(value).toLocaleDateString(isRTL ? 'ar-SA' : 'en-US', { month: 'short', day: 'numeric' })}
                />
                <YAxis tick={{ fontSize: 12 }} />
                <Tooltip 
                  labelFormatter={(value) => new Date(value).toLocaleDateString(isRTL ? 'ar-SA' : 'en-US')}
                  formatter={(value, name) => [
                    value,
                    name === 'sent' ? (isRTL ? 'مرسل' : 'Sent') :
                    name === 'opened' ? (isRTL ? 'مفتوح' : 'Opened') :
                    name === 'clicked' ? (isRTL ? 'منقور' : 'Clicked') : name
                  ]}
                />
                <Line type="monotone" dataKey="sent" stroke="#3b82f6" strokeWidth={2} />
                <Line type="monotone" dataKey="opened" stroke="#10b981" strokeWidth={2} />
                <Line type="monotone" dataKey="clicked" stroke="#f59e0b" strokeWidth={2} />
              </LineChart>
            </ResponsiveContainer>
          </div>
        </div>

        {/* Client Categories */}
        <div className="card">
          <div className="card-header">
            <h3 className="text-lg font-medium text-gray-900">
              {isRTL ? 'توزيع العملاء حسب الفئة' : 'Client Distribution by Category'}
            </h3>
          </div>
          <div className="card-body">
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={categoryData}
                  cx="50%"
                  cy="50%"
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                  label={renderCustomLabel}
                  labelLine={false}
                >
                  {categoryData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          </div>
        </div>
      </div>

      {/* Recent Activity */}
      <div className="card">
        <div className="card-header">
          <h3 className="text-lg font-medium text-gray-900">
            {t('dashboard.recent_activity')}
          </h3>
        </div>
        <div className="card-body">
          <div className="space-y-4">
            {recentActivity.length === 0 ? (
              <p className="text-gray-500 text-center py-4">
                {isRTL ? 'لا يوجد نشاط حديث' : 'No recent activity'}
              </p>
            ) : (
              recentActivity.map((activity, index) => {
                const getActivityIcon = (type) => {
                  switch (type) {
                    case 'message_sent': return Mail;
                    case 'client_added': return Users;
                    default: return TrendingUp;
                  }
                };

                const getActivityColor = (type) => {
                  switch (type) {
                    case 'message_sent': return 'green';
                    case 'client_added': return 'blue';
                    default: return 'purple';
                  }
                };

                const getActivityText = (activity) => {
                  switch (activity.type) {
                    case 'message_sent':
                      return {
                        action: isRTL ? 'تم إرسال رسالة' : 'Message sent',
                        details: activity.details
                      };
                    case 'client_added':
                      return {
                        action: isRTL ? 'تم إضافة عميل جديد' : 'New client added',
                        details: activity.details
                      };
                    default:
                      return {
                        action: activity.title,
                        details: activity.details
                      };
                  }
                };

                const activityText = getActivityText(activity);
                const Icon = getActivityIcon(activity.type);
                const color = getActivityColor(activity.type);

                return (
                  <div key={index} className="flex items-center space-x-3 rtl:space-x-reverse">
                    <div className={`
                      h-8 w-8 rounded-full flex items-center justify-center
                      ${color === 'green' ? 'bg-green-100' : ''}
                      ${color === 'blue' ? 'bg-blue-100' : ''}
                      ${color === 'purple' ? 'bg-purple-100' : ''}
                    `}>
                      <Icon className={`
                        h-4 w-4
                        ${color === 'green' ? 'text-green-600' : ''}
                        ${color === 'blue' ? 'text-blue-600' : ''}
                        ${color === 'purple' ? 'text-purple-600' : ''}
                      `} />
                    </div>
                    <div className="flex-1">
                      <p className="text-sm font-medium text-gray-900">
                        {activityText.action}
                      </p>
                      <p className="text-sm text-gray-500">
                        {activityText.details}
                      </p>
                    </div>
                    <span className="text-xs text-gray-400">
                      {new Date(activity.created_at).toLocaleString(isRTL ? 'ar-SA' : 'en-US', {
                        hour: '2-digit',
                        minute: '2-digit',
                        day: 'numeric',
                        month: 'short'
                      })}
                    </span>
                  </div>
                );
              })
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
