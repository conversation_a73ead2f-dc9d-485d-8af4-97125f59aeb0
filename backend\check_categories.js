const { db } = require('./config/database');

// Check client categories table structure
db.all("PRAGMA table_info(client_categories)", [], (err, columns) => {
  if (err) {
    console.error('Error:', err);
    return;
  }
  
  console.log('📋 Client categories table structure:');
  columns.forEach(col => {
    console.log(`  - ${col.name}: ${col.type}`);
  });
  
  // Check current data
  db.all("SELECT * FROM client_categories LIMIT 5", [], (err, rows) => {
    if (err) {
      console.error('Error:', err);
      return;
    }
    
    console.log('\n📂 Current categories data:');
    rows.forEach(row => {
      console.log('  -', row);
    });
    
    process.exit(0);
  });
});
