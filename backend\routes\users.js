const express = require('express');
const bcrypt = require('bcryptjs');
const { authenticateToken } = require('./auth');
const db = require('../config/database').db;

const router = express.Router();

// Middleware to check admin permissions
const requireAdmin = (req, res, next) => {
  if (req.user.role !== 'admin') {
    return res.status(403).json({
      success: false,
      error: 'Access denied. Admin privileges required.',
      message_ar: 'تم رفض الوصول. مطلوب صلاحيات المدير.'
    });
  }
  next();
};

// Get all users (admin only)
router.get('/', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const { page = 1, limit = 10, search = '' } = req.query;
    const offset = (page - 1) * limit;

    let whereClause = 'WHERE 1=1';
    const params = [];

    if (search) {
      whereClause += ' AND (name LIKE ? OR username LIKE ? OR email LIKE ?)';
      params.push(`%${search}%`, `%${search}%`, `%${search}%`);
    }

    // Get users
    const users = await new Promise((resolve, reject) => {
      db.all(
        `SELECT id, username, email, name, phone, role, created_at, updated_at
         FROM users ${whereClause}
         ORDER BY created_at DESC
         LIMIT ? OFFSET ?`,
        [...params, limit, offset],
        (err, rows) => {
          if (err) reject(err);
          else resolve(rows);
        }
      );
    });

    // Get total count
    const totalResult = await new Promise((resolve, reject) => {
      db.get(
        `SELECT COUNT(*) as total FROM users ${whereClause}`,
        params,
        (err, row) => {
          if (err) reject(err);
          else resolve(row);
        }
      );
    });

    res.json({
      success: true,
      data: {
        users,
        total: totalResult.total,
        page: parseInt(page),
        limit: parseInt(limit),
        pages: Math.ceil(totalResult.total / limit)
      },
      message: 'Users retrieved successfully',
      message_ar: 'تم استرجاع المستخدمين بنجاح'
    });

  } catch (error) {
    console.error('Error fetching users:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch users',
      message_ar: 'فشل في استرجاع المستخدمين'
    });
  }
});

// Create new user (admin only)
router.post('/', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const { username, email, name, phone, role, password, permissions } = req.body;

    // Validate required fields
    if (!username || !email || !name || !role || !password) {
      return res.status(400).json({
        success: false,
        error: 'Username, email, name, role, and password are required',
        message_ar: 'اسم المستخدم والبريد الإلكتروني والاسم والدور وكلمة المرور مطلوبة'
      });
    }

    // Check if user already exists
    const existingUser = await new Promise((resolve, reject) => {
      db.get(
        'SELECT id FROM users WHERE username = ? OR email = ?',
        [username, email],
        (err, row) => {
          if (err) reject(err);
          else resolve(row);
        }
      );
    });

    if (existingUser) {
      return res.status(400).json({
        success: false,
        error: 'Username or email already exists',
        message_ar: 'اسم المستخدم أو البريد الإلكتروني موجود بالفعل'
      });
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(password, 10);

    // Create user
    const result = await new Promise((resolve, reject) => {
      db.run(
        `INSERT INTO users (username, email, name, phone, role, password, permissions, created_at, updated_at)
         VALUES (?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)`,
        [username, email, name, phone || null, role, hashedPassword, JSON.stringify(permissions || [])],
        function(err) {
          if (err) reject(err);
          else resolve({ id: this.lastID });
        }
      );
    });

    // Get created user
    const newUser = await new Promise((resolve, reject) => {
      db.get(
        'SELECT id, username, email, name, phone, role, created_at FROM users WHERE id = ?',
        [result.id],
        (err, row) => {
          if (err) reject(err);
          else resolve(row);
        }
      );
    });

    res.status(201).json({
      success: true,
      data: newUser,
      message: 'User created successfully',
      message_ar: 'تم إنشاء المستخدم بنجاح'
    });

  } catch (error) {
    console.error('Error creating user:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to create user',
      message_ar: 'فشل في إنشاء المستخدم'
    });
  }
});

// Update user (admin only)
router.put('/:id', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const { id } = req.params;
    const { username, email, name, phone, role, permissions } = req.body;

    // Validate required fields
    if (!username || !email || !name || !role) {
      return res.status(400).json({
        success: false,
        error: 'Username, email, name, and role are required',
        message_ar: 'اسم المستخدم والبريد الإلكتروني والاسم والدور مطلوبة'
      });
    }

    // Check if user exists
    const existingUser = await new Promise((resolve, reject) => {
      db.get('SELECT id FROM users WHERE id = ?', [id], (err, row) => {
        if (err) reject(err);
        else resolve(row);
      });
    });

    if (!existingUser) {
      return res.status(404).json({
        success: false,
        error: 'User not found',
        message_ar: 'المستخدم غير موجود'
      });
    }

    // Check if username or email already exists (excluding current user)
    const duplicateUser = await new Promise((resolve, reject) => {
      db.get(
        'SELECT id FROM users WHERE (username = ? OR email = ?) AND id != ?',
        [username, email, id],
        (err, row) => {
          if (err) reject(err);
          else resolve(row);
        }
      );
    });

    if (duplicateUser) {
      return res.status(400).json({
        success: false,
        error: 'Username or email already exists',
        message_ar: 'اسم المستخدم أو البريد الإلكتروني موجود بالفعل'
      });
    }

    // Update user
    await new Promise((resolve, reject) => {
      db.run(
        `UPDATE users SET
          username = ?,
          email = ?,
          name = ?,
          phone = ?,
          role = ?,
          permissions = ?,
          updated_at = CURRENT_TIMESTAMP
         WHERE id = ?`,
        [username, email, name, phone || null, role, JSON.stringify(permissions || []), id],
        function(err) {
          if (err) reject(err);
          else resolve();
        }
      );
    });

    // Get updated user
    const updatedUser = await new Promise((resolve, reject) => {
      db.get(
        'SELECT id, username, email, name, phone, role, created_at, updated_at FROM users WHERE id = ?',
        [id],
        (err, row) => {
          if (err) reject(err);
          else resolve(row);
        }
      );
    });

    res.json({
      success: true,
      data: updatedUser,
      message: 'User updated successfully',
      message_ar: 'تم تحديث المستخدم بنجاح'
    });

  } catch (error) {
    console.error('Error updating user:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to update user',
      message_ar: 'فشل في تحديث المستخدم'
    });
  }
});

// Delete user (admin only)
router.delete('/:id', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const { id } = req.params;

    // Prevent deleting self
    if (parseInt(id) === req.user.id) {
      return res.status(400).json({
        success: false,
        error: 'Cannot delete your own account',
        message_ar: 'لا يمكن حذف حسابك الخاص'
      });
    }

    // Check if user exists
    const existingUser = await new Promise((resolve, reject) => {
      db.get('SELECT id FROM users WHERE id = ?', [id], (err, row) => {
        if (err) reject(err);
        else resolve(row);
      });
    });

    if (!existingUser) {
      return res.status(404).json({
        success: false,
        error: 'User not found',
        message_ar: 'المستخدم غير موجود'
      });
    }

    // Delete user
    await new Promise((resolve, reject) => {
      db.run('DELETE FROM users WHERE id = ?', [id], function(err) {
        if (err) reject(err);
        else resolve();
      });
    });

    res.json({
      success: true,
      message: 'User deleted successfully',
      message_ar: 'تم حذف المستخدم بنجاح'
    });

  } catch (error) {
    console.error('Error deleting user:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to delete user',
      message_ar: 'فشل في حذف المستخدم'
    });
  }
});

module.exports = router;
