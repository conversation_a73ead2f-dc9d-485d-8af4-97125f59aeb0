import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { 
  Bell, 
  Search, 
  User, 
  LogOut, 
  Settings, 
  Globe,
  Menu,
  X
} from 'lucide-react';
import { useAuth } from '../../contexts/AuthContext';
import { useTheme } from '../../contexts/ThemeContext';

const Header = () => {
  const [showUserMenu, setShowUserMenu] = useState(false);
  const [showNotifications, setShowNotifications] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  
  const { t, i18n } = useTranslation();
  const navigate = useNavigate();
  const { user, logout } = useAuth();
  const { setLanguage, getAvailableLanguages } = useTheme();
  
  const isRTL = i18n.language === 'ar';
  const availableLanguages = getAvailableLanguages();

  const handleLogout = async () => {
    await logout();
    navigate('/login');
  };

  const handleLanguageChange = (language) => {
    setLanguage(language);
    setShowUserMenu(false);
  };

  const handleSearch = (e) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      // Implement search functionality
      console.log('Search:', searchQuery);
    }
  };

  // Mock notifications
  const notifications = [
    {
      id: 1,
      title: isRTL ? 'رسالة جديدة' : 'New Message',
      message: isRTL ? 'تم إرسال رسالة جديدة بنجاح' : 'New message sent successfully',
      time: '5 min ago',
      unread: true
    },
    {
      id: 2,
      title: isRTL ? 'عميل جديد' : 'New Client',
      message: isRTL ? 'تم إضافة عميل جديد' : 'New client has been added',
      time: '1 hour ago',
      unread: true
    },
    {
      id: 3,
      title: isRTL ? 'تقرير شهري' : 'Monthly Report',
      message: isRTL ? 'تقرير الشهر الماضي جاهز' : 'Last month report is ready',
      time: '2 hours ago',
      unread: false
    }
  ];

  const unreadCount = notifications.filter(n => n.unread).length;

  return (
    <header className="bg-white shadow-sm border-b border-gray-200 h-16 flex items-center justify-between px-4 sm:px-6 lg:px-8">
      {/* Search Bar */}
      <div className="flex-1 max-w-lg">
        <form onSubmit={handleSearch} className="relative">
          <div className={`absolute inset-y-0 ${isRTL ? 'right-0 pr-3' : 'left-0 pl-3'} flex items-center pointer-events-none`}>
            <Search className="h-5 w-5 text-gray-400" />
          </div>
          <input
            type="text"
            placeholder={t('common.search')}
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className={`
              block w-full ${isRTL ? 'pr-10 pl-3' : 'pl-10 pr-3'} py-2 border border-gray-300 rounded-md 
              leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 
              focus:ring-1 focus:ring-primary-500 focus:border-primary-500 sm:text-sm
            `}
          />
        </form>
      </div>

      {/* Right Side Actions */}
      <div className="flex items-center space-x-4 rtl:space-x-reverse">
        {/* Language Selector */}
        <div className="relative">
          <button
            onClick={() => setShowUserMenu(!showUserMenu)}
            className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-md transition-colors"
          >
            <Globe className="h-5 w-5" />
          </button>
        </div>

        {/* Notifications */}
        <div className="relative">
          <button
            onClick={() => setShowNotifications(!showNotifications)}
            className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-md transition-colors relative"
          >
            <Bell className="h-5 w-5" />
            {unreadCount > 0 && (
              <span className="absolute -top-1 -right-1 h-4 w-4 bg-red-500 text-white text-xs rounded-full flex items-center justify-center">
                {unreadCount}
              </span>
            )}
          </button>

          {/* Notifications Dropdown */}
          {showNotifications && (
            <div className={`
              absolute ${isRTL ? 'left-0' : 'right-0'} mt-2 w-80 bg-white rounded-md shadow-lg ring-1 ring-black ring-opacity-5 z-50
            `}>
              <div className="p-4 border-b border-gray-200">
                <h3 className="text-sm font-medium text-gray-900">
                  {t('common.notifications')} ({unreadCount})
                </h3>
              </div>
              <div className="max-h-64 overflow-y-auto">
                {notifications.map((notification) => (
                  <div
                    key={notification.id}
                    className={`
                      p-4 border-b border-gray-100 hover:bg-gray-50 cursor-pointer
                      ${notification.unread ? 'bg-blue-50' : ''}
                    `}
                  >
                    <div className="flex justify-between items-start">
                      <div className="flex-1">
                        <p className="text-sm font-medium text-gray-900">
                          {notification.title}
                        </p>
                        <p className="text-sm text-gray-600 mt-1">
                          {notification.message}
                        </p>
                      </div>
                      <span className="text-xs text-gray-500 ml-2">
                        {notification.time}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
              <div className="p-4 border-t border-gray-200">
                <button className="text-sm text-primary-600 hover:text-primary-700 font-medium">
                  {isRTL ? 'عرض جميع الإشعارات' : 'View all notifications'}
                </button>
              </div>
            </div>
          )}
        </div>

        {/* User Menu */}
        <div className="relative">
          <button
            onClick={() => setShowUserMenu(!showUserMenu)}
            className="flex items-center space-x-2 rtl:space-x-reverse p-2 text-gray-700 hover:bg-gray-100 rounded-md transition-colors"
          >
            <div className="h-8 w-8 bg-primary-600 rounded-full flex items-center justify-center">
              <User className="h-5 w-5 text-white" />
            </div>
            <div className="hidden md:block text-left rtl:text-right">
              <p className="text-sm font-medium text-gray-900">
                {user?.name || user?.username || 'User'}
              </p>
              <p className="text-xs text-gray-500">
                {user?.role || 'Admin'}
              </p>
            </div>
          </button>

          {/* User Dropdown */}
          {showUserMenu && (
            <div className={`
              absolute ${isRTL ? 'left-0' : 'right-0'} mt-2 w-56 bg-white rounded-md shadow-lg ring-1 ring-black ring-opacity-5 z-50
            `}>
              <div className="p-4 border-b border-gray-200">
                <p className="text-sm font-medium text-gray-900">
                  {user?.name || user?.username}
                </p>
                <p className="text-sm text-gray-500">
                  {user?.email}
                </p>
              </div>

              {/* Language Options */}
              <div className="p-2 border-b border-gray-200">
                <p className="text-xs font-medium text-gray-500 uppercase tracking-wide px-2 py-1">
                  {t('common.language')}
                </p>
                {availableLanguages.map((lang) => (
                  <button
                    key={lang.value}
                    onClick={() => handleLanguageChange(lang.value)}
                    className={`
                      w-full flex items-center space-x-2 rtl:space-x-reverse px-2 py-2 text-sm rounded-md transition-colors
                      ${i18n.language === lang.value
                        ? 'bg-primary-100 text-primary-700'
                        : 'text-gray-700 hover:bg-gray-100'
                      }
                    `}
                  >
                    <span>{lang.flag}</span>
                    <span>{lang.label}</span>
                  </button>
                ))}
              </div>

              {/* Menu Items */}
              <div className="p-2">
                <button
                  onClick={() => {
                    navigate('/profile');
                    setShowUserMenu(false);
                  }}
                  className="w-full flex items-center space-x-2 rtl:space-x-reverse px-2 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-md transition-colors"
                >
                  <User className="h-4 w-4" />
                  <span>{t('navigation.profile')}</span>
                </button>

                <button
                  onClick={() => {
                    navigate('/settings');
                    setShowUserMenu(false);
                  }}
                  className="w-full flex items-center space-x-2 rtl:space-x-reverse px-2 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-md transition-colors"
                >
                  <Settings className="h-4 w-4" />
                  <span>{t('navigation.settings')}</span>
                </button>

                <hr className="my-2" />

                <button
                  onClick={handleLogout}
                  className="w-full flex items-center space-x-2 rtl:space-x-reverse px-2 py-2 text-sm text-red-600 hover:bg-red-50 rounded-md transition-colors"
                >
                  <LogOut className="h-4 w-4" />
                  <span>{t('navigation.logout')}</span>
                </button>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Click outside to close dropdowns */}
      {(showUserMenu || showNotifications) && (
        <div
          className="fixed inset-0 z-40"
          onClick={() => {
            setShowUserMenu(false);
            setShowNotifications(false);
          }}
        />
      )}
    </header>
  );
};

export default Header;
