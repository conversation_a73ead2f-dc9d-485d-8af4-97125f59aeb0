import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useParams, useNavigate } from 'react-router-dom';
import { 
  Save, 
  ArrowLeft, 
  ArrowRight,
  Mail,
  Users,
  Eye
} from 'lucide-react';
import { toast } from 'react-hot-toast';
import apiHelpers from '../../utils/apiHelpers';
import LoadingSpinner from '../../components/UI/LoadingSpinner';

const EditMessage = () => {
  const { t, i18n } = useTranslation();
  const isRTL = i18n.language === 'ar';
  const { id } = useParams();
  const navigate = useNavigate();

  const [loading, setLoading] = useState(false);
  const [dataLoading, setDataLoading] = useState(true);
  const [message, setMessage] = useState(null);
  const [templates, setTemplates] = useState([]);
  const [clients, setClients] = useState([]);
  const [categories, setCategories] = useState([]);
  const [showPreview, setShowPreview] = useState(false);
  const [previewHtml, setPreviewHtml] = useState('');

  const [formData, setFormData] = useState({
    title: '',
    title_ar: '',
    subject: '',
    subject_ar: '',
    content: '',
    content_ar: '',
    template_id: '',
    type: 'one_time',
    campaign_type: 'manual',
    scheduled_at: '',
    recurring_frequency: '',
    target_criteria: {
      all_clients: true,
      categories: [],
      status: [],
      interest_levels: [],
      custom_filter: ''
    },
    variables: {}
  });

  const [targetedClients, setTargetedClients] = useState([]);
  const [clientsCount, setClientsCount] = useState(0);

  useEffect(() => {
    if (id) {
      loadInitialData();
    }
  }, [id]);

  const loadInitialData = async () => {
    setDataLoading(true);
    try {
      await Promise.all([
        fetchMessage(),
        fetchTemplates(),
        fetchClients(),
        fetchCategories()
      ]);
    } catch (error) {
      console.error('Error loading initial data:', error);
    } finally {
      setDataLoading(false);
    }
  };

  const loadExistingTargetClients = async (clientIds) => {
    try {
      console.log('🔍 Loading existing target clients for editing:', clientIds);

      // Load client details for the target clients
      const clientPromises = clientIds.map(async (clientId) => {
        try {
          const response = await apiHelpers.get(`/clients/${clientId}`);
          if (response.success) {
            return response.data;
          }
          return null;
        } catch (error) {
          console.error(`Error loading client ${clientId}:`, error);
          return null;
        }
      });

      const clients = await Promise.all(clientPromises);
      const validClients = clients.filter(client => client !== null);

      console.log(`✅ Loaded ${validClients.length} existing clients for editing`);

      // Set the targeted clients and count
      setTargetedClients(validClients);
      setClientsCount(validClients.length);

    } catch (error) {
      console.error('Error loading existing target clients:', error);
    }
  };

  const fetchMessage = async () => {
    try {
      // Try to fetch the actual message from API
      const response = await apiHelpers.get(`/messages/${id}`);

      let messageData;
      if (response.success && response.data) {
        // Use actual message data
        messageData = response.data;
      } else {
        // Fallback to mock data if API fails
        messageData = {
          id: id,
          title: `Message ${id}`,
          title_ar: `رسالة ${id}`,
          subject: 'Email Subject',
          subject_ar: 'موضوع البريد',
          content: 'Message content here...',
          content_ar: 'محتوى الرسالة هنا...',
          template_id: '',
          type: 'one_time',
          campaign_type: 'manual',
          scheduled_at: '',
          recurring_frequency: '',
          target_criteria: {
            all_clients: true,
            categories: [],
            status: [],
            interest_levels: []
          },
          variables: {},
          status: 'draft'
        };
      }

      console.log('📝 Loading message data for editing:', {
        id: messageData.id,
        template_id: messageData.template_id,
        target_criteria: messageData.target_criteria,
        target_clients: messageData.target_clients ? messageData.target_clients.length : 0
      });

      setMessage(messageData);
      setFormData({
        title: messageData.title || '',
        title_ar: messageData.title_ar || '',
        subject: messageData.subject || '',
        subject_ar: messageData.subject_ar || '',
        content: messageData.content || '',
        content_ar: messageData.content_ar || '',
        template_id: String(messageData.template_id || ''), // Ensure string type
        type: messageData.type || 'one_time',
        campaign_type: messageData.campaign_type || 'manual',
        scheduled_at: messageData.scheduled_at || '',
        recurring_frequency: messageData.recurring_frequency || '',
        target_criteria: messageData.target_criteria || {
          all_clients: true,
          categories: [],
          status: [],
          interest_levels: []
        },
        variables: messageData.variables || {},
        status: messageData.status || 'draft'
      });

      // Load existing target clients if they exist
      if (messageData.target_clients && Array.isArray(messageData.target_clients) && messageData.target_clients.length > 0) {
        console.log(`📋 Loading existing ${messageData.target_clients.length} target clients for editing`);
        await loadExistingTargetClients(messageData.target_clients);
      }
    } catch (error) {
      console.error('Error fetching message:', error);
      toast.error(
        isRTL 
          ? 'فشل في تحميل بيانات الرسالة' 
          : 'Failed to load message data'
      );
      navigate('/messages');
    }
  };

  const fetchTemplates = async () => {
    try {
      if (templates.length > 0) return; // Prevent duplicate calls
      const response = await apiHelpers.get('/email-templates');
      if (response.success) {
        setTemplates(response.data);
      }
    } catch (error) {
      console.error('Error fetching templates:', error);
    }
  };

  const fetchClients = async () => {
    try {
      if (clients.length > 0) return; // Prevent duplicate calls
      const response = await apiHelpers.get('/clients?limit=1000');
      if (response.success) {
        setClients(response.data.clients || []);
        console.log(`📋 Loaded ${response.data.clients?.length || 0} clients for editing`);
      }
    } catch (error) {
      console.error('Error fetching clients:', error);
    }
  };

  const fetchCategories = async () => {
    try {
      if (categories.length > 0) return; // Prevent duplicate calls
      const response = await apiHelpers.get('/client-categories');
      if (response.success) {
        setCategories(response.data);
      }
    } catch (error) {
      console.error('Error fetching categories:', error);
    }
  };

  const calculateTargetedClients = () => {
    console.log('🎯 Calculating targeted clients in edit mode:', {
      totalClients: clients.length,
      criteria: formData.target_criteria
    });

    let filtered = clients.filter(client => !client.unsubscribed);
    console.log(`📊 After filtering unsubscribed: ${filtered.length} clients`);

    if (!formData.target_criteria.all_clients) {
      console.log('🔍 Applying specific targeting criteria...');
      if (formData.target_criteria.categories.length > 0) {
        filtered = filtered.filter(client => 
          formData.target_criteria.categories.includes(client.category)
        );
      }

      if (formData.target_criteria.status.length > 0) {
        filtered = filtered.filter(client => 
          formData.target_criteria.status.includes(client.status)
        );
      }

      if (formData.target_criteria.interest_levels.length > 0) {
        filtered = filtered.filter(client => 
          formData.target_criteria.interest_levels.includes(client.interest_level)
        );
      }
    } else {
      console.log('✅ Using all clients (all_clients = true)');
    }

    console.log(`🎯 Final targeted clients in edit mode: ${filtered.length}`);
    setTargetedClients(filtered);
    setClientsCount(filtered.length);
  };

  useEffect(() => {
    if (formData.target_criteria && clients.length > 0) {
      const timeoutId = setTimeout(() => {
        calculateTargetedClients();
      }, 300);
      
      return () => clearTimeout(timeoutId);
    }
  }, [formData.target_criteria, clients]);

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (loading) return;
    
    try {
      setLoading(true);

      const payload = {
        ...formData,
        target_clients: targetedClients.map(c => c.id),
        total_recipients: clientsCount
      };

      const response = await apiHelpers.put(`/messages/${id}`, payload);

      if (response.success) {
        toast.success(
          isRTL 
            ? 'تم تحديث الرسالة بنجاح' 
            : 'Message updated successfully'
        );
        navigate('/messages');
      }
    } catch (error) {
      console.error('Error updating message:', error);
      toast.error(
        isRTL 
          ? 'فشل في تحديث الرسالة' 
          : 'Failed to update message'
      );
    } finally {
      setLoading(false);
    }
  };

  const handlePreview = async () => {
    try {
      if (!formData.template_id) {
        toast.error(isRTL ? 'يرجى اختيار قالب أولاً' : 'Please select a template first');
        return;
      }

      const selectedTemplate = templates.find(t => t.id == formData.template_id);
      if (!selectedTemplate) return;

      const previewVariables = {
        direction: isRTL ? 'rtl' : 'ltr',
        language: isRTL ? 'ar' : 'en',
        subject: isRTL ? (formData.subject_ar || formData.subject) : formData.subject,
        company_name: isRTL ? 'اسم الشركة' : 'Company Name',
        title: isRTL ? (formData.title_ar || formData.title) : formData.title,
        greeting: isRTL ? 'مرحباً' : 'Hello',
        client_name: isRTL ? 'اسم العميل' : 'Client Name',
        content: isRTL ? (formData.content_ar || formData.content) : formData.content,
        company_address: isRTL ? 'عنوان الشركة' : 'Company Address',
        unsubscribe_url: '#unsubscribe',
        unsubscribe_text: isRTL ? 'إلغاء الاشتراك' : 'Unsubscribe',
        tracking_pixel: '/api/tracking/pixel/preview',
        ...formData.variables
      };

      const response = await apiHelpers.post(`/email-templates/${formData.template_id}/preview`, {
        variables: previewVariables
      });

      if (response.success) {
        setPreviewHtml(response.data.html);
        setShowPreview(true);
      }
    } catch (error) {
      console.error('Error previewing message:', error);
      toast.error(
        isRTL 
          ? 'فشل في معاينة الرسالة' 
          : 'Failed to preview message'
      );
    }
  };

  if (dataLoading) {
    return (
      <div className="min-h-screen bg-gray-50 p-6">
        <div className="max-w-4xl mx-auto">
          <div className="flex items-center justify-center py-20">
            <LoadingSpinner size="lg" />
            <span className="ml-3 rtl:mr-3 rtl:ml-0 text-gray-600">
              {isRTL ? 'جاري تحميل البيانات...' : 'Loading data...'}
            </span>
          </div>
        </div>
      </div>
    );
  }

  if (!message) {
    return (
      <div className="min-h-screen bg-gray-50 p-6">
        <div className="max-w-4xl mx-auto">
          <div className="text-center py-20">
            <Mail className="h-12 w-12 mx-auto text-gray-300 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              {isRTL ? 'الرسالة غير موجودة' : 'Message not found'}
            </h3>
            <p className="text-gray-500 mb-6">
              {isRTL 
                ? 'لم يتم العثور على الرسالة المطلوبة'
                : 'The requested message could not be found'
              }
            </p>
            <button
              onClick={() => navigate('/messages')}
              className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
            >
              {isRTL ? <ArrowRight className="h-4 w-4 ml-2" /> : <ArrowLeft className="h-4 w-4 mr-2" />}
              {isRTL ? 'العودة للرسائل' : 'Back to Messages'}
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="w-full">
        {/* Header */}
        <div className="px-6 py-4 bg-white border-b border-gray-200">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
            <div className="flex items-center space-x-4 rtl:space-x-reverse">
              <button
                onClick={() => navigate('/messages')}
                className="p-2 text-gray-400 hover:text-gray-600"
              >
                {isRTL ? <ArrowRight className="h-5 w-5" /> : <ArrowLeft className="h-5 w-5" />}
              </button>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">
                  {isRTL ? 'تعديل الرسالة' : 'Edit Message'}
                </h1>
                <p className="text-gray-600">
                  {isRTL ? (message.title_ar || message.title) : message.title}
                </p>
              </div>
            </div>
            <div className="flex items-center space-x-3 rtl:space-x-reverse">
              <button
                onClick={handlePreview}
                className="flex items-center space-x-2 rtl:space-x-reverse px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
              >
                <Eye className="h-4 w-4" />
                <span>{isRTL ? 'معاينة' : 'Preview'}</span>
              </button>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="px-6 py-6">
          <form onSubmit={handleSubmit} className="max-w-4xl mx-auto">
            <div className="bg-white rounded-lg shadow-sm p-6 space-y-6">
              {/* Basic Information */}
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-4">
                  {isRTL ? 'المعلومات الأساسية' : 'Basic Information'}
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      {isRTL ? 'العنوان (إنجليزي) *' : 'Title (English) *'}
                    </label>
                    <input
                      type="text"
                      value={formData.title}
                      onChange={(e) => setFormData({ ...formData, title: e.target.value })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      {isRTL ? 'العنوان (عربي)' : 'Title (Arabic)'}
                    </label>
                    <input
                      type="text"
                      value={formData.title_ar}
                      onChange={(e) => setFormData({ ...formData, title_ar: e.target.value })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      {isRTL ? 'موضوع البريد (إنجليزي) *' : 'Email Subject (English) *'}
                    </label>
                    <input
                      type="text"
                      value={formData.subject}
                      onChange={(e) => setFormData({ ...formData, subject: e.target.value })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      {isRTL ? 'موضوع البريد (عربي)' : 'Email Subject (Arabic)'}
                    </label>
                    <input
                      type="text"
                      value={formData.subject_ar}
                      onChange={(e) => setFormData({ ...formData, subject_ar: e.target.value })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                </div>
              </div>

              {/* Content */}
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-4">
                  {isRTL ? 'المحتوى' : 'Content'}
                </h3>
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      {isRTL ? 'اختيار القالب' : 'Select Template'}
                    </label>
                    <select
                      value={formData.template_id}
                      onChange={(e) => setFormData({ ...formData, template_id: e.target.value })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                      <option value="">{isRTL ? 'اختر قالب' : 'Select Template'}</option>
                      {templates.map((template) => (
                        <option key={template.id} value={template.id}>
                          {isRTL ? (template.name_ar || template.name) : template.name}
                        </option>
                      ))}
                    </select>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        {isRTL ? 'المحتوى (إنجليزي) *' : 'Content (English) *'}
                      </label>
                      <textarea
                        value={formData.content}
                        onChange={(e) => setFormData({ ...formData, content: e.target.value })}
                        rows={6}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        required
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        {isRTL ? 'المحتوى (عربي)' : 'Content (Arabic)'}
                      </label>
                      <textarea
                        value={formData.content_ar}
                        onChange={(e) => setFormData({ ...formData, content_ar: e.target.value })}
                        rows={6}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      />
                    </div>
                  </div>
                </div>
              </div>

              {/* Targeting Info */}
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-4">
                  {isRTL ? 'الاستهداف' : 'Targeting'}
                </h3>
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <div className="flex items-center">
                    <Users className="h-5 w-5 text-blue-600 mr-2 rtl:ml-2 rtl:mr-0" />
                    <span className="text-sm font-medium text-blue-900">
                      {isRTL 
                        ? `العملاء المستهدفين: ${clientsCount} عميل`
                        : `Targeted Clients: ${clientsCount} clients`
                      }
                    </span>
                  </div>
                </div>
              </div>

              {/* Submit Buttons */}
              <div className="flex items-center justify-end space-x-4 rtl:space-x-reverse pt-4 border-t border-gray-200">
                <button
                  type="button"
                  onClick={() => navigate('/messages')}
                  className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
                >
                  {isRTL ? 'إلغاء' : 'Cancel'}
                </button>
                <button
                  type="submit"
                  disabled={loading}
                  className="flex items-center space-x-2 rtl:space-x-reverse px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {loading && <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>}
                  <Save className="h-4 w-4" />
                  <span>{loading ? (isRTL ? 'جاري التحديث...' : 'Updating...') : (isRTL ? 'تحديث الرسالة' : 'Update Message')}</span>
                </button>
              </div>
            </div>
          </form>
        </div>

        {/* Preview Modal */}
        {showPreview && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
            <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden">
              <div className="flex items-center justify-between p-6 border-b border-gray-200">
                <h3 className="text-lg font-medium text-gray-900">
                  {isRTL ? 'معاينة الرسالة' : 'Message Preview'}
                </h3>
                <button
                  onClick={() => setShowPreview(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  ×
                </button>
              </div>
              <div className="p-6 overflow-y-auto max-h-[calc(90vh-120px)]">
                <div 
                  className="border border-gray-200 rounded-lg p-4 bg-gray-50"
                  dangerouslySetInnerHTML={{ __html: previewHtml }}
                />
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default EditMessage;
