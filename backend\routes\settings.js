const express = require('express');
const router = express.Router();
const { auth } = require('../middleware/auth');
const db = require('../config/database').db;
const emailService = require('../utils/emailService');

// Get all settings
router.get('/', auth, async (req, res) => {
  try {
    db.all('SELECT * FROM settings', [], (err, settings) => {
      if (err) {
        console.error('Error fetching settings:', err);
        return res.status(500).json({
          success: false,
          error: 'Failed to fetch settings',
          error_ar: 'فشل في جلب الإعدادات'
        });
      }

      // Convert array to object for easier frontend handling
      const settingsObj = {};
      settings.forEach(setting => {
        settingsObj[setting.key] = setting.value;
      });

      res.json({
        success: true,
        data: settingsObj,
        message: 'Settings retrieved successfully',
        message_ar: 'تم جلب الإعدادات بنجاح'
      });
    });
  } catch (error) {
    console.error('Error in settings route:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error',
      error_ar: 'خطأ داخلي في الخادم'
    });
  }
});

// Update settings
router.put('/', auth, async (req, res) => {
  try {
    const updates = req.body;
    
    if (!updates || Object.keys(updates).length === 0) {
      return res.status(400).json({
        success: false,
        error: 'No settings provided',
        error_ar: 'لم يتم توفير إعدادات'
      });
    }

    // Update each setting
    const updatePromises = Object.entries(updates).map(([key, value]) => {
      return new Promise((resolve, reject) => {
        db.run(
          'INSERT OR REPLACE INTO settings (key, value, updated_at) VALUES (?, ?, CURRENT_TIMESTAMP)',
          [key, value],
          function(err) {
            if (err) {
              reject(err);
            } else {
              resolve();
            }
          }
        );
      });
    });

    await Promise.all(updatePromises);

    // Check if SMTP settings were updated and reload email service
    const smtpKeys = ['smtp_host', 'smtp_port', 'smtp_username', 'smtp_password', 'smtp_secure', 'smtp_from_name', 'smtp_from_email'];
    const hasSmtpUpdates = Object.keys(updates).some(key => smtpKeys.includes(key));

    if (hasSmtpUpdates) {
      try {
        await emailService.reloadConfiguration();
        console.log('✅ Email service reloaded after SMTP settings update');
      } catch (error) {
        console.warn('⚠️  Failed to reload email service:', error.message);
      }
    }

    res.json({
      success: true,
      message: 'Settings updated successfully',
      message_ar: 'تم تحديث الإعدادات بنجاح'
    });

  } catch (error) {
    console.error('Error updating settings:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to update settings',
      error_ar: 'فشل في تحديث الإعدادات'
    });
  }
});

// Get specific setting
router.get('/:key', auth, async (req, res) => {
  try {
    const { key } = req.params;

    db.get('SELECT * FROM settings WHERE key = ?', [key], (err, setting) => {
      if (err) {
        console.error('Error fetching setting:', err);
        return res.status(500).json({
          success: false,
          error: 'Failed to fetch setting',
          error_ar: 'فشل في جلب الإعداد'
        });
      }

      if (!setting) {
        return res.status(404).json({
          success: false,
          error: 'Setting not found',
          error_ar: 'الإعداد غير موجود'
        });
      }

      res.json({
        success: true,
        data: setting,
        message: 'Setting retrieved successfully',
        message_ar: 'تم جلب الإعداد بنجاح'
      });
    });
  } catch (error) {
    console.error('Error in setting route:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error',
      error_ar: 'خطأ داخلي في الخادم'
    });
  }
});

// Update specific setting
router.put('/:key', auth, async (req, res) => {
  try {
    const { key } = req.params;
    const { value } = req.body;

    if (value === undefined) {
      return res.status(400).json({
        success: false,
        error: 'Value is required',
        error_ar: 'القيمة مطلوبة'
      });
    }

    db.run(
      'INSERT OR REPLACE INTO settings (key, value, updated_at) VALUES (?, ?, CURRENT_TIMESTAMP)',
      [key, value],
      function(err) {
        if (err) {
          console.error('Error updating setting:', err);
          return res.status(500).json({
            success: false,
            error: 'Failed to update setting',
            error_ar: 'فشل في تحديث الإعداد'
          });
        }

        res.json({
          success: true,
          message: 'Setting updated successfully',
          message_ar: 'تم تحديث الإعداد بنجاح'
        });
      }
    );
  } catch (error) {
    console.error('Error updating setting:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error',
      error_ar: 'خطأ داخلي في الخادم'
    });
  }
});

// Reset settings to default
router.post('/reset', auth, async (req, res) => {
  try {
    const defaultSettings = [
      { key: 'default_language', value: 'ar', description: 'Default system language' },
      { key: 'smtp_configured', value: 'false', description: 'SMTP configuration status' },
      { key: 'max_recipients_per_campaign', value: '1000', description: 'Maximum recipients per campaign' },
      { key: 'email_rate_limit', value: '100', description: 'Emails per hour limit' },
      { key: 'theme', value: 'light', description: 'Default theme' },
      { key: 'notifications_enabled', value: 'true', description: 'Enable notifications' },
      { key: 'auto_backup', value: 'true', description: 'Enable automatic backups' }
    ];

    // Clear existing settings
    db.run('DELETE FROM settings', [], (err) => {
      if (err) {
        console.error('Error clearing settings:', err);
        return res.status(500).json({
          success: false,
          error: 'Failed to reset settings',
          error_ar: 'فشل في إعادة تعيين الإعدادات'
        });
      }

      // Insert default settings
      const insertPromises = defaultSettings.map(setting => {
        return new Promise((resolve, reject) => {
          db.run(
            'INSERT INTO settings (key, value, description) VALUES (?, ?, ?)',
            [setting.key, setting.value, setting.description],
            function(err) {
              if (err) {
                reject(err);
              } else {
                resolve();
              }
            }
          );
        });
      });

      Promise.all(insertPromises)
        .then(() => {
          res.json({
            success: true,
            message: 'Settings reset to default successfully',
            message_ar: 'تم إعادة تعيين الإعدادات إلى الافتراضية بنجاح'
          });
        })
        .catch(error => {
          console.error('Error inserting default settings:', error);
          res.status(500).json({
            success: false,
            error: 'Failed to reset settings',
            error_ar: 'فشل في إعادة تعيين الإعدادات'
          });
        });
    });
  } catch (error) {
    console.error('Error resetting settings:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error',
      error_ar: 'خطأ داخلي في الخادم'
    });
  }
});

module.exports = router;
