# Server Configuration
PORT=5000
NODE_ENV=development

# Database Configuration
DB_PATH=../database/marketing_email.db

# JWT Configuration
JWT_SECRET=your_jwt_secret_key_here
JWT_EXPIRES_IN=7d

# Email Configuration (Auto-detection Supported)
# Option 1: Auto-detection (Recommended) - Just provide email and password
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password-or-password

# Option 2: Manual SMTP Configuration (Optional)
# Uncomment and configure if auto-detection doesn't work
# SMTP_HOST=smtp.gmail.com
# SMTP_PORT=587
# SMTP_SECURE=false

# Email From Address (Optional - defaults to SMTP_USER)
# SMTP_FROM=<EMAIL>

# Supported Providers (Auto-detected):
# - Gmail: requires App Password
# - Outlook/Hotmail: requires App Password
# - Yahoo: requires App Password
# - iCloud: requires App Password
# - Zoho: regular password
# - AOL: requires App Password
# - ProtonMail: regular password
# - Custom: manual configuration required

# CORS Configuration
FRONTEND_URL=http://localhost:3000

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# File Upload Configuration
MAX_FILE_SIZE=10485760
UPLOAD_PATH=./uploads
