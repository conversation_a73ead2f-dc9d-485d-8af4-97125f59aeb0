{"name": "email-marketing-backend", "version": "1.0.0", "description": "Backend for Email Marketing Management System", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest"}, "keywords": ["email", "marketing", "express", "sqlite", "multilingual"], "author": "Email Marketing System", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "sqlite3": "^5.1.6", "multer": "^1.4.5-lts.1", "xlsx": "^0.18.5", "nodemailer": "^6.9.7", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "helmet": "^7.1.0", "express-rate-limit": "^7.1.5", "dotenv": "^16.3.1", "node-cron": "^3.0.3"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "supertest": "^6.3.3"}}