import React, { useState, useEffect, useRef } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { 
  ArrowLeft, 
  Edit, 
  Trash2, 
  Send, 
  Eye, 
  Calendar,
  MessageSquare,
  Users,
  BarChart3,
  Clock
} from 'lucide-react';
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, PieChart, Pie, Cell } from 'recharts';
import LoadingSpinner from '../../components/UI/LoadingSpinner';
import { apiHelpers, endpoints } from '../../services/api';
import toast from 'react-hot-toast';

const MessageDetails = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const { t, i18n } = useTranslation();
  const [message, setMessage] = useState(null);
  const [interactions, setInteractions] = useState([]);
  const [stats, setStats] = useState(null);
  const [timelineData, setTimelineData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('overview');
  const [timelinePeriod, setTimelinePeriod] = useState('7'); // days
  const [timelineInterval, setTimelineInterval] = useState('day'); // day, week, month
  const [timelineStartDate, setTimelineStartDate] = useState('');
  const loadingRef = useRef(false);

  const isRTL = i18n.language === 'ar';

  useEffect(() => {
    loadMessageData();
  }, [id]);

  useEffect(() => {
    if (message) {
      loadTimelineData();
    }
  }, [timelinePeriod, timelineInterval, timelineStartDate, message]);

  const loadMessageData = async () => {
    try {
      if (loadingRef.current) {
        console.log('⚠️ Skipping duplicate request - already loading message details');
        return;
      }
      loadingRef.current = true;
      setLoading(true);

      // Load message details
      const messageResult = await apiHelpers.get(endpoints.messages.get(id));
      if (messageResult.success) {
        setMessage(messageResult.data.data);
      }

      // Load message interactions
      const interactionsResult = await apiHelpers.get(endpoints.interactions.byMessage(id));
      console.log('📧 Message interactions result:', interactionsResult);
      if (interactionsResult.success) {
        const interactions = interactionsResult.data?.data?.interactions || interactionsResult.data?.data || [];
        console.log('📧 Setting interactions:', interactions);
        console.log('📧 Interactions length:', interactions.length);
        setInteractions(interactions);
      } else {
        console.log('📧 Failed to load interactions:', interactionsResult);
        setInteractions([]);
      }

      // Load message statistics
      const statsResult = await apiHelpers.get(endpoints.interactions.stats, {
        params: { message_id: id }
      });
      if (statsResult.success) {
        setStats(statsResult.data.data);
      }

      // Timeline data will be loaded separately

    } catch (error) {
      console.error('Error loading message data:', error);
      toast.error(t('messages.error_loading'));
    } finally {
      setLoading(false);
      loadingRef.current = false;
    }
  };

  const loadTimelineData = async () => {
    try {
      console.log('📈 Loading timeline data with params:', {
        message_id: id,
        period: timelinePeriod,
        interval: timelineInterval,
        start_date: timelineStartDate
      });

      const params = {
        message_id: id,
        days: timelinePeriod,
        interval: timelineInterval
      };

      if (timelineStartDate) {
        params.start_date = timelineStartDate;
      }

      const timelineResult = await apiHelpers.get(endpoints.interactions.timeline, {
        params
      });

      console.log('📈 Timeline result:', timelineResult);
      if (timelineResult.success) {
        const timeline = timelineResult.data?.data || [];
        console.log('📈 Setting timeline data:', timeline);
        setTimelineData(timeline);
      } else {
        console.log('📈 Failed to load timeline:', timelineResult);
        setTimelineData([]);
      }
    } catch (error) {
      console.error('Error loading timeline data:', error);
      setTimelineData([]);
    }
  };

  const handleDeleteMessage = async () => {
    if (!confirm(t('messages.delete_confirm'))) return;

    try {
      const result = await apiHelpers.delete(endpoints.messages.delete(id));
      
      if (result.success) {
        toast.success(t('messages_ui.success_delete'));
        navigate('/messages');
      }
    } catch (error) {
      toast.error(t('messages_ui.error_delete'));
    }
  };

  const handleSendMessage = () => {
    navigate(`/messages/${id}/send`);
  };

  const getStatusBadge = (status) => {
    const statusConfig = {
      draft: { class: 'badge-secondary', text: t('messages.draft') },
      published: { class: 'badge-primary', text: t('messages.published') },
      scheduled: { class: 'badge-warning', text: t('messages.scheduled') },
      sent: { class: 'badge-success', text: t('messages.sent') }
    };
    
    const config = statusConfig[status] || statusConfig.draft;
    return <span className={`badge ${config.class}`}>{config.text}</span>;
  };

  const getTypeBadge = (type) => {
    const typeConfig = {
      manual: { class: 'badge-blue', text: t('messages.manual') },
      automated: { class: 'badge-purple', text: t('messages.automated') },
      promotional: { class: 'badge-green', text: t('messages.promotional') },
      newsletter: { class: 'badge-orange', text: t('messages.newsletter') },
      notification: { class: 'badge-red', text: t('messages.notification') }
    };
    
    const config = typeConfig[type] || typeConfig.manual;
    return <span className={`badge ${config.class}`}>{config.text}</span>;
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      calendar: 'gregory'
    });
  };

  const formatDateTime = (dateString) => {
    return new Date(dateString).toLocaleString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      calendar: 'gregory'
    });
  };

  // Performance data for charts - only real data
  const performanceData = [
    { name: isRTL ? 'مرسل' : 'Sent', value: stats?.sent || 0, color: '#3b82f6' },
    { name: isRTL ? 'مفتوح' : 'Opened', value: stats?.opened || 0, color: '#10b981' },
    { name: isRTL ? 'منقور' : 'Clicked', value: stats?.clicked || 0, color: '#f59e0b' },
    { name: isRTL ? 'مرتد' : 'Bounced', value: stats?.bounced || 0, color: '#ef4444' }
  ];

  // Generate engagement data with empty periods filled in
  const generateEngagementData = () => {
    const periods = parseInt(timelinePeriod);
    const interval = timelineInterval;

    // Create a map of existing data
    const dataMap = {};
    timelineData.forEach(item => {
      dataMap[item.date] = {
        opens: item.opens || 0,
        clicks: item.clicks || 0,
        sent: item.sent || 0
      };
    });

    // Generate periods based on interval
    const data = [];
    const startDate = timelineStartDate ? new Date(timelineStartDate) : new Date();

    for (let i = periods - 1; i >= 0; i--) {
      const date = new Date(startDate);
      let dateStr = '';
      let displayLabel = '';

      if (interval === 'day') {
        date.setDate(date.getDate() - i);
        dateStr = date.toISOString().split('T')[0];
        displayLabel = date.toLocaleDateString('en-US', {
          month: 'short',
          day: 'numeric',
          calendar: 'gregory'
        });
      } else if (interval === 'week') {
        date.setDate(date.getDate() - (i * 7));
        // Get start of week
        const startOfWeek = new Date(date);
        startOfWeek.setDate(date.getDate() - date.getDay());
        dateStr = startOfWeek.toISOString().split('T')[0];
        displayLabel = `${isRTL ? 'أسبوع' : 'Week'} ${startOfWeek.toLocaleDateString('en-US', { month: 'short', day: 'numeric', calendar: 'gregory' })}`;
      } else if (interval === 'month') {
        date.setMonth(date.getMonth() - i);
        dateStr = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-01`;
        displayLabel = date.toLocaleDateString('en-US', {
          year: 'numeric',
          month: 'long',
          calendar: 'gregory'
        });
      }

      data.push({
        period: dateStr,
        displayLabel,
        opens: dataMap[dateStr]?.opens || 0,
        clicks: dataMap[dateStr]?.clicks || 0,
        sent: dataMap[dateStr]?.sent || 0
      });
    }

    return data;
  };

  const engagementData = generateEngagementData();

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="large" text={t('common.loading')} />
      </div>
    );
  }

  if (!message) {
    return (
      <div className="text-center py-12">
        <div className="text-red-600 mb-4">{t('messages.message_not_found')}</div>
        <button 
          onClick={() => navigate('/messages')}
          className="btn btn-primary"
        >
          {t('common.back')}
        </button>
      </div>
    );
  }

  const tabs = [
    { id: 'overview', label: t('dashboard.overview'), icon: Eye },
    { id: 'content', label: t('messages.message_content'), icon: MessageSquare },
    { id: 'analytics', label: t('reports.analytics'), icon: BarChart3 },
    { id: 'interactions', label: isRTL ? 'التفاعلات' : 'Interactions', icon: Clock },
    { id: 'recipients', label: t('messages.recipients'), icon: Users }
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4 rtl:space-x-reverse">
          <button
            onClick={() => navigate('/messages')}
            className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-md"
          >
            <ArrowLeft className="h-5 w-5" />
          </button>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">
              {message.title}
            </h1>
            <p className="mt-1 text-gray-600">
              {t('messages.message_details')}
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-3 rtl:space-x-reverse">
          {(message.status === 'published' || message.status === 'draft' || message.status === 'ready') && (
            <button
              onClick={handleSendMessage}
              className="btn btn-success flex items-center space-x-2 rtl:space-x-reverse"
            >
              <Send className="h-4 w-4" />
              <span>{t('messages.send_message')}</span>
            </button>
          )}
          <button
            onClick={() => navigate(`/messages/${id}/edit`)}
            className="btn btn-outline flex items-center space-x-2 rtl:space-x-reverse"
          >
            <Edit className="h-4 w-4" />
            <span>{t('common.edit')}</span>
          </button>
          <button
            onClick={handleDeleteMessage}
            className="btn btn-danger flex items-center space-x-2 rtl:space-x-reverse"
          >
            <Trash2 className="h-4 w-4" />
            <span>{t('common.delete')}</span>
          </button>
        </div>
      </div>

      {/* Message Info Card */}
      <div className="card">
        <div className="card-body">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Basic Info */}
            <div className="lg:col-span-2">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div>
                    <p className="text-sm font-medium text-gray-500 mb-1">
                      {t('messages.message_type')}
                    </p>
                    {getTypeBadge(message.type)}
                  </div>

                  <div>
                    <p className="text-sm font-medium text-gray-500 mb-1">
                      {t('messages.message_status')}
                    </p>
                    {getStatusBadge(message.status)}
                  </div>

                  <div>
                    <p className="text-sm font-medium text-gray-500 mb-1">
                      {t('messages.message_language')}
                    </p>
                    <p className="text-gray-900">
                      {message.language === 'ar' ? 'العربية' : 'English'}
                    </p>
                  </div>

                  {message.category && (
                    <div>
                      <p className="text-sm font-medium text-gray-500 mb-1">
                        {t('messages.message_category')}
                      </p>
                      <span className="badge badge-secondary">{message.category}</span>
                    </div>
                  )}
                </div>

                <div className="space-y-4">
                  <div className="flex items-center space-x-3 rtl:space-x-reverse">
                    <Calendar className="h-5 w-5 text-gray-400" />
                    <div>
                      <p className="text-sm font-medium text-gray-500">
                        {t('messages.message_created')}
                      </p>
                      <p className="text-gray-900">{formatDate(message.created_at)}</p>
                    </div>
                  </div>

                  {message.scheduled_at && (
                    <div className="flex items-center space-x-3 rtl:space-x-reverse">
                      <Clock className="h-5 w-5 text-gray-400" />
                      <div>
                        <p className="text-sm font-medium text-gray-500">
                          {t('messages.message_scheduled')}
                        </p>
                        <p className="text-gray-900">{formatDateTime(message.scheduled_at)}</p>
                      </div>
                    </div>
                  )}

                  {message.created_by && (
                    <div>
                      <p className="text-sm font-medium text-gray-500 mb-1">
                        {isRTL ? 'أنشأ بواسطة' : 'Created by'}
                      </p>
                      <p className="text-gray-900">{message.created_by}</p>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Stats */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-900">
                {t('messages.message_analytics')}
              </h3>
              
              <div className="space-y-3">
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">
                    {isRTL ? 'إجمالي المرسل' : 'Total Sent'}
                  </span>
                  <span className="font-medium">{stats?.sent || 0}</span>
                </div>
                
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">
                    {isRTL ? 'معدل الفتح' : 'Open Rate'}
                  </span>
                  <span className="font-medium text-green-600">
                    {stats?.rates?.open_rate || 0}%
                  </span>
                </div>
                
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">
                    {isRTL ? 'معدل النقر' : 'Click Rate'}
                  </span>
                  <span className="font-medium text-blue-600">
                    {stats?.rates?.click_rate || 0}%
                  </span>
                </div>
                
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">
                    {isRTL ? 'معدل الارتداد' : 'Bounce Rate'}
                  </span>
                  <span className="font-medium text-red-600">
                    {stats?.rates?.bounce_rate || 0}%
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8 rtl:space-x-reverse">
          {tabs.map((tab) => {
            const Icon = tab.icon;
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`
                  flex items-center space-x-2 rtl:space-x-reverse py-2 px-1 border-b-2 font-medium text-sm
                  ${activeTab === tab.id
                    ? 'border-primary-500 text-primary-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }
                `}
              >
                <Icon className="h-4 w-4" />
                <span>{tab.label}</span>
              </button>
            );
          })}
        </nav>
      </div>

      {/* Tab Content */}
      <div className="space-y-6">
        {activeTab === 'overview' && (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Performance Chart */}
            <div className="card">
              <div className="card-header">
                <h3 className="text-lg font-medium text-gray-900">
                  {t('messages.message_analytics')}
                </h3>
              </div>
              <div className="card-body">
                <ResponsiveContainer width="100%" height={250}>
                  <PieChart>
                    <Pie
                      data={performanceData}
                      cx="50%"
                      cy="50%"
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="value"
                      label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                    >
                      {performanceData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Pie>
                    <Tooltip />
                  </PieChart>
                </ResponsiveContainer>
              </div>
            </div>

            {/* Engagement Timeline */}
            <div className="card">
              <div className="card-header">
                <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                  <h3 className="text-lg font-medium text-gray-900">
                    {isRTL ? 'تطور التفاعل' : 'Engagement Timeline'}
                  </h3>

                  {/* Timeline Controls */}
                  <div className="flex flex-col sm:flex-row gap-3">
                    {/* Start Date Picker */}
                    <div className="flex flex-col">
                      <label className="text-xs text-gray-500 mb-1">
                        {isRTL ? 'تاريخ البداية' : 'Start Date'}
                      </label>
                      <input
                        type="date"
                        value={timelineStartDate}
                        onChange={(e) => setTimelineStartDate(e.target.value)}
                        className="px-3 py-1 text-sm border border-gray-300 rounded-md focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                      />
                    </div>

                    {/* Period Selector */}
                    <div className="flex flex-col">
                      <label className="text-xs text-gray-500 mb-1">
                        {isRTL ? 'عدد الفترات' : 'Number of Periods'}
                      </label>
                      <select
                        value={timelinePeriod}
                        onChange={(e) => setTimelinePeriod(e.target.value)}
                        className="px-3 py-1 text-sm border border-gray-300 rounded-md focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                      >
                        <option value="7">7</option>
                        <option value="14">14</option>
                        <option value="30">30</option>
                        <option value="60">60</option>
                        <option value="90">90</option>
                      </select>
                    </div>

                    {/* Interval Selector */}
                    <div className="flex flex-col">
                      <label className="text-xs text-gray-500 mb-1">
                        {isRTL ? 'الفترة الزمنية' : 'Time Interval'}
                      </label>
                      <select
                        value={timelineInterval}
                        onChange={(e) => setTimelineInterval(e.target.value)}
                        className="px-3 py-1 text-sm border border-gray-300 rounded-md focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                      >
                        <option value="day">{isRTL ? 'يوم' : 'Day'}</option>
                        <option value="week">{isRTL ? 'أسبوع' : 'Week'}</option>
                        <option value="month">{isRTL ? 'شهر' : 'Month'}</option>
                      </select>
                    </div>
                  </div>
                </div>
              </div>
              <div className="card-body">
                <ResponsiveContainer width="100%" height={300}>
                  <BarChart data={engagementData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis
                      dataKey="displayLabel"
                      tick={{ fontSize: 12 }}
                      angle={-45}
                      textAnchor="end"
                      height={80}
                    />
                    <YAxis tick={{ fontSize: 12 }} />
                    <Tooltip
                      labelFormatter={(value) => value}
                      formatter={(value, name) => [
                        value,
                        name === 'opens' ? (isRTL ? 'فتح' : 'Opens') :
                        name === 'clicks' ? (isRTL ? 'نقرات' : 'Clicks') :
                        (isRTL ? 'مرسل' : 'Sent')
                      ]}
                    />
                    <Bar dataKey="sent" fill="#3b82f6" name="sent" />
                    <Bar dataKey="opens" fill="#10b981" name="opens" />
                    <Bar dataKey="clicks" fill="#f59e0b" name="clicks" />
                  </BarChart>
                </ResponsiveContainer>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'content' && (
          <div className="card">
            <div className="card-header">
              <h3 className="text-lg font-medium text-gray-900">
                {t('messages.message_content')}
              </h3>
            </div>
            <div className="card-body">
              <div className="space-y-6">
                {/* Arabic Content */}
                {(message.title_ar || message.content_ar) && (
                  <div>
                    <h4 className="text-md font-medium text-gray-900 mb-3">
                      {t('messages.arabic_content')}
                    </h4>
                    {message.title_ar && (
                      <div className="mb-4">
                        <p className="text-sm font-medium text-gray-500 mb-1">
                          {t('messages.arabic_title')}
                        </p>
                        <p className="text-lg font-medium text-gray-900 rtl">
                          {message.title_ar}
                        </p>
                      </div>
                    )}
                    {message.content_ar && (
                      <div>
                        <p className="text-sm font-medium text-gray-500 mb-2">
                          {t('messages.arabic_content')}
                        </p>
                        <div 
                          className="prose prose-sm max-w-none rtl text-gray-900 bg-gray-50 p-4 rounded-lg"
                          dangerouslySetInnerHTML={{ __html: message.content_ar }}
                        />
                      </div>
                    )}
                  </div>
                )}

                {/* English Content */}
                {(message.title_en || message.content_en) && (
                  <div>
                    <h4 className="text-md font-medium text-gray-900 mb-3">
                      {t('messages.english_content')}
                    </h4>
                    {message.title_en && (
                      <div className="mb-4">
                        <p className="text-sm font-medium text-gray-500 mb-1">
                          {t('messages.english_title')}
                        </p>
                        <p className="text-lg font-medium text-gray-900 ltr">
                          {message.title_en}
                        </p>
                      </div>
                    )}
                    {message.content_en && (
                      <div>
                        <p className="text-sm font-medium text-gray-500 mb-2">
                          {t('messages.english_content')}
                        </p>
                        <div 
                          className="prose prose-sm max-w-none ltr text-gray-900 bg-gray-50 p-4 rounded-lg"
                          dangerouslySetInnerHTML={{ __html: message.content_en }}
                        />
                      </div>
                    )}
                  </div>
                )}

                {/* Default Content */}
                {!message.title_ar && !message.content_ar && !message.title_en && !message.content_en && (
                  <div>
                    <h4 className="text-md font-medium text-gray-900 mb-3">
                      {t('messages.message_content')}
                    </h4>
                    <div 
                      className="prose prose-sm max-w-none text-gray-900 bg-gray-50 p-4 rounded-lg"
                      dangerouslySetInnerHTML={{ __html: message.content }}
                    />
                  </div>
                )}
              </div>
            </div>
          </div>
        )}

        {activeTab === 'analytics' && (
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
            <div className="card">
              <div className="card-body text-center">
                <div className="text-3xl font-bold text-blue-600">
                  {stats?.sent || 0}
                </div>
                <div className="text-sm text-gray-600 mt-1">
                  {isRTL ? 'إجمالي المرسل' : 'Total Sent'}
                </div>
              </div>
            </div>
            
            <div className="card">
              <div className="card-body text-center">
                <div className="text-3xl font-bold text-green-600">
                  {stats?.opened || 0}
                </div>
                <div className="text-sm text-gray-600 mt-1">
                  {isRTL ? 'تم فتحها' : 'Opened'}
                </div>
              </div>
            </div>
            
            <div className="card">
              <div className="card-body text-center">
                <div className="text-3xl font-bold text-orange-600">
                  {stats?.clicked || 0}
                </div>
                <div className="text-sm text-gray-600 mt-1">
                  {isRTL ? 'تم النقر' : 'Clicked'}
                </div>
              </div>
            </div>
            
            <div className="card">
              <div className="card-body text-center">
                <div className="text-3xl font-bold text-red-600">
                  {stats?.bounced || 0}
                </div>
                <div className="text-sm text-gray-600 mt-1">
                  {isRTL ? 'مرتد' : 'Bounced'}
                </div>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'interactions' && (
          <div className="card">
            <div className="card-header">
              <h3 className="text-lg font-medium text-gray-900">
                {isRTL ? 'سجل التفاعلات' : 'Interaction Log'}
              </h3>
              <p className="text-sm text-gray-600 mt-1">
                {isRTL ? 'جميع التفاعلات مع هذه الرسالة' : 'All interactions with this message'}
              </p>
            </div>
            <div className="overflow-x-auto">
              <table className="table">
                <thead className="table-header">
                  <tr>
                    <th>{isRTL ? 'العميل' : 'Client'}</th>
                    <th>{isRTL ? 'البريد الإلكتروني' : 'Email'}</th>
                    <th>{isRTL ? 'النوع' : 'Type'}</th>
                    <th>{isRTL ? 'الحالة' : 'Status'}</th>
                    <th>{isRTL ? 'تاريخ الإرسال' : 'Sent At'}</th>
                    <th>{isRTL ? 'تاريخ الفتح' : 'Opened At'}</th>
                    <th>{isRTL ? 'تاريخ النقر' : 'Clicked At'}</th>
                  </tr>
                </thead>
                <tbody>
                  {(() => {
                    console.log('🔍 Rendering interactions, length:', interactions.length);
                    console.log('🔍 Interactions data:', interactions);
                    return interactions.length === 0 ? (
                      <tr>
                        <td colSpan="7" className="text-center py-8 text-gray-500">
                          {isRTL ? 'لا توجد تفاعلات' : 'No interactions found'}
                        </td>
                      </tr>
                    ) : (
                    interactions.map((interaction, index) => (
                      <tr key={index} className="table-row-hover">
                        <td className="font-medium">
                          {interaction.client_name || `${isRTL ? 'عميل' : 'Client'} #${interaction.client_id}`}
                        </td>
                        <td>{interaction.client_email || '-'}</td>
                        <td>
                          <span className={`badge ${
                            interaction.type === 'email_sent' ? 'badge-info' :
                            interaction.type === 'email' ? 'badge-primary' :
                            'badge-secondary'
                          }`}>
                            {interaction.type === 'email_sent' ? (isRTL ? 'إرسال' : 'Sent') :
                             interaction.type === 'email' ? (isRTL ? 'بريد' : 'Email') :
                             interaction.type}
                          </span>
                        </td>
                        <td>
                          <span className={`badge ${
                            interaction.status === 'sent' ? 'badge-success' :
                            interaction.status === 'opened' ? 'badge-primary' :
                            interaction.status === 'clicked' ? 'badge-warning' :
                            interaction.status === 'failed' ? 'badge-danger' :
                            'badge-secondary'
                          }`}>
                            {interaction.status === 'sent' ? (isRTL ? 'مرسل' : 'Sent') :
                             interaction.status === 'opened' ? (isRTL ? 'مفتوح' : 'Opened') :
                             interaction.status === 'clicked' ? (isRTL ? 'منقور' : 'Clicked') :
                             interaction.status === 'failed' ? (isRTL ? 'فشل' : 'Failed') :
                             interaction.status}
                          </span>
                        </td>
                        <td className="text-sm text-gray-500">
                          {interaction.sent_at ? formatDateTime(interaction.sent_at) : '-'}
                        </td>
                        <td className="text-sm text-gray-500">
                          {interaction.opened_at ? formatDateTime(interaction.opened_at) : '-'}
                        </td>
                        <td className="text-sm text-gray-500">
                          {interaction.clicked_at ? formatDateTime(interaction.clicked_at) : '-'}
                        </td>
                      </tr>
                    ))
                  );
                  })()}
                </tbody>
              </table>
            </div>
          </div>
        )}

        {activeTab === 'recipients' && (
          <div className="card">
            <div className="card-header">
              <h3 className="text-lg font-medium text-gray-900">
                {t('messages.recipients')}
              </h3>
            </div>
            <div className="overflow-x-auto">
              <table className="table">
                <thead className="table-header">
                  <tr>
                    <th>{t('clients.client_name')}</th>
                    <th>{t('clients.client_email')}</th>
                    <th>{t('common.status')}</th>
                    <th>{t('messages.sent_at')}</th>
                    <th>{t('messages.opened_at')}</th>
                    <th>{t('messages.clicked_at')}</th>
                  </tr>
                </thead>
                <tbody className="table-body">
                  {interactions.map((interaction, index) => (
                    <tr key={index} className="table-row-hover">
                      <td className="font-medium">
                        {interaction.client_name || (isRTL ? 'عميل' : 'Client')} #{interaction.client_id}
                      </td>
                      <td>{interaction.client_email || '-'}</td>
                      <td>
                        <span className={`badge ${
                          interaction.status === 'sent' ? 'badge-success' :
                          interaction.status === 'opened' ? 'badge-primary' :
                          interaction.status === 'clicked' ? 'badge-warning' :
                          'badge-secondary'
                        }`}>
                          {interaction.status}
                        </span>
                      </td>
                      <td className="text-sm text-gray-500">
                        {interaction.sent_at ? formatDateTime(interaction.sent_at) : '-'}
                      </td>
                      <td className="text-sm text-gray-500">
                        {interaction.opened_at ? formatDateTime(interaction.opened_at) : '-'}
                      </td>
                      <td className="text-sm text-gray-500">
                        {interaction.clicked_at ? formatDateTime(interaction.clicked_at) : '-'}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default MessageDetails;
