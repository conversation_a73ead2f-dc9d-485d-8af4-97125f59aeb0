import React, { useState, useEffect, useRef } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { 
  ArrowLeft, 
  Edit, 
  Trash2, 
  Send, 
  Eye, 
  Calendar,
  MessageSquare,
  Users,
  BarChart3,
  Clock
} from 'lucide-react';
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, PieChart, Pie, Cell } from 'recharts';
import LoadingSpinner from '../../components/UI/LoadingSpinner';
import { apiHelpers, endpoints } from '../../services/api';
import toast from 'react-hot-toast';

const MessageDetails = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const { t, i18n } = useTranslation();
  const [message, setMessage] = useState(null);
  const [interactions, setInteractions] = useState([]);
  const [stats, setStats] = useState(null);
  const [timelineData, setTimelineData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('overview');
  const loadingRef = useRef(false);

  const isRTL = i18n.language === 'ar';

  useEffect(() => {
    loadMessageData();
  }, [id]);

  const loadMessageData = async () => {
    try {
      if (loadingRef.current) {
        console.log('⚠️ Skipping duplicate request - already loading message details');
        return;
      }
      loadingRef.current = true;
      setLoading(true);

      // Load message details
      const messageResult = await apiHelpers.get(endpoints.messages.get(id));
      if (messageResult.success) {
        setMessage(messageResult.data.data);
      }

      // Load message interactions
      const interactionsResult = await apiHelpers.get(endpoints.interactions.byMessage(id));
      if (interactionsResult.success) {
        setInteractions(interactionsResult.data.data.interactions);
      }

      // Load message statistics
      const statsResult = await apiHelpers.get(endpoints.interactions.stats, {
        params: { message_id: id }
      });
      if (statsResult.success) {
        setStats(statsResult.data.data);
      }

      // Load timeline data
      const timelineResult = await apiHelpers.get(endpoints.interactions.timeline, {
        params: { message_id: id, days: 7 }
      });
      if (timelineResult.success) {
        setTimelineData(timelineResult.data.data);
      }

    } catch (error) {
      console.error('Error loading message data:', error);
      toast.error(t('messages.error_loading'));
    } finally {
      setLoading(false);
      loadingRef.current = false;
    }
  };

  const handleDeleteMessage = async () => {
    if (!confirm(t('messages.delete_confirm'))) return;

    try {
      const result = await apiHelpers.delete(endpoints.messages.delete(id));
      
      if (result.success) {
        toast.success(t('messages_ui.success_delete'));
        navigate('/messages');
      }
    } catch (error) {
      toast.error(t('messages_ui.error_delete'));
    }
  };

  const handleSendMessage = () => {
    navigate(`/messages/${id}/send`);
  };

  const getStatusBadge = (status) => {
    const statusConfig = {
      draft: { class: 'badge-secondary', text: t('messages.draft') },
      published: { class: 'badge-primary', text: t('messages.published') },
      scheduled: { class: 'badge-warning', text: t('messages.scheduled') },
      sent: { class: 'badge-success', text: t('messages.sent') }
    };
    
    const config = statusConfig[status] || statusConfig.draft;
    return <span className={`badge ${config.class}`}>{config.text}</span>;
  };

  const getTypeBadge = (type) => {
    const typeConfig = {
      manual: { class: 'badge-blue', text: t('messages.manual') },
      automated: { class: 'badge-purple', text: t('messages.automated') },
      promotional: { class: 'badge-green', text: t('messages.promotional') },
      newsletter: { class: 'badge-orange', text: t('messages.newsletter') },
      notification: { class: 'badge-red', text: t('messages.notification') }
    };
    
    const config = typeConfig[type] || typeConfig.manual;
    return <span className={`badge ${config.class}`}>{config.text}</span>;
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString(isRTL ? 'ar-SA' : 'en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const formatDateTime = (dateString) => {
    return new Date(dateString).toLocaleString(isRTL ? 'ar-SA' : 'en-US');
  };

  // Performance data for charts - only real data
  const performanceData = [
    { name: isRTL ? 'مرسل' : 'Sent', value: stats?.sent || 0, color: '#3b82f6' },
    { name: isRTL ? 'مفتوح' : 'Opened', value: stats?.opened || 0, color: '#10b981' },
    { name: isRTL ? 'منقور' : 'Clicked', value: stats?.clicked || 0, color: '#f59e0b' },
    { name: isRTL ? 'مرتد' : 'Bounced', value: stats?.bounced || 0, color: '#ef4444' }
  ];

  // Use real timeline data or empty array
  const engagementData = timelineData.length > 0 ? timelineData.map(item => ({
    period: item.date,
    opens: item.opens || 0,
    clicks: item.clicks || 0
  })) : [];

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="large" text={t('common.loading')} />
      </div>
    );
  }

  if (!message) {
    return (
      <div className="text-center py-12">
        <div className="text-red-600 mb-4">{t('messages.message_not_found')}</div>
        <button 
          onClick={() => navigate('/messages')}
          className="btn btn-primary"
        >
          {t('common.back')}
        </button>
      </div>
    );
  }

  const tabs = [
    { id: 'overview', label: t('dashboard.overview'), icon: Eye },
    { id: 'content', label: t('messages.message_content'), icon: MessageSquare },
    { id: 'analytics', label: t('reports.analytics'), icon: BarChart3 },
    { id: 'interactions', label: isRTL ? 'التفاعلات' : 'Interactions', icon: Clock },
    { id: 'recipients', label: t('messages.recipients'), icon: Users }
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4 rtl:space-x-reverse">
          <button
            onClick={() => navigate('/messages')}
            className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-md"
          >
            <ArrowLeft className="h-5 w-5" />
          </button>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">
              {message.title}
            </h1>
            <p className="mt-1 text-gray-600">
              {t('messages.message_details')}
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-3 rtl:space-x-reverse">
          {(message.status === 'published' || message.status === 'draft' || message.status === 'ready') && (
            <button
              onClick={handleSendMessage}
              className="btn btn-success flex items-center space-x-2 rtl:space-x-reverse"
            >
              <Send className="h-4 w-4" />
              <span>{t('messages.send_message')}</span>
            </button>
          )}
          <button
            onClick={() => navigate(`/messages/${id}/edit`)}
            className="btn btn-outline flex items-center space-x-2 rtl:space-x-reverse"
          >
            <Edit className="h-4 w-4" />
            <span>{t('common.edit')}</span>
          </button>
          <button
            onClick={handleDeleteMessage}
            className="btn btn-danger flex items-center space-x-2 rtl:space-x-reverse"
          >
            <Trash2 className="h-4 w-4" />
            <span>{t('common.delete')}</span>
          </button>
        </div>
      </div>

      {/* Message Info Card */}
      <div className="card">
        <div className="card-body">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Basic Info */}
            <div className="lg:col-span-2">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div>
                    <p className="text-sm font-medium text-gray-500 mb-1">
                      {t('messages.message_type')}
                    </p>
                    {getTypeBadge(message.type)}
                  </div>

                  <div>
                    <p className="text-sm font-medium text-gray-500 mb-1">
                      {t('messages.message_status')}
                    </p>
                    {getStatusBadge(message.status)}
                  </div>

                  <div>
                    <p className="text-sm font-medium text-gray-500 mb-1">
                      {t('messages.message_language')}
                    </p>
                    <p className="text-gray-900">
                      {message.language === 'ar' ? 'العربية' : 'English'}
                    </p>
                  </div>

                  {message.category && (
                    <div>
                      <p className="text-sm font-medium text-gray-500 mb-1">
                        {t('messages.message_category')}
                      </p>
                      <span className="badge badge-secondary">{message.category}</span>
                    </div>
                  )}
                </div>

                <div className="space-y-4">
                  <div className="flex items-center space-x-3 rtl:space-x-reverse">
                    <Calendar className="h-5 w-5 text-gray-400" />
                    <div>
                      <p className="text-sm font-medium text-gray-500">
                        {t('messages.message_created')}
                      </p>
                      <p className="text-gray-900">{formatDate(message.created_at)}</p>
                    </div>
                  </div>

                  {message.scheduled_at && (
                    <div className="flex items-center space-x-3 rtl:space-x-reverse">
                      <Clock className="h-5 w-5 text-gray-400" />
                      <div>
                        <p className="text-sm font-medium text-gray-500">
                          {t('messages.message_scheduled')}
                        </p>
                        <p className="text-gray-900">{formatDateTime(message.scheduled_at)}</p>
                      </div>
                    </div>
                  )}

                  {message.created_by && (
                    <div>
                      <p className="text-sm font-medium text-gray-500 mb-1">
                        {isRTL ? 'أنشأ بواسطة' : 'Created by'}
                      </p>
                      <p className="text-gray-900">{message.created_by}</p>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Stats */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-900">
                {t('messages.message_analytics')}
              </h3>
              
              <div className="space-y-3">
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">
                    {isRTL ? 'إجمالي المرسل' : 'Total Sent'}
                  </span>
                  <span className="font-medium">{stats?.sent || 0}</span>
                </div>
                
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">
                    {isRTL ? 'معدل الفتح' : 'Open Rate'}
                  </span>
                  <span className="font-medium text-green-600">
                    {stats?.rates?.open_rate || 0}%
                  </span>
                </div>
                
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">
                    {isRTL ? 'معدل النقر' : 'Click Rate'}
                  </span>
                  <span className="font-medium text-blue-600">
                    {stats?.rates?.click_rate || 0}%
                  </span>
                </div>
                
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">
                    {isRTL ? 'معدل الارتداد' : 'Bounce Rate'}
                  </span>
                  <span className="font-medium text-red-600">
                    {stats?.rates?.bounce_rate || 0}%
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8 rtl:space-x-reverse">
          {tabs.map((tab) => {
            const Icon = tab.icon;
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`
                  flex items-center space-x-2 rtl:space-x-reverse py-2 px-1 border-b-2 font-medium text-sm
                  ${activeTab === tab.id
                    ? 'border-primary-500 text-primary-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }
                `}
              >
                <Icon className="h-4 w-4" />
                <span>{tab.label}</span>
              </button>
            );
          })}
        </nav>
      </div>

      {/* Tab Content */}
      <div className="space-y-6">
        {activeTab === 'overview' && (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Performance Chart */}
            <div className="card">
              <div className="card-header">
                <h3 className="text-lg font-medium text-gray-900">
                  {t('messages.message_analytics')}
                </h3>
              </div>
              <div className="card-body">
                <ResponsiveContainer width="100%" height={250}>
                  <PieChart>
                    <Pie
                      data={performanceData}
                      cx="50%"
                      cy="50%"
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="value"
                      label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                    >
                      {performanceData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Pie>
                    <Tooltip />
                  </PieChart>
                </ResponsiveContainer>
              </div>
            </div>

            {/* Engagement Timeline */}
            <div className="card">
              <div className="card-header">
                <h3 className="text-lg font-medium text-gray-900">
                  {isRTL ? 'تطور التفاعل' : 'Engagement Timeline'}
                </h3>
              </div>
              <div className="card-body">
                <ResponsiveContainer width="100%" height={250}>
                  <BarChart data={engagementData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis 
                      dataKey="period" 
                      tick={{ fontSize: 12 }}
                      tickFormatter={(value) => new Date(value).toLocaleDateString(isRTL ? 'ar-SA' : 'en-US', { month: 'short', day: 'numeric' })}
                    />
                    <YAxis tick={{ fontSize: 12 }} />
                    <Tooltip 
                      labelFormatter={(value) => new Date(value).toLocaleDateString(isRTL ? 'ar-SA' : 'en-US')}
                      formatter={(value, name) => [
                        value,
                        name === 'opens' ? (isRTL ? 'فتح' : 'Opens') : (isRTL ? 'نقرات' : 'Clicks')
                      ]}
                    />
                    <Bar dataKey="opens" fill="#3b82f6" />
                    <Bar dataKey="clicks" fill="#10b981" />
                  </BarChart>
                </ResponsiveContainer>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'content' && (
          <div className="card">
            <div className="card-header">
              <h3 className="text-lg font-medium text-gray-900">
                {t('messages.message_content')}
              </h3>
            </div>
            <div className="card-body">
              <div className="space-y-6">
                {/* Arabic Content */}
                {(message.title_ar || message.content_ar) && (
                  <div>
                    <h4 className="text-md font-medium text-gray-900 mb-3">
                      {t('messages.arabic_content')}
                    </h4>
                    {message.title_ar && (
                      <div className="mb-4">
                        <p className="text-sm font-medium text-gray-500 mb-1">
                          {t('messages.arabic_title')}
                        </p>
                        <p className="text-lg font-medium text-gray-900 rtl">
                          {message.title_ar}
                        </p>
                      </div>
                    )}
                    {message.content_ar && (
                      <div>
                        <p className="text-sm font-medium text-gray-500 mb-2">
                          {t('messages.arabic_content')}
                        </p>
                        <div 
                          className="prose prose-sm max-w-none rtl text-gray-900 bg-gray-50 p-4 rounded-lg"
                          dangerouslySetInnerHTML={{ __html: message.content_ar }}
                        />
                      </div>
                    )}
                  </div>
                )}

                {/* English Content */}
                {(message.title_en || message.content_en) && (
                  <div>
                    <h4 className="text-md font-medium text-gray-900 mb-3">
                      {t('messages.english_content')}
                    </h4>
                    {message.title_en && (
                      <div className="mb-4">
                        <p className="text-sm font-medium text-gray-500 mb-1">
                          {t('messages.english_title')}
                        </p>
                        <p className="text-lg font-medium text-gray-900 ltr">
                          {message.title_en}
                        </p>
                      </div>
                    )}
                    {message.content_en && (
                      <div>
                        <p className="text-sm font-medium text-gray-500 mb-2">
                          {t('messages.english_content')}
                        </p>
                        <div 
                          className="prose prose-sm max-w-none ltr text-gray-900 bg-gray-50 p-4 rounded-lg"
                          dangerouslySetInnerHTML={{ __html: message.content_en }}
                        />
                      </div>
                    )}
                  </div>
                )}

                {/* Default Content */}
                {!message.title_ar && !message.content_ar && !message.title_en && !message.content_en && (
                  <div>
                    <h4 className="text-md font-medium text-gray-900 mb-3">
                      {t('messages.message_content')}
                    </h4>
                    <div 
                      className="prose prose-sm max-w-none text-gray-900 bg-gray-50 p-4 rounded-lg"
                      dangerouslySetInnerHTML={{ __html: message.content }}
                    />
                  </div>
                )}
              </div>
            </div>
          </div>
        )}

        {activeTab === 'analytics' && (
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
            <div className="card">
              <div className="card-body text-center">
                <div className="text-3xl font-bold text-blue-600">
                  {stats?.sent || 0}
                </div>
                <div className="text-sm text-gray-600 mt-1">
                  {isRTL ? 'إجمالي المرسل' : 'Total Sent'}
                </div>
              </div>
            </div>
            
            <div className="card">
              <div className="card-body text-center">
                <div className="text-3xl font-bold text-green-600">
                  {stats?.opened || 0}
                </div>
                <div className="text-sm text-gray-600 mt-1">
                  {isRTL ? 'تم فتحها' : 'Opened'}
                </div>
              </div>
            </div>
            
            <div className="card">
              <div className="card-body text-center">
                <div className="text-3xl font-bold text-orange-600">
                  {stats?.clicked || 0}
                </div>
                <div className="text-sm text-gray-600 mt-1">
                  {isRTL ? 'تم النقر' : 'Clicked'}
                </div>
              </div>
            </div>
            
            <div className="card">
              <div className="card-body text-center">
                <div className="text-3xl font-bold text-red-600">
                  {stats?.bounced || 0}
                </div>
                <div className="text-sm text-gray-600 mt-1">
                  {isRTL ? 'مرتد' : 'Bounced'}
                </div>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'interactions' && (
          <div className="card">
            <div className="card-header">
              <h3 className="text-lg font-medium text-gray-900">
                {isRTL ? 'سجل التفاعلات' : 'Interaction Log'}
              </h3>
              <p className="text-sm text-gray-600 mt-1">
                {isRTL ? 'جميع التفاعلات مع هذه الرسالة' : 'All interactions with this message'}
              </p>
            </div>
            <div className="overflow-x-auto">
              <table className="table">
                <thead className="table-header">
                  <tr>
                    <th>{isRTL ? 'العميل' : 'Client'}</th>
                    <th>{isRTL ? 'البريد الإلكتروني' : 'Email'}</th>
                    <th>{isRTL ? 'النوع' : 'Type'}</th>
                    <th>{isRTL ? 'الحالة' : 'Status'}</th>
                    <th>{isRTL ? 'تاريخ الإرسال' : 'Sent At'}</th>
                    <th>{isRTL ? 'تاريخ الفتح' : 'Opened At'}</th>
                    <th>{isRTL ? 'تاريخ النقر' : 'Clicked At'}</th>
                  </tr>
                </thead>
                <tbody>
                  {interactions.length === 0 ? (
                    <tr>
                      <td colSpan="7" className="text-center py-8 text-gray-500">
                        {isRTL ? 'لا توجد تفاعلات' : 'No interactions found'}
                      </td>
                    </tr>
                  ) : (
                    interactions.map((interaction, index) => (
                      <tr key={index} className="table-row-hover">
                        <td className="font-medium">
                          {interaction.client_name || `${isRTL ? 'عميل' : 'Client'} #${interaction.client_id}`}
                        </td>
                        <td>{interaction.client_email || '-'}</td>
                        <td>
                          <span className={`badge ${
                            interaction.type === 'email_sent' ? 'badge-info' :
                            interaction.type === 'email' ? 'badge-primary' :
                            'badge-secondary'
                          }`}>
                            {interaction.type === 'email_sent' ? (isRTL ? 'إرسال' : 'Sent') :
                             interaction.type === 'email' ? (isRTL ? 'بريد' : 'Email') :
                             interaction.type}
                          </span>
                        </td>
                        <td>
                          <span className={`badge ${
                            interaction.status === 'sent' ? 'badge-success' :
                            interaction.status === 'opened' ? 'badge-primary' :
                            interaction.status === 'clicked' ? 'badge-warning' :
                            interaction.status === 'failed' ? 'badge-danger' :
                            'badge-secondary'
                          }`}>
                            {interaction.status === 'sent' ? (isRTL ? 'مرسل' : 'Sent') :
                             interaction.status === 'opened' ? (isRTL ? 'مفتوح' : 'Opened') :
                             interaction.status === 'clicked' ? (isRTL ? 'منقور' : 'Clicked') :
                             interaction.status === 'failed' ? (isRTL ? 'فشل' : 'Failed') :
                             interaction.status}
                          </span>
                        </td>
                        <td className="text-sm text-gray-500">
                          {interaction.sent_at ? formatDateTime(interaction.sent_at) : '-'}
                        </td>
                        <td className="text-sm text-gray-500">
                          {interaction.opened_at ? formatDateTime(interaction.opened_at) : '-'}
                        </td>
                        <td className="text-sm text-gray-500">
                          {interaction.clicked_at ? formatDateTime(interaction.clicked_at) : '-'}
                        </td>
                      </tr>
                    ))
                  )}
                </tbody>
              </table>
            </div>
          </div>
        )}

        {activeTab === 'recipients' && (
          <div className="card">
            <div className="card-header">
              <h3 className="text-lg font-medium text-gray-900">
                {t('messages.recipients')}
              </h3>
            </div>
            <div className="overflow-x-auto">
              <table className="table">
                <thead className="table-header">
                  <tr>
                    <th>{t('clients.client_name')}</th>
                    <th>{t('clients.client_email')}</th>
                    <th>{t('common.status')}</th>
                    <th>{t('messages.sent_at')}</th>
                    <th>{t('messages.opened_at')}</th>
                    <th>{t('messages.clicked_at')}</th>
                  </tr>
                </thead>
                <tbody className="table-body">
                  {interactions.map((interaction, index) => (
                    <tr key={index} className="table-row-hover">
                      <td className="font-medium">
                        {interaction.client_name || (isRTL ? 'عميل' : 'Client')} #{interaction.client_id}
                      </td>
                      <td>{interaction.client_email || '-'}</td>
                      <td>
                        <span className={`badge ${
                          interaction.status === 'sent' ? 'badge-success' :
                          interaction.status === 'opened' ? 'badge-primary' :
                          interaction.status === 'clicked' ? 'badge-warning' :
                          'badge-secondary'
                        }`}>
                          {interaction.status}
                        </span>
                      </td>
                      <td className="text-sm text-gray-500">
                        {interaction.sent_at ? formatDateTime(interaction.sent_at) : '-'}
                      </td>
                      <td className="text-sm text-gray-500">
                        {interaction.opened_at ? formatDateTime(interaction.opened_at) : '-'}
                      </td>
                      <td className="text-sm text-gray-500">
                        {interaction.clicked_at ? formatDateTime(interaction.clicked_at) : '-'}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default MessageDetails;
