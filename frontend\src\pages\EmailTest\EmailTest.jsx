import React, { useState, useEffect, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { Save, TestTube, CheckCircle, XCircle, Mail } from 'lucide-react';
import { useForm } from 'react-hook-form';
import toast from 'react-hot-toast';
import LoadingSpinner from '../../components/UI/LoadingSpinner';
import { apiHelpers } from '../../services/api';

const EmailTest = () => {

  const { i18n } = useTranslation();
  const isRTL = i18n.language === 'ar';
  
  const [testing, setTesting] = useState(false);
  const [testResult, setTestResult] = useState(null);
  const [loading, setLoading] = useState(true);
  const hasLoadedRef = useRef(false);
  const [formData, setFormData] = useState({
    host: '',
    port: 465,
    email: '',
    password: '',
    secure: true,
    testEmail: ''
  });

  const { register, handleSubmit, getValues, setValue, reset } = useForm({
    defaultValues: formData
  });

  const loadSavedSettings = async () => {
    // Prevent multiple calls
    if (hasLoadedRef.current) return;
    hasLoadedRef.current = true;

    try {
      setLoading(true);

      const response = await apiHelpers.get('/simple-email/settings');

      if (response.success && response.data?.data) {
        const formData = response.data.data;

        // Check if we have actual saved data (not empty)
        const hasValidData = formData.host && formData.email &&
                            formData.host.trim() !== '' && formData.email.trim() !== '' &&
                            formData.host !== 'undefined' && formData.email !== 'undefined';

        if (hasValidData) {
          // Update state and reset form
          const newFormData = {
            host: formData.host,
            port: formData.port,
            email: formData.email,
            password: formData.password,
            secure: formData.secure,
            testEmail: formData.testEmail
          };

          setFormData(newFormData);
          reset(newFormData);

          // Set individual values as backup
          setTimeout(() => {
            setValue('host', formData.host);
            setValue('port', formData.port);
            setValue('email', formData.email);
            setValue('password', formData.password);
            setValue('secure', formData.secure);
            setValue('testEmail', formData.testEmail);
          }, 100);

          toast.success(
            isRTL
              ? 'تم تحميل الإعدادات المحفوظة'
              : 'Saved settings loaded'
          );
        } else {
          toast.success(
            isRTL
              ? 'لا توجد إعدادات محفوظة، النموذج فارغ'
              : 'No saved settings found, form is empty'
          );
        }
      } else {
        console.log('⚠️ Failed to load settings or no data');
        toast.error(
          isRTL
            ? 'فشل في تحميل الإعدادات'
            : 'Failed to load settings'
        );
      }
    } catch (error) {
      toast.error(
        isRTL
          ? 'خطأ في تحميل الإعدادات'
          : 'Error loading settings'
      );
    } finally {
      setLoading(false);
    }
  };

  // Load saved settings on mount
  useEffect(() => {
    loadSavedSettings();
  }, []);

  const saveSettings = async (data) => {
    try {
      setTesting(true);
      setTestResult(null);

      const response = await apiHelpers.post('/simple-email/save-settings', data);

      if (response.success) {
        setTestResult({
          success: true,
          data: {
            message: 'Settings saved successfully',
            saved: true,
            config: response.data
          }
        });

        toast.success(
          isRTL
            ? 'تم حفظ الإعدادات بنجاح!'
            : 'Settings saved successfully!'
        );
      } else {
        setTestResult({
          success: false,
          error: response.error
        });

        toast.error(
          isRTL
            ? `فشل في حفظ الإعدادات: ${response.error}`
            : `Failed to save settings: ${response.error}`
        );
      }
    } catch (error) {
      toast.error(
        isRTL
          ? 'خطأ في حفظ الإعدادات'
          : 'Error saving settings'
      );
    } finally {
      setTesting(false);
    }
  };

  const deliveryTest = async () => {
    try {
      setTesting(true);
      setTestResult(null);

      const formData = getValues();

      const response = await apiHelpers.post('/simple-email/delivery-test', formData, {
        timeout: 60000
      });

      if (response.success) {
        setTestResult({
          success: true,
          data: {
            message: 'Delivery test completed successfully',
            connectionTime: response.data.tests?.[0]?.connectionTime || 'N/A',
            sendTime: response.data.tests?.[0]?.sendTime || 'N/A',
            emailSent: response.data.summary?.successful > 0,
            messageId: response.data.tests?.[0]?.messageId,
            config: {
              host: formData.host,
              port: formData.port,
              secure: formData.secure,
              user: formData.email
            },
            deliveryResults: response.data
          }
        });

        toast.success(
          isRTL
            ? 'تم إرسال اختبار التسليم بنجاح!'
            : 'Delivery test sent successfully!'
        );
      } else {
        setTestResult({
          success: false,
          error: response.error
        });

        toast.error(
          isRTL
            ? `فشل اختبار التسليم: ${response.error}`
            : `Delivery test failed: ${response.error}`
        );
      }
    } catch (error) {

      if (error.message?.includes('timeout')) {
        toast.success(
          isRTL
            ? 'تم إرسال الاختبار! قد يستغرق وصول البريد بضع دقائق'
            : 'Test sent! Email may take a few minutes to arrive'
        );
      } else {
        toast.error(
          isRTL
            ? 'خطأ في اختبار التسليم'
            : 'Delivery test error'
        );
      }
    } finally {
      setTesting(false);
    }
  };

  // اختبار التسليم باستخدام البيانات المحفوظة
  const deliveryTest2 = async () => {
    try {
      setTesting(true);
      setTestResult(null);

      const formData = getValues();
      const testEmail = formData.testEmail || '<EMAIL>';

      console.log('🔧 Testing with saved settings from database, target email:', testEmail);

      const response = await apiHelpers.post('/simple-email/delivery-test2', {
        testEmail: testEmail
      }, {
        timeout: 60000
      });

      if (response.success) {
        setTestResult({
          success: true,
          data: {
            message: 'Delivery test completed successfully',
            connectionTime: response.data.tests?.[0]?.connectionTime || 'N/A',
            sendTime: response.data.tests?.[0]?.sendTime || 'N/A',
            emailSent: response.data.summary?.successful > 0,
            messageId: response.data.tests?.[0]?.messageId,
            config: {
              host: formData.host,
              port: formData.port,
              secure: formData.secure,
              user: formData.email
            },
            deliveryResults: response.data
          }
        });

        toast.success(
          isRTL
            ? 'تم اختبار الإعدادات المحفوظة بنجاح!'
            : 'Saved settings test completed successfully!'
        );
      } else {
        setTestResult({
          success: false,
          error: response.error
        });

        toast.error(
          isRTL
            ? `فشل اختبار الإعدادات المحفوظة: ${response.error}`
            : `Saved settings test failed: ${response.error}`
        );
      }
    } catch (error) {
      console.error('Delivery test error:', error);

      // تحديد نتيجة الاختبار مع الخطأ
      setTestResult({
        success: false,
        error: error.response?.data?.error || error.message || 'Unknown error occurred'
      });

      if (error.message?.includes('timeout')) {
        toast.success(
          isRTL
            ? 'تم إرسال الاختبار! قد يستغرق وصول البريد بضع دقائق'
            : 'Test sent! Email may take a few minutes to arrive'
        );
      } else {
        const errorMessage = error.response?.data?.error || error.message || 'Unknown error';
        toast.error(
          isRTL
            ? `خطأ في اختبار الإعدادات المحفوظة: ${errorMessage}`
            : `Saved settings test error: ${errorMessage}`
        );
      }
    } finally {
      setTesting(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 p-6">
        <div className="max-w-4xl mx-auto">
          <div className="flex items-center justify-center py-20">
            <div className="text-center">
              <LoadingSpinner size="lg" />
              <p className="mt-4 text-gray-600">
                {isRTL ? 'جاري تحميل الإعدادات...' : 'Loading settings...'}
              </p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            {isRTL ? 'إعدادات SMTP' : 'SMTP Settings'}
          </h1>
          <p className="text-gray-600">
            {isRTL
              ? 'إعداد وإدارة خادم البريد الإلكتروني'
              : 'Configure and manage email server settings'
            }
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Settings Form */}
          <div className="bg-white rounded-lg shadow-sm p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">
              {isRTL ? 'إعدادات SMTP' : 'SMTP Settings'}
            </h2>

            <form onSubmit={handleSubmit(saveSettings)} className="space-y-4">
              {/* Host */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  {isRTL ? 'خادم SMTP' : 'SMTP Host'}
                </label>
                <input
                  type="text"
                  {...register('host', { required: true })}
                  placeholder={isRTL ? 'مثال: mail.example.com' : 'e.g., mail.example.com'}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              {/* Port */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  {isRTL ? 'المنفذ' : 'Port'}
                </label>
                <input
                  type="number"
                  {...register('port', { required: true })}
                  placeholder="465"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              {/* Email */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  {isRTL ? 'البريد الإلكتروني' : 'Email'}
                </label>
                <input
                  type="email"
                  {...register('email', { required: true })}
                  placeholder={isRTL ? 'مثال: <EMAIL>' : 'e.g., <EMAIL>'}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              {/* Password */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  {isRTL ? 'كلمة المرور' : 'Password'}
                </label>
                <input
                  type="password"
                  {...register('password', { required: true })}
                  placeholder={isRTL ? 'كلمة المرور' : 'Password'}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              {/* SSL */}
              <div className="flex items-center">
                <input
                  type="checkbox"
                  {...register('secure')}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <label className="ml-2 rtl:mr-2 rtl:ml-0 text-sm text-gray-700">
                  {isRTL ? 'استخدام SSL/TLS' : 'Use SSL/TLS'}
                </label>
              </div>

              {/* Test Email */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  {isRTL ? 'بريد الاختبار' : 'Test Email'}
                </label>
                <input
                  type="email"
                  {...register('testEmail')}
                  placeholder="<EMAIL>"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              {/* Buttons */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 pt-6">
                <button
                  type="submit"
                  disabled={testing}
                  className="flex items-center justify-center space-x-2 rtl:space-x-reverse px-6 py-3 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50"
                >
                  {testing ? (
                    <LoadingSpinner size="sm" />
                  ) : (
                    <Save className="h-4 w-4" />
                  )}
                  <span>
                    {testing
                      ? (isRTL ? 'جاري الحفظ...' : 'Saving...')
                      : (isRTL ? 'حفظ الإعدادات' : 'Save Settings')
                    }
                  </span>
                </button>

                <button
                  type="button"
                  onClick={deliveryTest}
                  disabled={testing}
                  className="flex items-center justify-center space-x-2 rtl:space-x-reverse px-6 py-3 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 disabled:opacity-50"
                >
                  {testing ? (
                    <LoadingSpinner size="sm" />
                  ) : (
                    <TestTube className="h-4 w-4" />
                  )}
                  <span>
                    {testing
                      ? (isRTL ? 'جاري اختبار التسليم...' : 'Testing delivery...')
                      : (isRTL ? 'اختبار التسليم' : 'Delivery Test')
                    }
                  </span>
                </button>

                <button
                  type="button"
                  onClick={deliveryTest2}
                  disabled={testing}
                  className="flex items-center justify-center space-x-2 rtl:space-x-reverse px-6 py-3 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 disabled:opacity-50"
                >
                  {testing ? (
                    <LoadingSpinner size="sm" />
                  ) : (
                    <TestTube className="h-4 w-4" />
                  )}
                  <span>
                    {testing
                      ? (isRTL ? 'جاري الاختبار...' : 'Testing...')
                      : (isRTL ? 'اختبار الإعدادات المحفوظة' : 'Test Saved Settings')
                    }
                  </span>
                </button>
              </div>
            </form>
          </div>

          {/* Results */}
          <div className="bg-white rounded-lg shadow-sm p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">
              {isRTL ? 'نتائج الاختبار' : 'Test Results'}
            </h2>

            {!testResult && (
              <div className="text-center py-8 text-gray-500">
                <Mail className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p>{isRTL ? 'لم يتم إجراء اختبار بعد' : 'No test performed yet'}</p>
              </div>
            )}

            {testResult && (
              <div className="space-y-4">
                {testResult.success ? (
                  <div className="p-4 bg-green-50 border border-green-200 rounded-md">
                    <div className="flex items-center space-x-2 rtl:space-x-reverse">
                      <CheckCircle className="h-5 w-5 text-green-600" />
                      <span className="font-medium text-green-800">
                        {isRTL ? 'نجح الاختبار!' : 'Test Successful!'}
                      </span>
                    </div>

                    <div className="mt-3 text-sm text-green-700 space-y-1">
                      {/* Save Results */}
                      {testResult.data.saved && (
                        <div className="p-3 bg-blue-50 border border-blue-200 rounded mb-3">
                          <p className="font-medium text-blue-800">
                            {isRTL ? '✅ تم حفظ الإعدادات بنجاح' : '✅ Settings saved successfully'}
                          </p>
                        </div>
                      )}

                      {/* Delivery Test Results */}
                      {!testResult.data.saved && (
                        <>
                          <p><strong>{isRTL ? 'وقت الاتصال:' : 'Connection Time:'}</strong> {testResult.data.connectionTime || 'N/A'}</p>
                          <p><strong>{isRTL ? 'حالة الإرسال:' : 'Email Status:'}</strong> {testResult.data.emailSent ? '✅ تم الإرسال' : '❌ لم يتم الإرسال'}</p>
                        </>
                      )}
                    </div>
                  </div>
                ) : (
                  <div className="p-4 bg-red-50 border border-red-200 rounded-md">
                    <div className="flex items-center space-x-2 rtl:space-x-reverse">
                      <XCircle className="h-5 w-5 text-red-600" />
                      <span className="font-medium text-red-800">
                        {isRTL ? 'فشل الاختبار' : 'Test Failed'}
                      </span>
                    </div>

                    <div className="mt-3 text-sm text-red-700">
                      <p><strong>{isRTL ? 'الخطأ:' : 'Error:'}</strong> {testResult.error}</p>
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default EmailTest;
