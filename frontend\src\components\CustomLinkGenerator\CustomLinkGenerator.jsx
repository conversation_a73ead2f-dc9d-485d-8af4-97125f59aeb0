import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Link, Copy, ExternalLink, Plus, Trash2 } from 'lucide-react';
import toast from 'react-hot-toast';
import apiHelpers from '../../utils/apiHelpers';

const CustomLinkGenerator = ({ onLinksChange, messageId, clientId }) => {
  const { t, i18n } = useTranslation();
  const isRTL = i18n.language === 'ar';
  
  const [customLinks, setCustomLinks] = useState([]);
  const [newLink, setNewLink] = useState({
    targetUrl: '',
    linkText: '',
    description: ''
  });
  const [isGenerating, setIsGenerating] = useState(false);

  const generateCustomLink = async () => {
    if (!newLink.targetUrl || !newLink.linkText) {
      toast.error(
        isRTL 
          ? 'يرجى إدخال الرابط والنص المطلوب'
          : 'Please enter URL and link text'
      );
      return;
    }

    try {
      setIsGenerating(true);
      
      const response = await apiHelpers.post('/tracking/generate-custom-link', {
        targetUrl: newLink.targetUrl,
        linkText: newLink.linkText,
        clientId: clientId,
        messageId: messageId
      });

      if (response.success) {
        const generatedLink = {
          id: Date.now(),
          ...newLink,
          ...response.data,
          description: newLink.description || newLink.linkText
        };

        const updatedLinks = [...customLinks, generatedLink];
        setCustomLinks(updatedLinks);
        
        // Notify parent component
        if (onLinksChange) {
          onLinksChange(updatedLinks);
        }

        // Reset form
        setNewLink({
          targetUrl: '',
          linkText: '',
          description: ''
        });

        toast.success(
          isRTL 
            ? 'تم إنشاء الرابط المخصص بنجاح'
            : 'Custom link generated successfully'
        );
      }
    } catch (error) {
      console.error('Error generating custom link:', error);
      toast.error(
        isRTL 
          ? 'خطأ في إنشاء الرابط المخصص'
          : 'Error generating custom link'
      );
    } finally {
      setIsGenerating(false);
    }
  };

  const copyToClipboard = (text, type) => {
    navigator.clipboard.writeText(text).then(() => {
      toast.success(
        isRTL 
          ? `تم نسخ ${type === 'html' ? 'كود HTML' : 'الرابط'}`
          : `${type === 'html' ? 'HTML code' : 'Link'} copied to clipboard`
      );
    });
  };

  const removeLink = (linkId) => {
    const updatedLinks = customLinks.filter(link => link.id !== linkId);
    setCustomLinks(updatedLinks);
    
    if (onLinksChange) {
      onLinksChange(updatedLinks);
    }
  };

  const insertLinkIntoContent = (htmlLink) => {
    // This function can be called by parent to insert link into content
    if (window.insertCustomLink) {
      window.insertCustomLink(htmlLink);
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border p-6">
      <div className="flex items-center gap-2 mb-4">
        <Link className="h-5 w-5 text-blue-600" />
        <h3 className="text-lg font-semibold text-gray-900">
          {isRTL ? 'إنشاء روابط مخصصة مع التتبع' : 'Generate Custom Tracking Links'}
        </h3>
      </div>

      {/* Add New Link Form */}
      <div className="space-y-4 mb-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              {isRTL ? 'الرابط المستهدف' : 'Target URL'}
            </label>
            <input
              type="url"
              className="input"
              placeholder="https://example.com"
              value={newLink.targetUrl}
              onChange={(e) => setNewLink(prev => ({ ...prev, targetUrl: e.target.value }))}
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              {isRTL ? 'نص الرابط' : 'Link Text'}
            </label>
            <input
              type="text"
              className="input"
              placeholder={isRTL ? 'زيارة موقعنا' : 'Visit our website'}
              value={newLink.linkText}
              onChange={(e) => setNewLink(prev => ({ ...prev, linkText: e.target.value }))}
            />
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            {isRTL ? 'وصف الرابط (اختياري)' : 'Link Description (Optional)'}
          </label>
          <input
            type="text"
            className="input"
            placeholder={isRTL ? 'وصف مختصر للرابط' : 'Brief description of the link'}
            value={newLink.description}
            onChange={(e) => setNewLink(prev => ({ ...prev, description: e.target.value }))}
          />
        </div>

        <button
          onClick={generateCustomLink}
          disabled={isGenerating || !newLink.targetUrl || !newLink.linkText}
          className="btn btn-primary flex items-center gap-2"
        >
          <Plus className="h-4 w-4" />
          {isGenerating 
            ? (isRTL ? 'جاري الإنشاء...' : 'Generating...') 
            : (isRTL ? 'إنشاء رابط مخصص' : 'Generate Custom Link')
          }
        </button>
      </div>

      {/* Generated Links List */}
      {customLinks.length > 0 && (
        <div className="space-y-4">
          <h4 className="text-md font-medium text-gray-900">
            {isRTL ? 'الروابط المخصصة المُنشأة' : 'Generated Custom Links'}
          </h4>
          
          {customLinks.map((link) => (
            <div key={link.id} className="border rounded-lg p-4 bg-gray-50">
              <div className="flex items-start justify-between mb-3">
                <div className="flex-1">
                  <h5 className="font-medium text-gray-900">{link.linkText}</h5>
                  {link.description && (
                    <p className="text-sm text-gray-600 mt-1">{link.description}</p>
                  )}
                  <p className="text-sm text-blue-600 mt-1 break-all">{link.targetUrl}</p>
                </div>
                
                <button
                  onClick={() => removeLink(link.id)}
                  className="text-red-600 hover:text-red-800 p-1"
                  title={isRTL ? 'حذف الرابط' : 'Remove link'}
                >
                  <Trash2 className="h-4 w-4" />
                </button>
              </div>

              <div className="space-y-2">
                {/* Tracking URL */}
                <div className="flex items-center gap-2">
                  <span className="text-sm font-medium text-gray-700 min-w-[100px]">
                    {isRTL ? 'رابط التتبع:' : 'Tracking URL:'}
                  </span>
                  <code className="flex-1 text-xs bg-white p-2 rounded border break-all">
                    {link.trackingUrl}
                  </code>
                  <button
                    onClick={() => copyToClipboard(link.trackingUrl, 'url')}
                    className="text-blue-600 hover:text-blue-800 p-1"
                    title={isRTL ? 'نسخ الرابط' : 'Copy URL'}
                  >
                    <Copy className="h-4 w-4" />
                  </button>
                </div>

                {/* HTML Code */}
                <div className="flex items-center gap-2">
                  <span className="text-sm font-medium text-gray-700 min-w-[100px]">
                    {isRTL ? 'كود HTML:' : 'HTML Code:'}
                  </span>
                  <code className="flex-1 text-xs bg-white p-2 rounded border break-all">
                    {link.htmlLink}
                  </code>
                  <button
                    onClick={() => copyToClipboard(link.htmlLink, 'html')}
                    className="text-blue-600 hover:text-blue-800 p-1"
                    title={isRTL ? 'نسخ كود HTML' : 'Copy HTML'}
                  >
                    <Copy className="h-4 w-4" />
                  </button>
                </div>

                {/* Insert Button */}
                <div className="flex justify-end">
                  <button
                    onClick={() => insertLinkIntoContent(link.htmlLink)}
                    className="btn btn-sm btn-secondary flex items-center gap-1"
                  >
                    <ExternalLink className="h-3 w-3" />
                    {isRTL ? 'إدراج في المحتوى' : 'Insert into Content'}
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Instructions */}
      <div className="mt-6 p-4 bg-blue-50 rounded-lg">
        <h5 className="font-medium text-blue-900 mb-2">
          {isRTL ? 'تعليمات الاستخدام:' : 'Usage Instructions:'}
        </h5>
        <ul className="text-sm text-blue-800 space-y-1">
          <li>
            {isRTL 
              ? '• أدخل الرابط المستهدف ونص الرابط المطلوب عرضه'
              : '• Enter the target URL and the text you want to display'
            }
          </li>
          <li>
            {isRTL 
              ? '• انقر على "إنشاء رابط مخصص" لإنتاج رابط مع تتبع النقرات'
              : '• Click "Generate Custom Link" to create a link with click tracking'
            }
          </li>
          <li>
            {isRTL 
              ? '• انسخ كود HTML وألصقه في محتوى الرسالة'
              : '• Copy the HTML code and paste it into your message content'
            }
          </li>
          <li>
            {isRTL 
              ? '• سيتم تسجيل جميع النقرات على هذه الروابط تلقائياً'
              : '• All clicks on these links will be automatically tracked'
            }
          </li>
        </ul>
      </div>
    </div>
  );
};

export default CustomLinkGenerator;
