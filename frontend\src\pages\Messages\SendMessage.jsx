import React, { useState, useEffect, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { useParams, useNavigate } from 'react-router-dom';
import { 
  Send, 
  ArrowLeft, 
  ArrowRight,
  Mail,
  Users,
  Clock,
  CheckCircle,
  AlertCircle,
  Calendar
} from 'lucide-react';
import { toast } from 'react-hot-toast';
import apiHelpers from '../../utils/apiHelpers';
import LoadingSpinner from '../../components/UI/LoadingSpinner';

const SendMessage = () => {
  const { t, i18n } = useTranslation();
  const isRTL = i18n.language === 'ar';
  const { id } = useParams();
  const navigate = useNavigate();

  const [loading, setLoading] = useState(false);
  const [dataLoading, setDataLoading] = useState(true);
  const [error, setError] = useState(null);
  const [message, setMessage] = useState(null);
  const [sendProgress, setSendProgress] = useState(0);
  const [sendStatus, setSendStatus] = useState('ready'); // ready, sending, completed, failed
  const [sendResults, setSendResults] = useState({
    total: 0,
    sent: 0,
    failed: 0,
    errors: []
  });

  const [sendOptions, setSendOptions] = useState({
    send_immediately: true,
    scheduled_at: '',
    test_email: '',
    send_test_first: false
  });

  const fetchedRef = useRef(false);

  useEffect(() => {
    if (id && !fetchedRef.current) {
      fetchedRef.current = true;
      fetchMessage();
    }
  }, [id]);

  const loadTargetClients = async (clientIds) => {
    try {
      console.log('🔍 Loading target clients:', clientIds);

      // Load client details for the target clients
      const clientPromises = clientIds.map(async (clientId) => {
        try {
          const response = await apiHelpers.get(`/clients/${clientId}`);
          if (response.success) {
            return response.data;
          }
          return null;
        } catch (error) {
          console.error(`Error loading client ${clientId}:`, error);
          return null;
        }
      });

      const clients = await Promise.all(clientPromises);
      const validClients = clients.filter(client => client !== null);

      console.log(`✅ Loaded ${validClients.length} valid clients out of ${clientIds.length}`);

      // Update message with actual client count
      setMessage(prev => ({
        ...prev,
        total_recipients: validClients.length,
        target_clients: clientIds,
        loaded_clients: validClients
      }));

      setSendResults(prev => ({
        ...prev,
        total: validClients.length
      }));

    } catch (error) {
      console.error('Error loading target clients:', error);
      toast.error(
        isRTL
          ? 'فشل في تحميل بيانات العملاء المستهدفين'
          : 'Failed to load target clients data'
      );
    }
  };

  const fetchMessage = async () => {
    try {
      // Only skip if we're already loading AND have a message
      if (dataLoading && message) {
        console.log('⚠️ Skipping duplicate request - already loading message');
        return;
      }

      // Allow initial load even if dataLoading is true
      if (!dataLoading) {
        setDataLoading(true);
      }
      console.log('🔍 Fetching message with ID:', id);

      // Try to fetch the actual message from API
      const response = await apiHelpers.get(`/messages/${id}`);
      console.log('📧 Message API response:', response);

      let messageData;
      if (response.success && response.data) {
        // Use actual message data
        console.log('✅ Message found:', response.data);
        messageData = response.data;
      } else {
        // Show error instead of fallback data
        console.error('❌ Message not found or API error:', response);
        setError(
          isRTL
            ? response.message_ar || 'الرسالة غير موجودة'
            : response.error || 'Message not found'
        );
        setDataLoading(false);
        return;
      }

      setMessage(messageData);
      setSendResults(prev => ({
        ...prev,
        total: messageData.total_recipients || 0
      }));

      // Load target clients if they exist
      if (messageData.target_clients && Array.isArray(messageData.target_clients) && messageData.target_clients.length > 0) {
        console.log(`📋 Loading ${messageData.target_clients.length} target clients for message`);
        await loadTargetClients(messageData.target_clients);
      } else {
        console.log('⚠️ No target clients found for this message');
      }
    } catch (error) {
      console.error('Error fetching message:', error);
      toast.error(
        isRTL 
          ? 'فشل في تحميل بيانات الرسالة' 
          : 'Failed to load message data'
      );
      navigate('/messages');
    } finally {
      setDataLoading(false);
    }
  };

  const handleRetry = () => {
    console.log('🔄 Retrying message send');
    setSendStatus('ready');
    setSendResults({ total: 0, sent: 0, failed: 0, errors: [] });
    setSendProgress(0);
  };

  const handleSendTest = async () => {
    if (!sendOptions.test_email) {
      toast.error(
        isRTL 
          ? 'يرجى إدخال بريد إلكتروني للاختبار' 
          : 'Please enter a test email address'
      );
      return;
    }

    try {
      setLoading(true);
      
      const response = await apiHelpers.post(`/messages/${id}/test`, {
        testEmail: sendOptions.test_email,
        clientIds: message.target_clients || []
      });

      if (response.success) {
        toast.success(
          isRTL 
            ? 'تم إرسال الرسالة التجريبية بنجاح' 
            : 'Test message sent successfully'
        );
      }
    } catch (error) {
      console.error('Error sending test message:', error);
      toast.error(
        isRTL 
          ? 'فشل في إرسال الرسالة التجريبية' 
          : 'Failed to send test message'
      );
    } finally {
      setLoading(false);
    }
  };

  const handleSendMessage = async () => {
    // Check if there are target clients
    if (!message.target_clients || message.target_clients.length === 0) {
      toast.error(
        isRTL
          ? 'لا يمكن إرسال الرسالة بدون تحديد عملاء مستهدفين'
          : 'Cannot send message without target clients'
      );
      return;
    }

    if (!window.confirm(
      isRTL
        ? `هل أنت متأكد من إرسال الرسالة إلى ${message.total_recipients} عميل؟`
        : `Are you sure you want to send the message to ${message.total_recipients} clients?`
    )) {
      return;
    }

    try {
      setLoading(true);
      setSendStatus('sending');
      setSendProgress(0);

      const payload = {
        send_immediately: sendOptions.send_immediately,
        scheduled_at: sendOptions.scheduled_at || null,
        client_ids: message.target_clients || [], // Use client_ids as expected by server
        title: message.title,
        title_ar: message.title_ar,
        subject: message.subject,
        subject_ar: message.subject_ar,
        content: message.content,
        content_ar: message.content_ar,
        template_id: message.template_id
      };

      console.log('📤 Sending message with payload:', {
        messageId: id,
        client_ids: payload.client_ids,
        clientCount: payload.client_ids.length
      });

      // Simulate sending progress
      const progressInterval = setInterval(() => {
        setSendProgress(prev => {
          if (prev >= 95) {
            clearInterval(progressInterval);
            return 95;
          }
          return prev + Math.random() * 10;
        });
      }, 500);

      const response = await apiHelpers.post(`/messages/${id}/send`, payload);

      clearInterval(progressInterval);
      setSendProgress(100);

      if (response.success) {
        console.log('✅ Message sent successfully:', response.data);
        setSendStatus('completed');
        setSendResults({
          total: response.data.total_recipients || message.total_recipients,
          sent: response.data.successful_sends || 0,
          failed: response.data.failed_sends || 0,
          errors: response.data.errors || []
        });

        toast.success(
          isRTL
            ? `تم إرسال الرسالة بنجاح إلى ${response.data.successful_sends} عميل`
            : `Message sent successfully to ${response.data.successful_sends} clients`
        );
      }
    } catch (error) {
      console.error('Error sending message:', error);
      setSendStatus('failed');
      setSendResults({
        total: message.total_recipients,
        sent: 0,
        failed: message.total_recipients,
        errors: [error.message || 'Unknown error occurred']
      });
      toast.error(
        isRTL
          ? `فشل في إرسال الرسالة: ${error.message}`
          : `Failed to send message: ${error.message}`
      );
    } finally {
      setLoading(false);
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'ready':
        return <Clock className="h-5 w-5 text-blue-600" />;
      case 'sending':
        return <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-600"></div>;
      case 'completed':
        return <CheckCircle className="h-5 w-5 text-green-600" />;
      case 'failed':
        return <AlertCircle className="h-5 w-5 text-red-600" />;
      default:
        return <Clock className="h-5 w-5 text-gray-400" />;
    }
  };

  const getStatusText = (status) => {
    switch (status) {
      case 'ready':
        return isRTL ? 'جاهز للإرسال' : 'Ready to Send';
      case 'sending':
        return isRTL ? 'جاري الإرسال...' : 'Sending...';
      case 'completed':
        return isRTL ? 'تم الإرسال' : 'Completed';
      case 'failed':
        return isRTL ? 'فشل الإرسال' : 'Failed';
      default:
        return isRTL ? 'غير معروف' : 'Unknown';
    }
  };

  if (dataLoading) {
    return (
      <div className="min-h-screen bg-gray-50 p-6">
        <div className="max-w-4xl mx-auto">
          <div className="flex items-center justify-center py-20">
            <LoadingSpinner size="lg" />
            <span className="ml-3 rtl:mr-3 rtl:ml-0 text-gray-600">
              {isRTL ? 'جاري تحميل البيانات...' : 'Loading data...'}
            </span>
          </div>
        </div>
      </div>
    );
  }

  if (error || !message) {
    return (
      <div className="min-h-screen bg-gray-50 p-6">
        <div className="max-w-4xl mx-auto">
          <div className="text-center py-20">
            <Mail className="h-12 w-12 mx-auto text-gray-300 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              {error || (isRTL ? 'الرسالة غير موجودة' : 'Message not found')}
            </h3>
            <p className="text-gray-500 mb-6">
              {error ? (
                isRTL
                  ? 'حدث خطأ أثناء تحميل الرسالة'
                  : 'An error occurred while loading the message'
              ) : (
                isRTL
                  ? 'لم يتم العثور على الرسالة المطلوبة'
                  : 'The requested message could not be found'
              )}
            </p>
            <button
              onClick={() => navigate('/messages')}
              className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
            >
              {isRTL ? <ArrowRight className="h-4 w-4 ml-2" /> : <ArrowLeft className="h-4 w-4 mr-2" />}
              {isRTL ? 'العودة للرسائل' : 'Back to Messages'}
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="w-full">
        {/* Header */}
        <div className="px-6 py-4 bg-white border-b border-gray-200">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
            <div className="flex items-center space-x-4 rtl:space-x-reverse">
              <button
                onClick={() => navigate('/messages')}
                className="p-2 text-gray-400 hover:text-gray-600"
              >
                {isRTL ? <ArrowRight className="h-5 w-5" /> : <ArrowLeft className="h-5 w-5" />}
              </button>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">
                  {isRTL ? 'إرسال الرسالة' : 'Send Message'}
                </h1>
                <p className="text-gray-600">
                  {isRTL ? (message.title_ar || message.title) : message.title}
                </p>
              </div>
            </div>
            <div className="flex items-center space-x-3 rtl:space-x-reverse">
              {getStatusIcon(sendStatus)}
              <span className="text-sm font-medium text-gray-700">
                {getStatusText(sendStatus)}
              </span>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="px-6 py-6">
          <div className="max-w-4xl mx-auto space-y-6">
            {/* Message Summary */}
            <div className="bg-white rounded-lg shadow-sm p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">
                {isRTL ? 'ملخص الرسالة' : 'Message Summary'}
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="flex items-center">
                  <Mail className="h-5 w-5 text-blue-600 mr-3 rtl:ml-3 rtl:mr-0" />
                  <div>
                    <p className="text-sm text-gray-500">
                      {isRTL ? 'موضوع البريد' : 'Subject'}
                    </p>
                    <p className="font-medium text-gray-900">
                      {isRTL ? (message.subject_ar || message.subject) : message.subject}
                    </p>
                  </div>
                </div>
                <div className="flex items-center">
                  <Users className="h-5 w-5 text-green-600 mr-3 rtl:ml-3 rtl:mr-0" />
                  <div>
                    <p className="text-sm text-gray-500">
                      {isRTL ? 'المستقبلين' : 'Recipients'}
                    </p>
                    <p className="font-medium text-gray-900">
                      {message.total_recipients} {isRTL ? 'عميل' : 'clients'}
                    </p>
                  </div>
                </div>
                <div className="flex items-center">
                  <Calendar className="h-5 w-5 text-purple-600 mr-3 rtl:ml-3 rtl:mr-0" />
                  <div>
                    <p className="text-sm text-gray-500">
                      {isRTL ? 'نوع الحملة' : 'Campaign Type'}
                    </p>
                    <p className="font-medium text-gray-900">
                      {message.campaign_type === 'manual' 
                        ? (isRTL ? 'يدوية' : 'Manual')
                        : (isRTL ? 'تلقائية' : 'Automated')
                      }
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* Target Clients */}
            {message.loaded_clients && message.loaded_clients.length > 0 && (
              <div className="bg-white rounded-lg shadow-sm p-6">
                <h3 className="text-lg font-medium text-gray-900 mb-4">
                  {isRTL ? 'العملاء المستهدفين' : 'Target Clients'}
                </h3>
                <div className="space-y-3">
                  <div className="text-sm text-gray-600 mb-3">
                    {isRTL
                      ? `سيتم إرسال الرسالة إلى ${message.loaded_clients.length} عميل:`
                      : `Message will be sent to ${message.loaded_clients.length} clients:`
                    }
                  </div>
                  <div className="max-h-40 overflow-y-auto border rounded-md">
                    {message.loaded_clients.map((client, index) => (
                      <div key={client.id} className="flex items-center justify-between p-3 border-b last:border-b-0">
                        <div className="flex items-center space-x-3 rtl:space-x-reverse">
                          <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                            <span className="text-sm font-medium text-blue-600">
                              {client.name ? client.name.charAt(0).toUpperCase() : 'C'}
                            </span>
                          </div>
                          <div>
                            <p className="font-medium text-gray-900">{client.name}</p>
                            <p className="text-sm text-gray-500">{client.email}</p>
                          </div>
                        </div>
                        <div className="text-sm text-gray-400">
                          #{index + 1}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            )}

            {/* No Target Clients Warning */}
            {(!message.target_clients || message.target_clients.length === 0) && (
              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <svg className="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div className="ml-3 rtl:mr-3 rtl:ml-0">
                    <h3 className="text-sm font-medium text-yellow-800">
                      {isRTL ? 'تحذير: لا توجد عملاء مستهدفين' : 'Warning: No Target Clients'}
                    </h3>
                    <div className="mt-2 text-sm text-yellow-700">
                      <p>
                        {isRTL
                          ? 'لم يتم تحديد أي عملاء لهذه الرسالة. يرجى العودة إلى صفحة إنشاء الرسالة وتحديد العملاء المستهدفين.'
                          : 'No clients have been selected for this message. Please go back to the message creation page and select target clients.'
                        }
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Send Options */}
            {sendStatus === 'ready' && (
              <div className="bg-white rounded-lg shadow-sm p-6">
                <h3 className="text-lg font-medium text-gray-900 mb-4">
                  {isRTL ? 'خيارات الإرسال' : 'Send Options'}
                </h3>
                <div className="space-y-4">
                  {/* Test Email */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      {isRTL ? 'إرسال رسالة تجريبية أولاً' : 'Send Test Email First'}
                    </label>
                    <div className="flex items-center space-x-3 rtl:space-x-reverse">
                      <input
                        type="email"
                        value={sendOptions.test_email}
                        onChange={(e) => setSendOptions({ ...sendOptions, test_email: e.target.value })}
                        placeholder={isRTL ? 'البريد الإلكتروني للاختبار' : 'Test email address'}
                        className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      />
                      <button
                        onClick={handleSendTest}
                        disabled={loading || !sendOptions.test_email}
                        className="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 disabled:opacity-50"
                      >
                        {isRTL ? 'إرسال تجريبي' : 'Send Test'}
                      </button>
                    </div>
                  </div>

                  {/* Scheduling */}
                  <div>
                    <label className="flex items-center space-x-2 rtl:space-x-reverse">
                      <input
                        type="radio"
                        name="send_timing"
                        checked={sendOptions.send_immediately}
                        onChange={() => setSendOptions({ ...sendOptions, send_immediately: true, scheduled_at: '' })}
                        className="text-blue-600"
                      />
                      <span className="text-sm font-medium text-gray-700">
                        {isRTL ? 'إرسال فوري' : 'Send Immediately'}
                      </span>
                    </label>
                  </div>

                  <div>
                    <label className="flex items-center space-x-2 rtl:space-x-reverse mb-2">
                      <input
                        type="radio"
                        name="send_timing"
                        checked={!sendOptions.send_immediately}
                        onChange={() => setSendOptions({ ...sendOptions, send_immediately: false })}
                        className="text-blue-600"
                      />
                      <span className="text-sm font-medium text-gray-700">
                        {isRTL ? 'جدولة الإرسال' : 'Schedule Send'}
                      </span>
                    </label>
                    {!sendOptions.send_immediately && (
                      <input
                        type="datetime-local"
                        value={sendOptions.scheduled_at}
                        onChange={(e) => setSendOptions({ ...sendOptions, scheduled_at: e.target.value })}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        min={new Date().toISOString().slice(0, 16)}
                      />
                    )}
                  </div>
                </div>
              </div>
            )}

            {/* Send Progress */}
            {sendStatus === 'sending' && (
              <div className="bg-white rounded-lg shadow-sm p-6">
                <h3 className="text-lg font-medium text-gray-900 mb-4">
                  {isRTL ? 'تقدم الإرسال' : 'Send Progress'}
                </h3>
                <div className="space-y-4">
                  <div>
                    <div className="flex justify-between text-sm text-gray-600 mb-1">
                      <span>{isRTL ? 'جاري الإرسال...' : 'Sending...'}</span>
                      <span>{Math.round(sendProgress)}%</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div 
                        className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                        style={{ width: `${sendProgress}%` }}
                      ></div>
                    </div>
                  </div>
                  <p className="text-sm text-gray-600">
                    {isRTL 
                      ? `جاري إرسال الرسالة إلى ${message.total_recipients} عميل...`
                      : `Sending message to ${message.total_recipients} clients...`
                    }
                  </p>
                </div>
              </div>
            )}

            {/* Send Results */}
            {(sendStatus === 'completed' || sendStatus === 'failed') && (
              <div className="bg-white rounded-lg shadow-sm p-6">
                <h3 className="text-lg font-medium text-gray-900 mb-4">
                  {isRTL ? 'نتائج الإرسال' : 'Send Results'}
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                  <div className="text-center p-4 bg-blue-50 rounded-lg">
                    <p className="text-2xl font-bold text-blue-600">{sendResults.total}</p>
                    <p className="text-sm text-gray-600">{isRTL ? 'إجمالي' : 'Total'}</p>
                  </div>
                  <div className="text-center p-4 bg-green-50 rounded-lg">
                    <p className="text-2xl font-bold text-green-600">{sendResults.sent}</p>
                    <p className="text-sm text-gray-600">{isRTL ? 'مرسل' : 'Sent'}</p>
                  </div>
                  <div className="text-center p-4 bg-red-50 rounded-lg">
                    <p className="text-2xl font-bold text-red-600">{sendResults.failed}</p>
                    <p className="text-sm text-gray-600">{isRTL ? 'فشل' : 'Failed'}</p>
                  </div>
                </div>

                {sendResults.errors.length > 0 && (
                  <div>
                    <h4 className="text-sm font-medium text-gray-900 mb-2">
                      {isRTL ? 'أخطاء الإرسال:' : 'Send Errors:'}
                    </h4>
                    <div className="bg-red-50 border border-red-200 rounded-lg p-3">
                      <ul className="text-sm text-red-700 space-y-1">
                        {sendResults.errors.map((error, index) => (
                          <li key={index}>• {error}</li>
                        ))}
                      </ul>
                    </div>
                  </div>
                )}

                {/* Retry Button for Failed Sends */}
                {sendStatus === 'failed' && (
                  <div className="mt-4 flex justify-center">
                    <button
                      onClick={handleRetry}
                      className="flex items-center space-x-2 rtl:space-x-reverse px-6 py-2 bg-red-600 text-white rounded-md hover:bg-red-700"
                    >
                      <Send className="h-4 w-4" />
                      <span>{isRTL ? 'إعادة المحاولة' : 'Retry Send'}</span>
                    </button>
                  </div>
                )}
              </div>
            )}

            {/* Action Buttons */}
            <div className="flex items-center justify-end space-x-4 rtl:space-x-reverse">
              <button
                onClick={() => navigate('/messages')}
                className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
              >
                {sendStatus === 'completed' || sendStatus === 'failed' 
                  ? (isRTL ? 'العودة للرسائل' : 'Back to Messages')
                  : (isRTL ? 'إلغاء' : 'Cancel')
                }
              </button>
              
              {sendStatus === 'ready' && (
                <button
                  onClick={handleSendMessage}
                  disabled={
                    loading ||
                    (!sendOptions.send_immediately && !sendOptions.scheduled_at) ||
                    !message.target_clients ||
                    message.target_clients.length === 0
                  }
                  className="flex items-center space-x-2 rtl:space-x-reverse px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {loading && <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>}
                  <Send className="h-4 w-4" />
                  <span>
                    {loading 
                      ? (isRTL ? 'جاري الإرسال...' : 'Sending...')
                      : (sendOptions.send_immediately 
                          ? (isRTL ? 'إرسال الآن' : 'Send Now')
                          : (isRTL ? 'جدولة الإرسال' : 'Schedule Send')
                        )
                    }
                  </span>
                </button>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SendMessage;
