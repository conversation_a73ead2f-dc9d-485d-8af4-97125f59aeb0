import React from 'react';
import { useTranslation } from 'react-i18next';

const SettingsTest = () => {
  const { t, i18n } = useTranslation();
  const isRTL = i18n.language === 'ar';

  console.log('🔧 SettingsTest component rendering...');

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-4xl mx-auto">
        <div className="bg-white rounded-lg shadow-sm p-6">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">
            {isRTL ? 'صفحة الإعدادات - اختبار' : 'Settings Page - Test'}
          </h1>
          
          <div className="space-y-4">
            <div className="p-4 bg-green-50 border border-green-200 rounded-md">
              <p className="text-green-800">
                {isRTL 
                  ? '✅ تم تحميل صفحة الإعدادات بنجاح!'
                  : '✅ Settings page loaded successfully!'
                }
              </p>
            </div>
            
            <div className="p-4 bg-blue-50 border border-blue-200 rounded-md">
              <h3 className="font-medium text-blue-900 mb-2">
                {isRTL ? 'معلومات التشخيص:' : 'Debug Information:'}
              </h3>
              <ul className="text-sm text-blue-800 space-y-1">
                <li>• {isRTL ? 'اللغة الحالية:' : 'Current Language:'} {i18n.language}</li>
                <li>• {isRTL ? 'اتجاه النص:' : 'Text Direction:'} {isRTL ? 'RTL' : 'LTR'}</li>
                <li>• {isRTL ? 'الوقت:' : 'Time:'} {new Date().toLocaleString()}</li>
              </ul>
            </div>
            
            <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-md">
              <p className="text-yellow-800">
                {isRTL 
                  ? 'هذه صفحة اختبار للتأكد من أن مسار الإعدادات يعمل بشكل صحيح.'
                  : 'This is a test page to ensure the settings route works correctly.'
                }
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SettingsTest;
