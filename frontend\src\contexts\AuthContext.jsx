import React, { createContext, useContext, useReducer, useEffect, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import toast from 'react-hot-toast';
import api from '../services/api';

// Initial state
const initialState = {
  user: null,
  token: localStorage.getItem('token'),
  isAuthenticated: false,
  isLoading: true,
  error: null
};

// Action types
const AUTH_ACTIONS = {
  LOGIN_START: 'LOGIN_START',
  LOGIN_SUCCESS: 'LOGIN_SUCCESS',
  LOGIN_FAILURE: 'LOGIN_FAILURE',
  LOGOUT: 'LOGOUT',
  LOAD_USER_START: 'LOAD_USER_START',
  LOAD_USER_SUCCESS: 'LOAD_USER_SUCCESS',
  LOAD_USER_FAILURE: 'LOAD_USER_FAILURE',
  UPDATE_PROFILE: 'UPDATE_PROFILE',
  C<PERSON>AR_ERROR: 'CLEAR_ERROR'
};

// Reducer
const authReducer = (state, action) => {
  switch (action.type) {
    case AUTH_ACTIONS.LOGIN_START:
    case AUTH_ACTIONS.LOAD_USER_START:
      return {
        ...state,
        isLoading: true,
        error: null
      };

    case AUTH_ACTIONS.LOGIN_SUCCESS:
      return {
        ...state,
        user: action.payload.user,
        token: action.payload.token,
        isAuthenticated: true,
        isLoading: false,
        error: null
      };

    case AUTH_ACTIONS.LOAD_USER_SUCCESS:
      return {
        ...state,
        user: action.payload,
        isAuthenticated: true,
        isLoading: false,
        error: null
      };

    case AUTH_ACTIONS.LOGIN_FAILURE:
    case AUTH_ACTIONS.LOAD_USER_FAILURE:
      return {
        ...state,
        user: null,
        token: null,
        isAuthenticated: false,
        isLoading: false,
        error: action.payload
      };

    case AUTH_ACTIONS.LOGOUT:
      return {
        ...state,
        user: null,
        token: null,
        isAuthenticated: false,
        isLoading: false,
        error: null
      };

    case AUTH_ACTIONS.UPDATE_PROFILE:
      return {
        ...state,
        user: { ...state.user, ...action.payload }
      };

    case AUTH_ACTIONS.CLEAR_ERROR:
      return {
        ...state,
        error: null
      };

    default:
      return state;
  }
};

// Create context
const AuthContext = createContext();

// Auth provider component
export const AuthProvider = ({ children }) => {
  const [state, dispatch] = useReducer(authReducer, initialState);
  const { t } = useTranslation();
  const verificationInProgress = useRef(false);
  const verificationPromise = useRef(null);

  // Load user on app start
  useEffect(() => {
    let isMounted = true; // Flag to prevent state updates if component unmounts

    const loadUser = async () => {
      if (!isMounted) return;
      // Prevent multiple simultaneous verification attempts
      if (verificationInProgress.current) {
        console.log('⚠️ AuthContext: Verification already in progress, waiting for existing request');
        return verificationPromise.current;
      }

      if (state.loading) {
        console.log('⚠️ AuthContext: Already loading user, skipping duplicate verification');
        return;
      }

      const token = localStorage.getItem('token');

      console.log('🔍 AuthContext: Checking for existing token...', token ? 'Found' : 'Not found');

      if (token) {
        try {
          console.log('🔍 AuthContext: Verifying token with backend...');
          verificationInProgress.current = true;
          dispatch({ type: AUTH_ACTIONS.LOAD_USER_START });

          // Set token in API headers
          api.defaults.headers.common['Authorization'] = `Bearer ${token}`;

          // Create verification promise and store it
          verificationPromise.current = api.get('/auth/verify');

          // Verify token and get user data
          const response = await verificationPromise.current;

          if (response.data.success) {
            console.log('✅ AuthContext: Token verified successfully, user logged in');
            dispatch({
              type: AUTH_ACTIONS.LOAD_USER_SUCCESS,
              payload: response.data.data.user
            });
          } else {
            throw new Error('Token verification failed');
          }
        } catch (error) {
          console.warn('❌ AuthContext: Token verification failed:', error.response?.status, error.response?.data?.error);
          localStorage.removeItem('token');
          delete api.defaults.headers.common['Authorization'];
          dispatch({
            type: AUTH_ACTIONS.LOAD_USER_FAILURE,
            payload: 'Session expired'
          });
        } finally {
          // Clean up verification state
          verificationInProgress.current = false;
          verificationPromise.current = null;
        }
      } else {
        console.log('ℹ️  AuthContext: No token found, user not logged in');
        dispatch({
          type: AUTH_ACTIONS.LOAD_USER_FAILURE,
          payload: null
        });
        // Clean up verification state
        verificationInProgress.current = false;
        verificationPromise.current = null;
      }
    };

    loadUser();

    // Cleanup function to prevent memory leaks
    return () => {
      isMounted = false;
    };
  }, []);

  // Login function
  const login = async (credentials, redirectTo = null) => {
    try {
      dispatch({ type: AUTH_ACTIONS.LOGIN_START });

      const response = await api.post('/auth/login', credentials);

      if (response.data.success) {
        const { token, user } = response.data.data;

        // Store token
        localStorage.setItem('token', token);

        // Set token in API headers
        api.defaults.headers.common['Authorization'] = `Bearer ${token}`;

        dispatch({
          type: AUTH_ACTIONS.LOGIN_SUCCESS,
          payload: { token, user }
        });

        toast.success(t('auth.login_success'));

        // Return success with redirect information
        return {
          success: true,
          redirectTo: redirectTo || '/dashboard'
        };
      } else {
        throw new Error(response.data.error || 'Login failed');
      }
    } catch (error) {
      const errorMessage = error.response?.data?.error || error.message || 'Login failed';

      dispatch({
        type: AUTH_ACTIONS.LOGIN_FAILURE,
        payload: errorMessage
      });

      toast.error(t('auth.login_error'));
      return { success: false, error: errorMessage };
    }
  };

  // Logout function
  const logout = async () => {
    try {
      // Call logout endpoint
      await api.post('/auth/logout');
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      // Clear local storage and state regardless of API call result
      localStorage.removeItem('token');
      delete api.defaults.headers.common['Authorization'];
      
      dispatch({ type: AUTH_ACTIONS.LOGOUT });
      
      toast.success(t('auth.logout_success'));
    }
  };

  // Update profile function
  const updateProfile = async (profileData) => {
    try {
      const response = await api.put('/auth/profile', profileData);

      if (response.data.success) {
        dispatch({
          type: AUTH_ACTIONS.UPDATE_PROFILE,
          payload: response.data.data
        });

        toast.success(t('auth.profile_updated'));
        return { success: true };
      } else {
        throw new Error(response.data.error || 'Profile update failed');
      }
    } catch (error) {
      const errorMessage = error.response?.data?.error || error.message || 'Profile update failed';
      toast.error(errorMessage);
      return { success: false, error: errorMessage };
    }
  };

  // Change password function
  const changePassword = async (passwordData) => {
    try {
      const response = await api.put('/auth/password', passwordData);

      if (response.data.success) {
        toast.success(t('auth.password_changed'));
        return { success: true };
      } else {
        throw new Error(response.data.error || 'Password change failed');
      }
    } catch (error) {
      const errorMessage = error.response?.data?.error || error.message || 'Password change failed';
      toast.error(errorMessage);
      return { success: false, error: errorMessage };
    }
  };

  // Clear error function
  const clearError = () => {
    dispatch({ type: AUTH_ACTIONS.CLEAR_ERROR });
  };

  // Check if user has specific role
  const hasRole = (role) => {
    return state.user?.role === role;
  };

  // Check if user is admin
  const isAdmin = () => {
    return hasRole('admin');
  };

  // Context value
  const value = {
    // State
    user: state.user,
    token: state.token,
    isAuthenticated: state.isAuthenticated,
    isLoading: state.isLoading,
    error: state.error,
    
    // Actions
    login,
    logout,
    updateProfile,
    changePassword,
    clearError,
    
    // Utilities
    hasRole,
    isAdmin
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

// Custom hook to use auth context
export const useAuth = () => {
  const context = useContext(AuthContext);
  
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  
  return context;
};

export default AuthContext;
