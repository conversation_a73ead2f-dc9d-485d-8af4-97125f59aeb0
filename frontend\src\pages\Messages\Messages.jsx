import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import {
  Plus,
  Search,
  Filter,
  Send,
  Eye,
  Edit,
  Trash2,
  MoreVertical,
  Calendar,
  MessageSquare,
  Clock,
  Copy
} from 'lucide-react';
import { useForm } from 'react-hook-form';
import toast from 'react-hot-toast';
import LoadingSpinner from '../../components/UI/LoadingSpinner';
import { apiHelpers, endpoints } from '../../services/api';

const Messages = () => {
  const { t, i18n } = useTranslation();
  const navigate = useNavigate();
  const [messages, setMessages] = useState([]);
  const [loading, setLoading] = useState(true);
  const [totalPages, setTotalPages] = useState(1);
  const [currentPage, setCurrentPage] = useState(1);
  const [showFilters, setShowFilters] = useState(false);
  const [selectedMessages, setSelectedMessages] = useState([]);
  const [actionMenuOpen, setActionMenuOpen] = useState(null);

  const isRTL = i18n.language === 'ar';

  const { register, watch, reset } = useForm({
    defaultValues: {
      search: '',
      type: '',
      category: '',
      language: '',
      status: ''
    }
  });

  const filters = watch();

  // Load messages when page changes
  useEffect(() => {
    console.log('📋 Loading messages - Page:', currentPage);
    loadMessages();
  }, [currentPage]);

  // Load messages when filters change (with debounce)
  useEffect(() => {
    const hasFilters = filters.search || filters.status || filters.language || filters.type || filters.category;

    if (hasFilters) {
      const timeoutId = setTimeout(() => {
        console.log('📋 Loading messages with filters');
        if (currentPage !== 1) {
          setCurrentPage(1); // This will trigger the first useEffect
        } else {
          loadMessages(); // Load directly if already on page 1
        }
      }, 300);
      return () => clearTimeout(timeoutId);
    }
  }, [filters.search, filters.status, filters.language, filters.type, filters.category]);

  const loadMessages = async () => {
    try {
      // Only skip if we're loading AND already have messages
      if (loading && messages.length > 0) {
        console.log('⚠️ Skipping duplicate request - already loading with data');
        return;
      }

      // Allow initial load even if loading is true
      if (!loading) {
        setLoading(true);
      }
      console.log('🔄 Loading messages from API...');

      const params = new URLSearchParams({
        page: currentPage,
        limit: 10,
        search: filters.search || '',
        type: filters.type || '',
        category: filters.category || '',
        language: filters.language || '',
        status: filters.status || ''
      });

      const result = await apiHelpers.get(`${endpoints.messages.list}?${params}`);
      
      if (result.success) {
        setMessages(result.data.data?.messages || []);
        setTotalPages(result.data.data?.totalPages || 1);
        console.log(`✅ Loaded ${result.data.data?.messages?.length || 0} messages`);
      }
    } catch (error) {
      console.error('Error loading messages:', error);
      toast.error(t('messages.error_loading'));
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteMessage = async (messageId) => {
    if (!confirm(t('messages.delete_confirm'))) return;

    try {
      const result = await apiHelpers.delete(endpoints.messages.delete(messageId));
      
      if (result.success) {
        toast.success(t('messages_ui.success_delete'));
        loadMessages();
      }
    } catch (error) {
      toast.error(t('messages_ui.error_delete'));
    }
  };

  const handleSendMessage = async (messageId) => {
    if (!confirm(t('messages.confirm_send'))) return;

    try {
      // Navigate to send message page with pre-selected message
      navigate(`/messages/${messageId}/send`);
    } catch (error) {
      toast.error(t('messages_ui.error_send'));
    }
  };

  const handleDuplicateMessage = async (messageId) => {
    try {
      // Get message details
      const response = await apiHelpers.get(`/messages/${messageId}`);
      if (response.success) {
        const messageData = response.data;

        // Navigate to create message page with data
        navigate('/messages/new', {
          state: {
            duplicateData: {
              title: `${messageData.title} - Copy`,
              title_ar: `${messageData.title_ar || messageData.title} - نسخة`,
              content: messageData.content,
              content_ar: messageData.content_ar,
              subject: messageData.subject,
              subject_ar: messageData.subject_ar,
              type: messageData.type,
              category: messageData.category,
              language: messageData.language,
              template_id: messageData.template_id,
              target_clients: messageData.target_clients || []
            }
          }
        });

        toast.success(
          isRTL
            ? 'تم نسخ الرسالة بنجاح'
            : 'Message duplicated successfully'
        );
      }
    } catch (error) {
      console.error('Error duplicating message:', error);
      toast.error(
        isRTL
          ? 'فشل في نسخ الرسالة'
          : 'Failed to duplicate message'
      );
    }
  };

  const handleBulkDelete = async () => {
    if (selectedMessages.length === 0) return;
    if (!confirm(t('messages.delete_confirm'))) return;

    try {
      await Promise.all(
        selectedMessages.map(id => apiHelpers.delete(endpoints.messages.delete(id)))
      );
      
      toast.success(t('messages_ui.success_delete'));
      setSelectedMessages([]);
      loadMessages();
    } catch (error) {
      toast.error(t('messages_ui.error_delete'));
    }
  };

  const handleSelectAll = (checked) => {
    if (checked) {
      setSelectedMessages(messages.map(message => message.id));
    } else {
      setSelectedMessages([]);
    }
  };

  const handleSelectMessage = (messageId, checked) => {
    if (checked) {
      setSelectedMessages([...selectedMessages, messageId]);
    } else {
      setSelectedMessages(selectedMessages.filter(id => id !== messageId));
    }
  };

  const getStatusBadge = (status) => {
    const statusConfig = {
      draft: { class: 'badge-secondary', text: t('messages.draft') },
      published: { class: 'badge-primary', text: t('messages.published') },
      scheduled: { class: 'badge-warning', text: t('messages.scheduled') },
      sent: { class: 'badge-success', text: t('messages.sent') }
    };
    
    const config = statusConfig[status] || statusConfig.draft;
    return <span className={`badge ${config.class}`}>{config.text}</span>;
  };

  const getTypeBadge = (type) => {
    const typeConfig = {
      manual: { class: 'badge-blue', text: t('messages.manual') },
      automated: { class: 'badge-purple', text: t('messages.automated') },
      promotional: { class: 'badge-green', text: t('messages.promotional') },
      newsletter: { class: 'badge-orange', text: t('messages.newsletter') },
      notification: { class: 'badge-red', text: t('messages.notification') }
    };
    
    const config = typeConfig[type] || typeConfig.manual;
    return <span className={`badge ${config.class}`}>{config.text}</span>;
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      calendar: 'gregory'
    });
  };

  if (loading && messages.length === 0) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="large" text={t('common.loading')} />
      </div>
    );
  }

  return (
    <div className="flex-1 overflow-hidden">
      <div className="h-full flex flex-col space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">
            {t('messages.title')}
          </h1>
          <p className="mt-2 text-gray-600">
            {t('messages.message_list')}
          </p>
        </div>
        <div className="flex items-center space-x-3 rtl:space-x-reverse">
          <button
            onClick={() => navigate('/messages/new')}
            className="btn btn-primary flex items-center space-x-2 rtl:space-x-reverse"
          >
            <Plus className="h-4 w-4" />
            <span>{t('messages.add_message')}</span>
          </button>
        </div>
      </div>

      {/* Search and Filters */}
      <div className="card">
        <div className="card-body">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
            {/* Search */}
            <div className="flex-1 max-w-lg">
              <div className="relative">
                <div className={`absolute inset-y-0 ${isRTL ? 'right-0 pr-3' : 'left-0 pl-3'} flex items-center pointer-events-none`}>
                  <Search className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  type="text"
                  placeholder={t('messages.search_messages')}
                  className={`input ${isRTL ? 'pr-10' : 'pl-10'}`}
                  {...register('search')}
                />
              </div>
            </div>

            {/* Filter Toggle */}
            <div className="flex items-center space-x-3 rtl:space-x-reverse">
              <button
                onClick={() => setShowFilters(!showFilters)}
                className="btn btn-outline flex items-center space-x-2 rtl:space-x-reverse"
              >
                <Filter className="h-4 w-4" />
                <span>{t('common.filter')}</span>
              </button>

              {selectedMessages.length > 0 && (
                <button
                  onClick={handleBulkDelete}
                  className="btn btn-danger flex items-center space-x-2 rtl:space-x-reverse"
                >
                  <Trash2 className="h-4 w-4" />
                  <span>{t('messages.bulk_delete')} ({selectedMessages.length})</span>
                </button>
              )}
            </div>
          </div>

          {/* Filters */}
          {showFilters && (
            <div className="mt-4 pt-4 border-t border-gray-200">
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    {t('messages.message_type')}
                  </label>
                  <select className="input" {...register('type')}>
                    <option value="">{t('common.all')}</option>
                    <option value="manual">{t('messages.manual')}</option>
                    <option value="automated">{t('messages.automated')}</option>
                    <option value="promotional">{t('messages.promotional')}</option>
                    <option value="newsletter">{t('messages.newsletter')}</option>
                    <option value="notification">{t('messages.notification')}</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    {t('messages.message_category')}
                  </label>
                  <select className="input" {...register('category')}>
                    <option value="">{t('common.all')}</option>
                    <option value="general">{t('clients.general')}</option>
                    <option value="premium">{t('clients.premium')}</option>
                    <option value="vip">{t('clients.vip')}</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    {t('messages.message_language')}
                  </label>
                  <select className="input" {...register('language')}>
                    <option value="">{t('common.all')}</option>
                    <option value="ar">العربية</option>
                    <option value="en">English</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    {t('messages.message_status')}
                  </label>
                  <select className="input" {...register('status')}>
                    <option value="">{t('common.all')}</option>
                    <option value="draft">{t('messages.draft')}</option>
                    <option value="published">{t('messages.published')}</option>
                    <option value="scheduled">{t('messages.scheduled')}</option>
                    <option value="sent">{t('messages.sent')}</option>
                  </select>
                </div>
              </div>

              <div className="mt-4 flex justify-end">
                <button
                  onClick={() => reset()}
                  className="btn btn-outline"
                >
                  {t('common.clear_filters')}
                </button>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Messages Table */}
      <div className="card relative mb-8">
        <div className="overflow-x-auto overflow-y-visible">
          <table className="table">
            <thead className="table-header">
              <tr>
                <th className="w-12">
                  <input
                    type="checkbox"
                    checked={selectedMessages.length === messages.length && messages.length > 0}
                    onChange={(e) => handleSelectAll(e.target.checked)}
                    className="rounded border-gray-300"
                  />
                </th>
                <th>{t('messages.message_title')}</th>
                <th>{t('messages.message_type')}</th>
                <th>{t('messages.message_language')}</th>
                <th>{t('messages.message_status')}</th>
                <th>{t('messages.message_created')}</th>
                <th>{t('messages.message_scheduled')}</th>
                <th className="w-12">{t('common.actions')}</th>
              </tr>
            </thead>
            <tbody className="table-body">
              {messages.map((message) => (
                <tr
                  key={message.id}
                  className="table-row-hover cursor-pointer"
                  onClick={() => navigate(`/messages/${message.id}`)}
                >
                  <td>
                    <input
                      type="checkbox"
                      checked={selectedMessages.includes(message.id)}
                      onChange={(e) => handleSelectMessage(message.id, e.target.checked)}
                      className="rounded border-gray-300"
                    />
                  </td>
                  <td>
                    <div className="flex items-center space-x-3 rtl:space-x-reverse">
                      <div className="h-8 w-8 bg-blue-100 rounded-lg flex items-center justify-center">
                        <MessageSquare className="h-4 w-4 text-blue-600" />
                      </div>
                      <div>
                        <p className="font-medium text-gray-900">
                          {message.title}
                        </p>
                        {message.content && (
                          <p className="text-sm text-gray-500 truncate max-w-xs">
                            {message.content.replace(/<[^>]*>/g, '').substring(0, 50)}...
                          </p>
                        )}
                      </div>
                    </div>
                  </td>
                  <td>{getTypeBadge(message.type)}</td>
                  <td>
                    <span className="text-sm text-gray-600">
                      {message.language === 'ar' ? 'العربية' : 'English'}
                    </span>
                  </td>
                  <td>{getStatusBadge(message.status)}</td>
                  <td className="text-sm text-gray-500">
                    {formatDate(message.created_at)}
                  </td>
                  <td className="text-sm text-gray-500">
                    {message.scheduled_at ? (
                      <div className="flex items-center space-x-1 rtl:space-x-reverse">
                        <Clock className="h-4 w-4" />
                        <span>{formatDate(message.scheduled_at)}</span>
                      </div>
                    ) : (
                      '-'
                    )}
                  </td>
                  <td onClick={(e) => e.stopPropagation()}>
                    <div className="relative">
                      <button
                        onClick={() => setActionMenuOpen(actionMenuOpen === message.id ? null : message.id)}
                        className="p-1 text-gray-400 hover:text-gray-600"
                      >
                        <MoreVertical className="h-4 w-4" />
                      </button>

                      {actionMenuOpen === message.id && (
                        <div className={`
                          absolute ${isRTL ? 'left-0' : 'right-0'} mt-2 w-48 bg-white rounded-md shadow-lg ring-1 ring-black ring-opacity-5 z-50
                        `}>
                          <div className="py-1">
                            <button
                              onClick={() => {
                                navigate(`/messages/${message.id}`);
                                setActionMenuOpen(null);
                              }}
                              className="flex items-center space-x-2 rtl:space-x-reverse w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                            >
                              <Eye className="h-4 w-4" />
                              <span>{t('common.view')}</span>
                            </button>
                            
                            <button
                              onClick={() => {
                                navigate(`/messages/${message.id}/edit`);
                                setActionMenuOpen(null);
                              }}
                              className="flex items-center space-x-2 rtl:space-x-reverse w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                            >
                              <Edit className="h-4 w-4" />
                              <span>{t('common.edit')}</span>
                            </button>

                            <button
                              onClick={() => {
                                handleDuplicateMessage(message.id);
                                setActionMenuOpen(null);
                              }}
                              className="flex items-center space-x-2 rtl:space-x-reverse w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                            >
                              <Copy className="h-4 w-4" />
                              <span>{isRTL ? 'نسخ الرسالة' : 'Duplicate Message'}</span>
                            </button>

                            {(message.status === 'published' || message.status === 'draft' || message.status === 'ready') && (
                              <button
                                onClick={() => {
                                  handleSendMessage(message.id);
                                  setActionMenuOpen(null);
                                }}
                                className="flex items-center space-x-2 rtl:space-x-reverse w-full px-4 py-2 text-sm text-blue-600 hover:bg-blue-50"
                              >
                                <Send className="h-4 w-4" />
                                <span>{t('messages.send_message')}</span>
                              </button>
                            )}

                            <hr className="my-1" />

                            <button
                              onClick={() => {
                                handleDeleteMessage(message.id);
                                setActionMenuOpen(null);
                              }}
                              className="flex items-center space-x-2 rtl:space-x-reverse w-full px-4 py-2 text-sm text-red-600 hover:bg-red-50"
                            >
                              <Trash2 className="h-4 w-4" />
                              <span>{t('common.delete')}</span>
                            </button>
                          </div>
                        </div>
                      )}
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>

          {messages.length === 0 && !loading && (
            <div className="text-center py-12">
              <MessageSquare className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">
                {t('messages_ui.no_data')}
              </h3>
              <p className="mt-1 text-sm text-gray-500">
                {isRTL ? 'لا توجد رسائل حالياً' : 'No messages found'}
              </p>
              <div className="mt-6">
                <button
                  onClick={() => navigate('/messages/new')}
                  className="btn btn-primary"
                >
                  {t('messages.add_message')}
                </button>
              </div>
            </div>
          )}
        </div>

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="card-footer">
            <div className="flex items-center justify-between">
              <div className="text-sm text-gray-700">
                {isRTL 
                  ? `عرض ${messages.length} من ${totalPages * 10} رسالة`
                  : `Showing ${messages.length} of ${totalPages * 10} messages`
                }
              </div>
              <div className="flex items-center space-x-2 rtl:space-x-reverse">
                <button
                  onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                  disabled={currentPage === 1}
                  className="btn btn-outline btn-sm"
                >
                  {t('common.previous')}
                </button>
                <span className="text-sm text-gray-700">
                  {currentPage} {t('common.of')} {totalPages}
                </span>
                <button
                  onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                  disabled={currentPage === totalPages}
                  className="btn btn-outline btn-sm"
                >
                  {t('common.next')}
                </button>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Click outside to close action menu */}
      {actionMenuOpen && (
        <div
          className="fixed inset-0 z-5"
          onClick={() => setActionMenuOpen(null)}
        />
      )}
      </div>
    </div>
  );
};

export default Messages;
