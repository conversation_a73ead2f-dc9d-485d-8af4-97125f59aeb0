const { db } = require('../config/database');

class Client {
  // Get all clients with pagination and filtering
  static getAll(page = 1, limit = 10, filters = {}) {
    return new Promise((resolve, reject) => {
      const offset = (page - 1) * limit;
      let query = 'SELECT * FROM clients WHERE 1=1';
      let countQuery = 'SELECT COUNT(*) as total FROM clients WHERE 1=1';
      const params = [];

      // Apply filters
      if (filters.category) {
        query += ' AND category = ?';
        countQuery += ' AND category = ?';
        params.push(filters.category);
      }

      if (filters.language) {
        query += ' AND language = ?';
        countQuery += ' AND language = ?';
        params.push(filters.language);
      }

      if (filters.status) {
        query += ' AND status = ?';
        countQuery += ' AND status = ?';
        params.push(filters.status);
      }

      if (filters.search) {
        query += ' AND (name LIKE ? OR email LIKE ? OR company LIKE ?)';
        countQuery += ' AND (name LIKE ? OR email LIKE ? OR company LIKE ?)';
        const searchTerm = `%${filters.search}%`;
        params.push(searchTerm, searchTerm, searchTerm);
      }

      query += ' ORDER BY created_at DESC LIMIT ? OFFSET ?';
      params.push(limit, offset);

      // Get total count
      db.get(countQuery, params.slice(0, -2), (err, countResult) => {
        if (err) {
          reject(err);
          return;
        }

        // Get clients
        db.all(query, params, (err, rows) => {
          if (err) {
            reject(err);
          } else {
            resolve({
              clients: rows,
              total: countResult.total,
              page,
              limit,
              totalPages: Math.ceil(countResult.total / limit)
            });
          }
        });
      });
    });
  }

  // Get client by ID
  static getById(id) {
    return new Promise((resolve, reject) => {
      db.get('SELECT * FROM clients WHERE id = ?', [id], (err, row) => {
        if (err) {
          reject(err);
        } else {
          resolve(row);
        }
      });
    });
  }

  // Get client by email
  static getByEmail(email) {
    return new Promise((resolve, reject) => {
      db.get('SELECT * FROM clients WHERE email = ?', [email], (err, row) => {
        if (err) {
          reject(err);
        } else {
          resolve(row);
        }
      });
    });
  }

  // Create new client
  static create(clientData) {
    return new Promise((resolve, reject) => {
      const {
        name, email, phone, company, category, language, status, tags, notes
      } = clientData;

      const query = `
        INSERT INTO clients (name, email, phone, company, category, language, status, tags, notes)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
      `;

      db.run(query, [
        name, email, phone, company, 
        category || 'general', 
        language || 'ar', 
        status || 'active', 
        tags, notes
      ], function(err) {
        if (err) {
          reject(err);
        } else {
          resolve({ id: this.lastID, ...clientData });
        }
      });
    });
  }

  // Update client
  static update(id, clientData) {
    return new Promise((resolve, reject) => {
      const {
        name, email, phone, company, category, language, status, tags, notes
      } = clientData;

      const query = `
        UPDATE clients 
        SET name = ?, email = ?, phone = ?, company = ?, category = ?, 
            language = ?, status = ?, tags = ?, notes = ?, updated_at = CURRENT_TIMESTAMP
        WHERE id = ?
      `;

      db.run(query, [
        name, email, phone, company, category, language, status, tags, notes, id
      ], function(err) {
        if (err) {
          reject(err);
        } else {
          resolve({ id, changes: this.changes });
        }
      });
    });
  }

  // Delete client
  static delete(id) {
    return new Promise((resolve, reject) => {
      db.run('DELETE FROM clients WHERE id = ?', [id], function(err) {
        if (err) {
          reject(err);
        } else {
          resolve({ id, changes: this.changes });
        }
      });
    });
  }

  // Bulk insert clients (for Excel import)
  static bulkInsert(clientsData) {
    return new Promise((resolve, reject) => {
      const query = `
        INSERT OR IGNORE INTO clients (name, email, phone, company, category, language, status, tags, notes)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
      `;

      db.serialize(() => {
        db.run('BEGIN TRANSACTION');
        
        let completed = 0;
        let errors = [];
        let inserted = 0;

        clientsData.forEach((client, index) => {
          db.run(query, [
            client.name, client.email, client.phone, client.company,
            client.category || 'general',
            client.language || 'ar',
            client.status || 'active',
            client.tags, client.notes
          ], function(err) {
            completed++;
            
            if (err) {
              errors.push({ index, error: err.message, client });
            } else if (this.changes > 0) {
              inserted++;
            }

            if (completed === clientsData.length) {
              if (errors.length === 0) {
                db.run('COMMIT', (err) => {
                  if (err) {
                    reject(err);
                  } else {
                    resolve({ inserted, total: clientsData.length, errors });
                  }
                });
              } else {
                db.run('ROLLBACK', () => {
                  resolve({ inserted, total: clientsData.length, errors });
                });
              }
            }
          });
        });
      });
    });
  }

  // Get client statistics
  static getStatistics() {
    return new Promise((resolve, reject) => {
      console.log('📊 Getting client statistics...');
      const queries = {
        total: 'SELECT COUNT(*) as count FROM clients',
        active: 'SELECT COUNT(*) as count FROM clients WHERE status = "active"',
        byCategory: 'SELECT category, COUNT(*) as count FROM clients GROUP BY category',
        byLanguage: 'SELECT language, COUNT(*) as count FROM clients GROUP BY language',
        recent: 'SELECT COUNT(*) as count FROM clients WHERE created_at >= datetime("now", "-30 days")'
      };

      const results = {};
      let completed = 0;
      const totalQueries = Object.keys(queries).length;

      Object.entries(queries).forEach(([key, query]) => {
        if (key === 'byCategory' || key === 'byLanguage') {
          db.all(query, (err, rows) => {
            if (err) {
              reject(err);
              return;
            }
            results[key] = rows;
            completed++;
            if (completed === totalQueries) {
              console.log('✅ Client statistics completed:', results);
              resolve(results);
            }
          });
        } else {
          db.get(query, (err, row) => {
            if (err) {
              reject(err);
              return;
            }
            results[key] = row.count;
            completed++;
            if (completed === totalQueries) {
              resolve(results);
            }
          });
        }
      });
    });
  }
}

module.exports = Client;
