const express = require('express');
const router = express.Router();
const { auth } = require('../middleware/auth');
const { db } = require('../config/database');

// Get all client categories
router.get('/', auth, async (req, res) => {
  try {
    const categories = await new Promise((resolve, reject) => {
      db.all(
        'SELECT * FROM client_categories WHERE is_active = 1 ORDER BY sort_order ASC, name ASC',
        [],
        (err, rows) => {
          if (err) reject(err);
          else resolve(rows);
        }
      );
    });

    res.json({
      success: true,
      data: categories
    });

  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'Failed to fetch client categories',
      error_ar: 'فشل في استرجاع فئات العملاء'
    });
  }
});

// Get single client category
router.get('/:id', auth, async (req, res) => {
  try {
    const { id } = req.params;

    const category = await new Promise((resolve, reject) => {
      db.get(
        'SELECT * FROM client_categories WHERE id = ?',
        [id],
        (err, row) => {
          if (err) reject(err);
          else resolve(row);
        }
      );
    });

    if (!category) {
      return res.status(404).json({
        success: false,
        error: 'Category not found',
        error_ar: 'الفئة غير موجودة'
      });
    }

    res.json({
      success: true,
      data: category
    });

  } catch (error) {
    console.error('Error fetching client category:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch client category',
      error_ar: 'فشل في استرجاع فئة العميل'
    });
  }
});

// Create new client category
router.post('/', auth, async (req, res) => {
  try {
    const {
      name,
      name_ar,
      description,
      description_ar,
      color = '#6b7280',
      icon = 'users',
      sort_order = 0
    } = req.body;

    // Validate required fields
    if (!name) {
      return res.status(400).json({
        success: false,
        error: 'Category name is required',
        error_ar: 'اسم الفئة مطلوب'
      });
    }

    // Check if category name already exists
    const existingCategory = await new Promise((resolve, reject) => {
      db.get(
        'SELECT id FROM client_categories WHERE name = ? OR name_ar = ?',
        [name, name_ar],
        (err, row) => {
          if (err) reject(err);
          else resolve(row);
        }
      );
    });

    if (existingCategory) {
      return res.status(400).json({
        success: false,
        error: 'Category name already exists',
        error_ar: 'اسم الفئة موجود بالفعل'
      });
    }

    // Insert new category
    const result = await new Promise((resolve, reject) => {
      db.run(
        `INSERT INTO client_categories (
          name, name_ar, description, description_ar, color, icon, sort_order
        ) VALUES (?, ?, ?, ?, ?, ?, ?)`,
        [name, name_ar, description, description_ar, color, icon, sort_order],
        function(err) {
          if (err) reject(err);
          else resolve({ id: this.lastID });
        }
      );
    });

    res.status(201).json({
      success: true,
      data: {
        id: result.id,
        message: 'Category created successfully',
        message_ar: 'تم إنشاء الفئة بنجاح'
      }
    });

  } catch (error) {
    console.error('Error creating client category:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to create client category',
      error_ar: 'فشل في إنشاء فئة العميل'
    });
  }
});

// Update client category
router.put('/:id', auth, async (req, res) => {
  try {
    const { id } = req.params;
    const {
      name,
      name_ar,
      description,
      description_ar,
      color,
      icon,
      sort_order,
      is_active
    } = req.body;

    // Check if category exists
    const existingCategory = await new Promise((resolve, reject) => {
      db.get(
        'SELECT * FROM client_categories WHERE id = ?',
        [id],
        (err, row) => {
          if (err) reject(err);
          else resolve(row);
        }
      );
    });

    if (!existingCategory) {
      return res.status(404).json({
        success: false,
        error: 'Category not found',
        error_ar: 'الفئة غير موجودة'
      });
    }

    // Update category
    await new Promise((resolve, reject) => {
      db.run(
        `UPDATE client_categories SET 
          name = ?, name_ar = ?, description = ?, description_ar = ?, 
          color = ?, icon = ?, sort_order = ?, is_active = ?, 
          updated_at = CURRENT_TIMESTAMP
         WHERE id = ?`,
        [name, name_ar, description, description_ar, color, icon, sort_order, is_active, id],
        function(err) {
          if (err) reject(err);
          else resolve();
        }
      );
    });

    res.json({
      success: true,
      data: {
        message: 'Category updated successfully',
        message_ar: 'تم تحديث الفئة بنجاح'
      }
    });

  } catch (error) {
    console.error('Error updating client category:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to update client category',
      error_ar: 'فشل في تحديث فئة العميل'
    });
  }
});

// Delete client category
router.delete('/:id', auth, async (req, res) => {
  try {
    const { id } = req.params;

    // Check if category exists
    const existingCategory = await new Promise((resolve, reject) => {
      db.get(
        'SELECT * FROM client_categories WHERE id = ?',
        [id],
        (err, row) => {
          if (err) reject(err);
          else resolve(row);
        }
      );
    });

    if (!existingCategory) {
      return res.status(404).json({
        success: false,
        error: 'Category not found',
        error_ar: 'الفئة غير موجودة'
      });
    }

    // Check if category is being used by clients
    const clientsUsingCategory = await new Promise((resolve, reject) => {
      db.get(
        'SELECT COUNT(*) as count FROM clients WHERE category = ?',
        [existingCategory.name.toLowerCase()],
        (err, row) => {
          if (err) reject(err);
          else resolve(row);
        }
      );
    });

    if (clientsUsingCategory.count > 0) {
      return res.status(400).json({
        success: false,
        error: `Cannot delete category. ${clientsUsingCategory.count} clients are using this category.`,
        error_ar: `لا يمكن حذف الفئة. ${clientsUsingCategory.count} عميل يستخدم هذه الفئة.`
      });
    }

    // Delete category
    await new Promise((resolve, reject) => {
      db.run(
        'DELETE FROM client_categories WHERE id = ?',
        [id],
        function(err) {
          if (err) reject(err);
          else resolve();
        }
      );
    });

    res.json({
      success: true,
      data: {
        message: 'Category deleted successfully',
        message_ar: 'تم حذف الفئة بنجاح'
      }
    });

  } catch (error) {
    console.error('Error deleting client category:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to delete client category',
      error_ar: 'فشل في حذف فئة العميل'
    });
  }
});

// Reorder categories
router.post('/reorder', auth, async (req, res) => {
  try {
    const { categories } = req.body; // Array of {id, sort_order}

    if (!Array.isArray(categories)) {
      return res.status(400).json({
        success: false,
        error: 'Categories array is required',
        error_ar: 'مصفوفة الفئات مطلوبة'
      });
    }

    // Update sort order for each category
    for (const category of categories) {
      await new Promise((resolve, reject) => {
        db.run(
          'UPDATE client_categories SET sort_order = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
          [category.sort_order, category.id],
          function(err) {
            if (err) reject(err);
            else resolve();
          }
        );
      });
    }

    res.json({
      success: true,
      data: {
        message: 'Categories reordered successfully',
        message_ar: 'تم إعادة ترتيب الفئات بنجاح'
      }
    });

  } catch (error) {
    console.error('Error reordering categories:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to reorder categories',
      error_ar: 'فشل في إعادة ترتيب الفئات'
    });
  }
});

module.exports = router;
