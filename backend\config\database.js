const sqlite3 = require('sqlite3').verbose();
const path = require('path');
const fs = require('fs');
const bcrypt = require('bcrypt');

// Database path
const DB_PATH = process.env.DB_PATH || path.join(__dirname, '../../database/marketing_email.db');

// Ensure database directory exists
const dbDir = path.dirname(DB_PATH);
if (!fs.existsSync(dbDir)) {
  fs.mkdirSync(dbDir, { recursive: true });
}

// Create database connection
const db = new sqlite3.Database(DB_PATH, (err) => {
  if (err) {
    console.error('Error opening database:', err.message);
    console.error('خطأ في فتح قاعدة البيانات:', err.message);
  } else {
    console.log('Connected to SQLite database');
    console.log('تم الاتصال بقاعدة بيانات SQLite');
  }
});

// Enable foreign keys
db.run('PRAGMA foreign_keys = ON');

// Database schema
const createTables = () => {
  return new Promise((resolve, reject) => {
    db.serialize(() => {
      // Users table
      db.run(`
        CREATE TABLE IF NOT EXISTS users (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          username TEXT UNIQUE NOT NULL,
          email TEXT UNIQUE NOT NULL,
          password TEXT NOT NULL,
          role TEXT DEFAULT 'admin',
          first_name TEXT,
          last_name TEXT,
          phone TEXT,
          bio TEXT,
          permissions TEXT DEFAULT '[]',
          status TEXT DEFAULT 'active',
          last_login DATETIME,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
      `, (err) => {
        if (err) {
          console.error('Error creating users table:', err);
          reject(err);
        }
      });

      // Clients table
      db.run(`
        CREATE TABLE IF NOT EXISTS clients (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          name TEXT NOT NULL,
          email TEXT UNIQUE NOT NULL,
          phone TEXT,
          company TEXT,
          category TEXT DEFAULT 'general',
          language TEXT DEFAULT 'ar',
          status TEXT DEFAULT 'active',
          tags TEXT,
          notes TEXT,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
      `, (err) => {
        if (err) {
          console.error('Error creating clients table:', err);
          reject(err);
        } else {
          // Add new columns if they don't exist
          const newColumns = [
            { name: 'interest_level', type: 'TEXT DEFAULT "unknown"' },
            { name: 'source', type: 'TEXT' },
            { name: 'last_activity', type: 'DATETIME' },
            { name: 'total_emails_sent', type: 'INTEGER DEFAULT 0' },
            { name: 'total_emails_opened', type: 'INTEGER DEFAULT 0' },
            { name: 'total_clicks', type: 'INTEGER DEFAULT 0' },
            { name: 'engagement_score', type: 'INTEGER DEFAULT 0' },
            { name: 'unsubscribed', type: 'BOOLEAN DEFAULT 0' },
            { name: 'unsubscribed_at', type: 'DATETIME' },
            { name: 'unsubscribe_token', type: 'TEXT' }
          ];

          newColumns.forEach(column => {
            db.run(`ALTER TABLE clients ADD COLUMN ${column.name} ${column.type}`, (err) => {
              if (err && !err.message.includes('duplicate column name')) {
                console.error(`Error adding column ${column.name}:`, err);
              } else if (!err) {
                console.log(`Added column ${column.name} to clients table`);
              }
            });
          });
        }
      });

      // Messages table
      db.run(`
        CREATE TABLE IF NOT EXISTS messages (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          title TEXT NOT NULL,
          title_ar TEXT,
          title_en TEXT,
          content TEXT NOT NULL,
          content_ar TEXT,
          content_en TEXT,
          type TEXT DEFAULT 'manual',
          category TEXT,
          language TEXT DEFAULT 'ar',
          status TEXT DEFAULT 'draft',
          scheduled_at DATETIME,
          created_by TEXT,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
      `, (err) => {
        if (err) {
          console.error('Error creating messages table:', err);
          reject(err);
        } else {
          // Add new columns to messages table if they don't exist
          const messageColumns = [
            { name: 'subject', type: 'TEXT' },
            { name: 'subject_ar', type: 'TEXT' },
            { name: 'sender_email', type: 'TEXT' },
            { name: 'sender_name', type: 'TEXT' },
            { name: 'template_id', type: 'INTEGER' },
            { name: 'total_recipients', type: 'INTEGER DEFAULT 0' },
            { name: 'total_sent', type: 'INTEGER DEFAULT 0' },
            { name: 'total_delivered', type: 'INTEGER DEFAULT 0' },
            { name: 'total_opened', type: 'INTEGER DEFAULT 0' },
            { name: 'total_clicked', type: 'INTEGER DEFAULT 0' },
            { name: 'campaign_type', type: 'TEXT DEFAULT "manual"' },
            { name: 'recurring_frequency', type: 'TEXT' },
            { name: 'target_criteria', type: 'TEXT' },
            { name: 'target_clients', type: 'TEXT' },
            { name: 'sent_at', type: 'DATETIME' }
          ];

          messageColumns.forEach(column => {
            db.run(`ALTER TABLE messages ADD COLUMN ${column.name} ${column.type}`, (err) => {
              if (err && !err.message.includes('duplicate column name')) {
                console.error(`Error adding column ${column.name} to messages:`, err);
              } else if (!err) {
                console.log(`Added column ${column.name} to messages table`);
              }
            });
          });
        }
      });

      // Interactions table (email sends, opens, clicks, etc.)
      db.run(`
        CREATE TABLE IF NOT EXISTS interactions (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          client_id INTEGER NOT NULL,
          message_id INTEGER NOT NULL,
          type TEXT NOT NULL,
          status TEXT DEFAULT 'pending',
          tracking_id TEXT UNIQUE,
          sent_at DATETIME,
          opened_at DATETIME,
          clicked_at DATETIME,
          bounced_at DATETIME,
          error_message TEXT,
          metadata TEXT,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (client_id) REFERENCES clients (id) ON DELETE CASCADE,
          FOREIGN KEY (message_id) REFERENCES messages (id) ON DELETE CASCADE
        )
      `, (err) => {
        if (err) {
          console.error('Error creating interactions table:', err);
          reject(err);
        } else {
          // Add new columns to interactions table if they don't exist
          const interactionColumns = [
            { name: 'open_count', type: 'INTEGER DEFAULT 0' },
            { name: 'click_count', type: 'INTEGER DEFAULT 0' },
            { name: 'last_opened_at', type: 'DATETIME' },
            { name: 'last_clicked_at', type: 'DATETIME' },
            { name: 'ip_address', type: 'TEXT' },
            { name: 'user_agent', type: 'TEXT' },
            { name: 'tracking_id', type: 'TEXT UNIQUE' },
            { name: 'pixel_tracking_id', type: 'TEXT' },
            { name: 'link_tracking_data', type: 'TEXT' },
            { name: 'message_code', type: 'TEXT' },
            { name: 'email_message_id', type: 'TEXT' },
            { name: 'delivered_at', type: 'DATETIME' },
            { name: 'tracking_id', type: 'TEXT' },
            { name: 'unsubscribe_token', type: 'TEXT' }
          ];

          interactionColumns.forEach(column => {
            db.run(`ALTER TABLE interactions ADD COLUMN ${column.name} ${column.type}`, (err) => {
              if (err && !err.message.includes('duplicate column name')) {
                console.error(`Error adding column ${column.name} to interactions:`, err);
              } else if (!err) {
                console.log(`Added column ${column.name} to interactions table`);
              }
            });
          });
        }
      });

      // Campaigns table
      db.run(`
        CREATE TABLE IF NOT EXISTS campaigns (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          name TEXT NOT NULL,
          name_ar TEXT,
          name_en TEXT,
          description TEXT,
          message_id INTEGER,
          target_category TEXT,
          target_language TEXT,
          status TEXT DEFAULT 'draft',
          scheduled_at DATETIME,
          completed_at DATETIME,
          total_recipients INTEGER DEFAULT 0,
          sent_count INTEGER DEFAULT 0,
          opened_count INTEGER DEFAULT 0,
          clicked_count INTEGER DEFAULT 0,
          bounced_count INTEGER DEFAULT 0,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (message_id) REFERENCES messages (id) ON DELETE SET NULL
        )
      `, (err) => {
        if (err) {
          console.error('Error creating campaigns table:', err);
          reject(err);
        }
      });

      // Email campaigns table
      db.run(`
        CREATE TABLE IF NOT EXISTS email_campaigns (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          name TEXT NOT NULL,
          subject TEXT NOT NULL,
          content TEXT NOT NULL,
          status TEXT DEFAULT 'draft',
          sent_at DATETIME,
          total_recipients INTEGER DEFAULT 0,
          total_sent INTEGER DEFAULT 0,
          total_delivered INTEGER DEFAULT 0,
          total_opened INTEGER DEFAULT 0,
          total_clicked INTEGER DEFAULT 0,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
      `, (err) => {
        if (err) {
          console.error('Error creating email_campaigns table:', err);
          reject(err);
        }
      });

      // Email logs table
      db.run(`
        CREATE TABLE IF NOT EXISTS email_logs (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          client_id INTEGER NOT NULL,
          campaign_id INTEGER,
          email TEXT NOT NULL,
          subject TEXT,
          status TEXT DEFAULT 'sent',
          sent_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          opened_at DATETIME,
          clicked_at DATETIME,
          open_count INTEGER DEFAULT 0,
          click_count INTEGER DEFAULT 0,
          message_id TEXT,
          error_message TEXT,
          FOREIGN KEY (client_id) REFERENCES clients (id),
          FOREIGN KEY (campaign_id) REFERENCES email_campaigns (id)
        )
      `, (err) => {
        if (err) {
          console.error('Error creating email_logs table:', err);
          reject(err);
        }
      });

      // Client activities table
      db.run(`
        CREATE TABLE IF NOT EXISTS client_activities (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          client_id INTEGER NOT NULL,
          activity_type TEXT NOT NULL,
          activity_data TEXT,
          ip_address TEXT,
          user_agent TEXT,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (client_id) REFERENCES clients (id)
        )
      `, (err) => {
        if (err) {
          console.error('Error creating client_activities table:', err);
          reject(err);
        }
      });

      // Email config table
      db.run(`
        CREATE TABLE IF NOT EXISTS email_config (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          smtp_host TEXT NOT NULL,
          smtp_port INTEGER NOT NULL,
          smtp_username TEXT NOT NULL,
          smtp_password TEXT NOT NULL,
          sender_name TEXT,
          sender_email TEXT,
          is_active INTEGER DEFAULT 1,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
      `, (err) => {
        if (err) {
          console.error('Error creating email_config table:', err);
          reject(err);
        } else {
          console.log('Email config table created successfully');
        }
      });

      // Email templates table
      db.run(`
        CREATE TABLE IF NOT EXISTS email_templates (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          name TEXT NOT NULL,
          name_ar TEXT,
          description TEXT,
          description_ar TEXT,
          html_content TEXT NOT NULL,
          variables TEXT, -- JSON string of available variables
          category TEXT DEFAULT 'general',
          is_active BOOLEAN DEFAULT 1,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
      `, (err) => {
        if (err) {
          console.error('Error creating email_templates table:', err);
          reject(err);
        } else {
          console.log('Email templates table created successfully - no default template inserted');
        }
      });

      // Client categories table
      db.run(`
        CREATE TABLE IF NOT EXISTS client_categories (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          name TEXT UNIQUE NOT NULL,
          name_ar TEXT,
          description TEXT,
          description_ar TEXT,
          color TEXT DEFAULT '#6b7280',
          icon TEXT DEFAULT 'users',
          is_active BOOLEAN DEFAULT 1,
          sort_order INTEGER DEFAULT 0,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
      `, (err) => {
        if (err) {
          console.error('Error creating client_categories table:', err);
          reject(err);
        } else {
          // Insert default categories
          const defaultCategories = [
            { name: 'General', name_ar: 'عام', description: 'General clients', description_ar: 'عملاء عاديون', color: '#6b7280', icon: 'users', sort_order: 1 },
            { name: 'Premium', name_ar: 'مميز', description: 'Premium clients', description_ar: 'عملاء مميزون', color: '#3b82f6', icon: 'star', sort_order: 2 },
            { name: 'VIP', name_ar: 'كبار الشخصيات', description: 'VIP clients', description_ar: 'عملاء كبار الشخصيات', color: '#8b5cf6', icon: 'crown', sort_order: 3 },
            { name: 'Corporate', name_ar: 'شركات', description: 'Corporate clients', description_ar: 'عملاء الشركات', color: '#059669', icon: 'building', sort_order: 4 },
            { name: 'Individual', name_ar: 'أفراد', description: 'Individual clients', description_ar: 'عملاء أفراد', color: '#dc2626', icon: 'user', sort_order: 5 }
          ];

          defaultCategories.forEach(category => {
            db.run(
              'INSERT OR IGNORE INTO client_categories (name, name_ar, description, description_ar, color, icon, sort_order) VALUES (?, ?, ?, ?, ?, ?, ?)',
              [category.name, category.name_ar, category.description, category.description_ar, category.color, category.icon, category.sort_order],
              (err) => {
                if (err && !err.message.includes('UNIQUE constraint failed')) {
                  console.error('Error inserting default category:', err);
                } else if (!err) {
                  console.log(`Inserted default category: ${category.name}`);
                }
              }
            );
          });
        }
      });

      // Settings table
      db.run(`
        CREATE TABLE IF NOT EXISTS settings (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          key TEXT UNIQUE NOT NULL,
          value TEXT,
          description TEXT,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
      `, (err) => {
        if (err) {
          console.error('Error creating settings table:', err);
          reject(err);
        } else {
          console.log('All tables created successfully');
          console.log('تم إنشاء جميع الجداول بنجاح');
          resolve();
        }
      });
    });
  });
};

// Initialize default admin user
const initializeDefaultUser = () => {
  return new Promise((resolve, reject) => {
    const saltRounds = 12;

    // Check if admin user exists
    db.get('SELECT id FROM users WHERE username = ?', ['admin'], (err, user) => {
      if (err) {
        console.error('Error checking admin user:', err);
        reject(err);
        return;
      }

      if (!user) {
        // Create default admin user
        const hashedPassword = bcrypt.hashSync('admin123', saltRounds);

        db.run(
          'INSERT INTO users (username, email, password, role, first_name, last_name) VALUES (?, ?, ?, ?, ?, ?)',
          ['admin', '<EMAIL>', hashedPassword, 'admin', 'System', 'Administrator'],
          (err) => {
            if (err) {
              console.error('Error creating admin user:', err);
              reject(err);
            } else {
              console.log('Default admin user created');
              console.log('تم إنشاء مستخدم المدير الافتراضي');
              console.log('Username: admin, Password: admin123');
              resolve();
            }
          }
        );
      } else {
        console.log('Admin user already exists');
        console.log('مستخدم المدير موجود بالفعل');
        resolve();
      }
    });
  });
};

// Initialize default settings
const initializeSettings = () => {
  return new Promise((resolve, reject) => {
    const defaultSettings = [
      { key: 'default_language', value: 'ar', description: 'Default system language' },
      { key: 'smtp_configured', value: 'false', description: 'SMTP configuration status' },
      { key: 'max_recipients_per_campaign', value: '1000', description: 'Maximum recipients per campaign' },
      { key: 'email_rate_limit', value: '100', description: 'Emails per hour limit' }
    ];

    let completed = 0;
    defaultSettings.forEach(setting => {
      db.run(
        'INSERT OR IGNORE INTO settings (key, value, description) VALUES (?, ?, ?)',
        [setting.key, setting.value, setting.description],
        (err) => {
          if (err) {
            console.error('Error inserting setting:', err);
          }
          completed++;
          if (completed === defaultSettings.length) {
            console.log('Default settings initialized');
            console.log('تم تهيئة الإعدادات الافتراضية');
            resolve();
          }
        }
      );
    });
  });
};

// Update old interaction records
const updateOldInteractionRecords = () => {
  return new Promise((resolve) => {
    console.log('🔄 Updating old interaction records...');
    db.run(`
      UPDATE interactions
      SET type = 'email_sent'
      WHERE type = 'email' AND (status = 'sent' OR status = 'clicked' OR status = 'opened')
    `, (err) => {
      if (err) {
        console.error('Error updating old interaction records:', err);
      } else {
        console.log('✅ Old interaction records updated successfully');
      }
      resolve();
    });
  });
};

// Initialize database
const initializeDatabase = async () => {
  try {
    await createTables();
    await initializeDefaultUser();
    await initializeSettings();
    await updateOldInteractionRecords();
    console.log('Database initialization completed');
    console.log('تم إكمال تهيئة قاعدة البيانات');
  } catch (error) {
    console.error('Database initialization failed:', error);
    console.error('فشل في تهيئة قاعدة البيانات:', error);
    throw error;
  }
};

module.exports = {
  db,
  initializeDatabase
};
