import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { toast } from 'react-hot-toast';
import { useForm } from 'react-hook-form';
import { FiPlus, FiEdit2, FiTrash2, <PERSON><PERSON>ye, <PERSON><PERSON>yeOff, FiUsers, FiShield } from 'react-icons/fi';
import LoadingSpinner from '../../components/UI/LoadingSpinner';
import Modal from '../../components/UI/Modal';
import { apiHelpers, endpoints } from '../../services/api';

const UserManagement = () => {
  const { t, i18n } = useTranslation();
  const isRTL = i18n.language === 'ar';

  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(false);
  const [showModal, setShowModal] = useState(false);
  const [editingUser, setEditingUser] = useState(null);
  const [showPassword, setShowPassword] = useState(false);
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 10,
    total: 0,
    pages: 0
  });

  const { register, handleSubmit, reset, formState: { errors } } = useForm();

  // User roles and permissions
  const roles = [
    { value: 'admin', label: isRTL ? 'مدير' : 'Admin', permissions: ['all'] },
    { value: 'manager', label: isRTL ? 'مدير تنفيذي' : 'Manager', permissions: ['users', 'clients', 'messages', 'reports'] },
    { value: 'editor', label: isRTL ? 'محرر' : 'Editor', permissions: ['clients', 'messages'] },
    { value: 'viewer', label: isRTL ? 'مشاهد' : 'Viewer', permissions: ['reports'] }
  ];

  // Load users
  const loadUsers = async () => {
    try {
      setLoading(true);
      const result = await apiHelpers.get(`${endpoints.users.list}?page=${pagination.page}&limit=${pagination.limit}`);
      
      if (result.success) {
        // Handle different response structures
        let usersData = [];
        if (result.data?.users && Array.isArray(result.data.users)) {
          usersData = result.data.users;
        } else if (result.data?.data && Array.isArray(result.data.data)) {
          usersData = result.data.data;
        } else if (Array.isArray(result.data)) {
          usersData = result.data;
        }

        console.log('Users data loaded:', usersData);
        setUsers(usersData);
        setPagination(prev => ({
          ...prev,
          total: result.data.total || result.data.length,
          pages: result.data.pages || Math.ceil((result.data.total || result.data.length) / pagination.limit)
        }));
      }
    } catch (error) {
      console.error('Error loading users:', error);
      toast.error(t('users.error_loading'));
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadUsers();
  }, [pagination.page]);

  // Handle form submission
  const onSubmit = async (data) => {
    try {
      setLoading(true);
      
      const userData = {
        ...data,
        permissions: roles.find(r => r.value === data.role)?.permissions || []
      };

      let result;
      if (editingUser) {
        result = await apiHelpers.put(`${endpoints.users.update(editingUser.id)}`, userData);
      } else {
        result = await apiHelpers.post(endpoints.users.create, userData);
      }

      if (result.success) {
        toast.success(editingUser ? t('users.updated_successfully') : t('users.created_successfully'));
        setShowModal(false);
        setEditingUser(null);
        reset();
        loadUsers();
      } else {
        toast.error(result.error || t('common.error_occurred'));
      }
    } catch (error) {
      console.error('Error saving user:', error);
      toast.error(t('common.error_occurred'));
    } finally {
      setLoading(false);
    }
  };

  // Handle delete user
  const handleDelete = async (userId) => {
    if (!window.confirm(t('users.confirm_delete'))) return;

    try {
      setLoading(true);
      const result = await apiHelpers.delete(endpoints.users.delete(userId));
      
      if (result.success) {
        toast.success(t('users.deleted_successfully'));
        loadUsers();
      } else {
        toast.error(result.error || t('common.error_occurred'));
      }
    } catch (error) {
      console.error('Error deleting user:', error);
      toast.error(t('common.error_occurred'));
    } finally {
      setLoading(false);
    }
  };

  // Handle edit user
  const handleEdit = (user) => {
    setEditingUser(user);
    reset({
      username: user.username,
      email: user.email,
      name: user.name,
      phone: user.phone,
      role: user.role
    });
    setShowModal(true);
  };

  // Handle add new user
  const handleAdd = () => {
    setEditingUser(null);
    reset();
    setShowModal(true);
  };

  if (loading && users.length === 0) {
    return <LoadingSpinner size="large" text={t('common.loading')} />;
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3 rtl:space-x-reverse">
          <FiUsers className="h-8 w-8 text-blue-600" />
          <div>
            <h1 className="text-3xl font-bold text-gray-900">
              {t('users.title')}
            </h1>
            <p className="mt-2 text-gray-600">
              {t('users.subtitle')}
            </p>
          </div>
        </div>
        
        <button
          onClick={handleAdd}
          className="btn btn-primary flex items-center space-x-2 rtl:space-x-reverse"
        >
          <FiPlus className="h-4 w-4" />
          <span>{t('users.add_user')}</span>
        </button>
      </div>

      {/* Users Table */}
      <div className="card">
        <div className="card-header">
          <h3 className="text-lg font-semibold text-gray-900 flex items-center space-x-2 rtl:space-x-reverse">
            <FiShield className="h-5 w-5" />
            <span>{t('users.user_list')}</span>
          </h3>
        </div>
        
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  {t('users.name')}
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  {t('users.email')}
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  {t('users.role')}
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  {t('users.created_at')}
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  {t('common.actions')}
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {Array.isArray(users) && users.length > 0 ? users.map((user) => (
                <tr key={user.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div>
                      <div className="text-sm font-medium text-gray-900">{user.name}</div>
                      <div className="text-sm text-gray-500">@{user.username}</div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {user.email}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                      user.role === 'admin' ? 'bg-red-100 text-red-800' :
                      user.role === 'manager' ? 'bg-blue-100 text-blue-800' :
                      user.role === 'editor' ? 'bg-green-100 text-green-800' :
                      'bg-gray-100 text-gray-800'
                    }`}>
                      {roles.find(r => r.value === user.role)?.label || user.role}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {new Date(user.created_at).toLocaleDateString(isRTL ? 'ar-SA' : 'en-US')}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2 rtl:space-x-reverse">
                    <button
                      onClick={() => handleEdit(user)}
                      className="text-blue-600 hover:text-blue-900"
                    >
                      <FiEdit2 className="h-4 w-4" />
                    </button>
                    <button
                      onClick={() => handleDelete(user.id)}
                      className="text-red-600 hover:text-red-900"
                    >
                      <FiTrash2 className="h-4 w-4" />
                    </button>
                  </td>
                </tr>
              )) : (
                <tr>
                  <td colSpan="5" className="px-6 py-4 text-center text-gray-500">
                    {loading ? t('common.loading') : t('users.no_users_found')}
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>

        {/* Pagination */}
        {pagination.pages > 1 && (
          <div className="card-footer">
            <div className="flex items-center justify-between">
              <div className="text-sm text-gray-700">
                {t('common.showing')} {((pagination.page - 1) * pagination.limit) + 1} {t('common.to')} {Math.min(pagination.page * pagination.limit, pagination.total)} {t('common.of')} {pagination.total} {t('common.results')}
              </div>
              <div className="flex space-x-2 rtl:space-x-reverse">
                <button
                  onClick={() => setPagination(prev => ({ ...prev, page: prev.page - 1 }))}
                  disabled={pagination.page === 1}
                  className="btn btn-secondary btn-sm"
                >
                  {t('common.previous')}
                </button>
                <button
                  onClick={() => setPagination(prev => ({ ...prev, page: prev.page + 1 }))}
                  disabled={pagination.page === pagination.pages}
                  className="btn btn-secondary btn-sm"
                >
                  {t('common.next')}
                </button>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Add/Edit User Modal */}
      <Modal
        isOpen={showModal}
        onClose={() => {
          setShowModal(false);
          setEditingUser(null);
          reset();
        }}
        title={editingUser ? t('users.edit_user') : t('users.add_user')}
      >
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                {t('users.name')} *
              </label>
              <input
                type="text"
                {...register('name', { required: t('validation.required') })}
                className="input"
                placeholder={t('users.name_placeholder')}
              />
              {errors.name && <p className="text-red-500 text-sm mt-1">{errors.name.message}</p>}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                {t('users.username')} *
              </label>
              <input
                type="text"
                {...register('username', { required: t('validation.required') })}
                className="input"
                placeholder={t('users.username_placeholder')}
              />
              {errors.username && <p className="text-red-500 text-sm mt-1">{errors.username.message}</p>}
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              {t('users.email')} *
            </label>
            <input
              type="email"
              {...register('email', { 
                required: t('validation.required'),
                pattern: {
                  value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                  message: t('validation.invalid_email')
                }
              })}
              className="input"
              placeholder={t('users.email_placeholder')}
            />
            {errors.email && <p className="text-red-500 text-sm mt-1">{errors.email.message}</p>}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              {t('users.phone')}
            </label>
            <input
              type="tel"
              {...register('phone')}
              className="input"
              placeholder={t('users.phone_placeholder')}
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              {t('users.role')} *
            </label>
            <select
              {...register('role', { required: t('validation.required') })}
              className="input"
            >
              <option value="">{t('users.select_role')}</option>
              {roles.map(role => (
                <option key={role.value} value={role.value}>
                  {role.label}
                </option>
              ))}
            </select>
            {errors.role && <p className="text-red-500 text-sm mt-1">{errors.role.message}</p>}
          </div>

          {!editingUser && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                {t('users.password')} *
              </label>
              <div className="relative">
                <input
                  type={showPassword ? 'text' : 'password'}
                  {...register('password', { 
                    required: !editingUser ? t('validation.required') : false,
                    minLength: {
                      value: 6,
                      message: t('validation.min_length', { min: 6 })
                    }
                  })}
                  className="input pr-10"
                  placeholder={t('users.password_placeholder')}
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute inset-y-0 right-0 pr-3 flex items-center"
                >
                  {showPassword ? <FiEyeOff className="h-4 w-4" /> : <FiEye className="h-4 w-4" />}
                </button>
              </div>
              {errors.password && <p className="text-red-500 text-sm mt-1">{errors.password.message}</p>}
            </div>
          )}

          <div className="flex justify-end space-x-3 rtl:space-x-reverse pt-4">
            <button
              type="button"
              onClick={() => {
                setShowModal(false);
                setEditingUser(null);
                reset();
              }}
              className="btn btn-secondary"
            >
              {t('common.cancel')}
            </button>
            <button
              type="submit"
              disabled={loading}
              className="btn btn-primary"
            >
              {loading ? t('common.saving') : (editingUser ? t('common.update') : t('common.create'))}
            </button>
          </div>
        </form>
      </Modal>
    </div>
  );
};

export default UserManagement;
