const XLSX = require('xlsx');

class ExcelParser {
  // Parse Excel or CSV file and extract client data
  static parseClientsFile(buffer, filename = '') {
    try {
      console.log('📊 Parsing file:', filename);

      let jsonData;

      // Check if it's a CSV file
      if (filename.toLowerCase().endsWith('.csv')) {
        console.log('📄 Processing as CSV file');
        const csvText = buffer.toString('utf8');
        jsonData = this.parseCSV(csvText);
      } else {
        console.log('📊 Processing as Excel file');
        const workbook = XLSX.read(buffer, { type: 'buffer' });
        const sheetName = workbook.SheetNames[0];
        const worksheet = workbook.Sheets[sheetName];

        // Convert to JSON
        jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
      }

      if (jsonData.length < 2) {
        throw new Error('File must contain at least a header row and one data row');
      }

      const headers = jsonData[0];
      const dataRows = jsonData.slice(1);

      // Map headers to expected fields (support both Arabic and English)
      const headerMapping = this.createHeaderMapping(headers);
      
      const clients = [];
      const errors = [];

      dataRows.forEach((row, index) => {
        try {
          const client = this.parseClientRow(row, headerMapping, headers);
          if (client.email) { // Email is required
            clients.push(client);
          } else {
            errors.push({
              row: index + 2, // +2 because we start from row 1 and skip header
              error: 'Email is required',
              error_ar: 'البريد الإلكتروني مطلوب'
            });
          }
        } catch (error) {
          errors.push({
            row: index + 2,
            error: error.message,
            error_ar: 'خطأ في تحليل البيانات'
          });
        }
      });

      return {
        clients,
        errors,
        total: dataRows.length,
        valid: clients.length
      };

    } catch (error) {
      throw new Error(`Failed to parse Excel file: ${error.message}`);
    }
  }

  // Create mapping between Excel headers and database fields
  static createHeaderMapping(headers) {
    const mapping = {};
    
    headers.forEach((header, index) => {
      const normalizedHeader = header.toString().toLowerCase().trim();
      
      // Name mappings
      if (normalizedHeader.includes('name') || normalizedHeader.includes('اسم') || normalizedHeader.includes('الاسم')) {
        mapping.name = index;
      }
      // Email mappings
      else if (normalizedHeader.includes('email') || normalizedHeader.includes('بريد') || normalizedHeader.includes('إيميل')) {
        mapping.email = index;
      }
      // Phone mappings
      else if (normalizedHeader.includes('phone') || normalizedHeader.includes('هاتف') || normalizedHeader.includes('جوال') || normalizedHeader.includes('موبايل')) {
        mapping.phone = index;
      }
      // Company mappings
      else if (normalizedHeader.includes('company') || normalizedHeader.includes('شركة') || normalizedHeader.includes('مؤسسة')) {
        mapping.company = index;
      }
      // Category mappings
      else if (normalizedHeader.includes('category') || normalizedHeader.includes('فئة') || normalizedHeader.includes('تصنيف')) {
        mapping.category = index;
      }
      // Language mappings
      else if (normalizedHeader.includes('language') || normalizedHeader.includes('لغة')) {
        mapping.language = index;
      }
      // Status mappings
      else if (normalizedHeader.includes('status') || normalizedHeader.includes('حالة')) {
        mapping.status = index;
      }
      // Tags mappings
      else if (normalizedHeader.includes('tags') || normalizedHeader.includes('علامات') || normalizedHeader.includes('تاغ')) {
        mapping.tags = index;
      }
      // Notes mappings
      else if (normalizedHeader.includes('notes') || normalizedHeader.includes('ملاحظات') || normalizedHeader.includes('تعليق')) {
        mapping.notes = index;
      }
    });

    return mapping;
  }

  // Parse individual client row
  static parseClientRow(row, mapping, headers) {
    const client = {};

    // Extract data based on mapping
    if (mapping.name !== undefined) {
      client.name = this.sanitizeString(row[mapping.name]);
    }
    
    if (mapping.email !== undefined) {
      client.email = this.sanitizeEmail(row[mapping.email]);
    }
    
    if (mapping.phone !== undefined) {
      client.phone = this.sanitizeString(row[mapping.phone]);
    }
    
    if (mapping.company !== undefined) {
      client.company = this.sanitizeString(row[mapping.company]);
    }
    
    if (mapping.category !== undefined) {
      client.category = this.sanitizeString(row[mapping.category]) || 'general';
    } else {
      client.category = 'general';
    }
    
    if (mapping.language !== undefined) {
      client.language = this.sanitizeLanguage(row[mapping.language]);
    } else {
      client.language = 'ar';
    }
    
    if (mapping.status !== undefined) {
      client.status = this.sanitizeStatus(row[mapping.status]);
    } else {
      client.status = 'active';
    }
    
    if (mapping.tags !== undefined) {
      client.tags = this.sanitizeString(row[mapping.tags]);
    }
    
    if (mapping.notes !== undefined) {
      client.notes = this.sanitizeString(row[mapping.notes]);
    }

    return client;
  }

  // Sanitize string values
  static sanitizeString(value) {
    if (value === null || value === undefined) return null;
    return value.toString().trim() || null;
  }

  // Sanitize and validate email
  static sanitizeEmail(value) {
    if (!value) return null;
    
    const email = value.toString().trim().toLowerCase();
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    
    if (emailRegex.test(email)) {
      return email;
    }
    
    return null;
  }

  // Sanitize language value
  static sanitizeLanguage(value) {
    if (!value) return 'ar';
    
    const lang = value.toString().toLowerCase().trim();
    const validLanguages = ['ar', 'en', 'arabic', 'english', 'عربي', 'انجليزي'];
    
    if (lang.includes('ar') || lang.includes('arabic') || lang.includes('عربي')) {
      return 'ar';
    } else if (lang.includes('en') || lang.includes('english') || lang.includes('انجليزي')) {
      return 'en';
    }
    
    return 'ar'; // default
  }

  // Sanitize status value
  static sanitizeStatus(value) {
    if (!value) return 'active';
    
    const status = value.toString().toLowerCase().trim();
    const validStatuses = ['active', 'inactive', 'blocked', 'نشط', 'غير نشط', 'محظور'];
    
    if (status.includes('active') || status.includes('نشط')) {
      return 'active';
    } else if (status.includes('inactive') || status.includes('غير نشط')) {
      return 'inactive';
    } else if (status.includes('blocked') || status.includes('محظور')) {
      return 'blocked';
    }
    
    return 'active'; // default
  }

  // Generate Excel template for clients
  static generateTemplate() {
    const headers = [
      'Name / الاسم',
      'Email / البريد الإلكتروني',
      'Phone / الهاتف',
      'Company / الشركة',
      'Category / الفئة',
      'Language / اللغة',
      'Status / الحالة',
      'Tags / العلامات',
      'Notes / الملاحظات'
    ];

    const sampleData = [
      [
        'أحمد محمد',
        '<EMAIL>',
        '+966501234567',
        'شركة التقنية المتقدمة',
        'premium',
        'ar',
        'active',
        'vip,tech',
        'عميل مهم'
      ],
      [
        'John Smith',
        '<EMAIL>',
        '+1234567890',
        'Tech Solutions Inc',
        'general',
        'en',
        'active',
        'new,prospect',
        'Potential client'
      ]
    ];

    const worksheet = XLSX.utils.aoa_to_sheet([headers, ...sampleData]);
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, 'Clients');

    return XLSX.write(workbook, { type: 'buffer', bookType: 'xlsx' });
  }

  // Parse CSV text into array format
  static parseCSV(csvText) {
    console.log('📄 Parsing CSV content');

    const lines = csvText.split('\n').filter(line => line.trim());
    const result = [];

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim();
      if (!line) continue;

      // Simple CSV parsing (handles basic cases)
      const row = [];
      let current = '';
      let inQuotes = false;

      for (let j = 0; j < line.length; j++) {
        const char = line[j];

        if (char === '"') {
          inQuotes = !inQuotes;
        } else if (char === ',' && !inQuotes) {
          row.push(current.trim());
          current = '';
        } else {
          current += char;
        }
      }

      // Add the last field
      row.push(current.trim());
      result.push(row);
    }

    console.log(`📊 Parsed ${result.length} rows from CSV`);
    return result;
  }
}

module.exports = ExcelParser;
