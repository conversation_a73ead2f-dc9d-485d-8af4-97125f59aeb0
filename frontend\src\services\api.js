import axios from 'axios';
import toast from 'react-hot-toast';

// Create axios instance
const api = axios.create({
  baseURL: import.meta.env.VITE_API_URL || '/api',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request tracking for debugging rate limiting issues
const requestTracker = new Map();

// Request interceptor
api.interceptors.request.use(
  (config) => {
    // Add auth token if available
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }

    // Add language header
    const language = localStorage.getItem('i18nextLng') || 'ar';
    config.headers['Accept-Language'] = language;

    // Track requests for debugging rate limiting
    const requestKey = `${config.method?.toUpperCase()} ${config.url}`;
    const now = Date.now();

    if (requestTracker.has(requestKey)) {
      const lastRequest = requestTracker.get(requestKey);
      const timeDiff = now - lastRequest;

      if (timeDiff < 500) { // Less than 500ms
        console.warn(`⚠️  Rapid request detected: ${requestKey} (${timeDiff}ms ago)`);
        console.warn(`⚠️  This might cause rate limiting issues`);
      }
    }

    requestTracker.set(requestKey, now);

    // Log request in development
    if (import.meta.env.DEV) {
      console.log(`🚀 ${config.method?.toUpperCase()} ${config.url}`, config.data);
    }

    return config;
  },
  (error) => {
    console.error('Request error:', error);
    return Promise.reject(error);
  }
);

// Response interceptor
api.interceptors.response.use(
  (response) => {
    // Log response in development
    if (import.meta.env.DEV) {
      console.log(`✅ ${response.config.method?.toUpperCase()} ${response.config.url}`, response.data);
    }

    return response;
  },
  (error) => {
    // Log error in development
    if (import.meta.env.DEV) {
      console.error(`❌ ${error.config?.method?.toUpperCase()} ${error.config?.url}`, error.response?.data || error.message);
    }

    // Handle different error types
    if (error.response) {
      // Server responded with error status
      const { status, data } = error.response;

      switch (status) {
        case 401:
          // Unauthorized - redirect to login
          localStorage.removeItem('token');
          delete api.defaults.headers.common['Authorization'];
          
          if (window.location.pathname !== '/login') {
            toast.error(data.message_ar || data.error || 'Session expired');
            window.location.href = '/login';
          }
          break;

        case 403:
          // Forbidden
          toast.error(data.message_ar || data.error || 'Access denied');
          break;

        case 404:
          // Not found
          toast.error(data.message_ar || data.error || 'Resource not found');
          break;

        case 422:
          // Validation error
          if (data.errors) {
            // Handle validation errors
            Object.values(data.errors).forEach(errorArray => {
              if (Array.isArray(errorArray)) {
                errorArray.forEach(error => toast.error(error));
              } else {
                toast.error(errorArray);
              }
            });
          } else {
            toast.error(data.message_ar || data.error || 'Validation error');
          }
          break;

        case 429:
          // Rate limit exceeded
          toast.error(data.message_ar || data.error || 'Too many requests. Please try again later.');
          break;

        case 500:
          // Server error
          toast.error(data.message_ar || data.error || 'Server error. Please try again later.');
          break;

        default:
          // Other errors
          toast.error(data.message_ar || data.error || 'An error occurred');
      }
    } else if (error.request) {
      // Network error
      toast.error('Network error. Please check your connection.');
    } else {
      // Other error
      toast.error('An unexpected error occurred');
    }

    return Promise.reject(error);
  }
);

// API endpoints
export const endpoints = {
  // Auth
  auth: {
    login: '/auth/login',
    logout: '/auth/logout',
    profile: '/auth/profile',
    verify: '/auth/verify',
    changePassword: '/auth/password',
  },

  // Clients
  clients: {
    list: '/clients',
    create: '/clients',
    get: (id) => `/clients/${id}`,
    update: (id) => `/clients/${id}`,
    delete: (id) => `/clients/${id}`,
    import: '/clients/import',
    template: '/clients/template/download',
    stats: '/clients/stats/overview',
  },

  // Messages
  messages: {
    list: '/messages',
    create: '/messages',
    get: (id) => `/messages/${id}`,
    update: (id) => `/messages/${id}`,
    delete: (id) => `/messages/${id}`,
    send: (id) => `/messages/${id}/send`,
    testEmail: '/messages/test-email',
    stats: '/messages/stats/overview',
  },

  // Interactions
  interactions: {
    list: '/interactions',
    create: '/interactions',
    get: (id) => `/interactions/${id}`,
    update: (id) => `/interactions/${id}`,
    delete: (id) => `/interactions/${id}`,
    byClient: (clientId) => `/interactions/client/${clientId}`,
    byMessage: (messageId) => `/interactions/message/${messageId}`,
    stats: '/interactions/stats/overview',
    timeline: '/interactions/timeline',
  },

  // Reports
  reports: {
    dashboard: '/reports/dashboard',
    emailPerformance: '/reports/email-performance',
    clientEngagement: '/reports/client-engagement',
    timeline: '/reports/analytics/timeline',
    topContent: '/reports/top-content',
    recentActivity: '/reports/recent-activity',
    export: (type) => `/reports/export/${type}`,
  },

  // Settings
  settings: {
    list: '/settings',
    update: '/settings',
    get: (key) => `/settings/${key}`,
    updateKey: (key) => `/settings/${key}`,
    reset: '/settings/reset',
  },
};

// Helper functions for common API operations
export const apiHelpers = {
  // GET request with error handling
  get: async (url, config = {}) => {
    try {
      const response = await api.get(url, config);
      return { success: true, data: response.data };
    } catch (error) {
      return { success: false, error: error.response?.data || error.message };
    }
  },

  // POST request with error handling
  post: async (url, data = {}, config = {}) => {
    try {
      const response = await api.post(url, data, config);
      return { success: true, data: response.data };
    } catch (error) {
      return { success: false, error: error.response?.data || error.message };
    }
  },

  // PUT request with error handling
  put: async (url, data = {}, config = {}) => {
    try {
      const response = await api.put(url, data, config);
      return { success: true, data: response.data };
    } catch (error) {
      return { success: false, error: error.response?.data || error.message };
    }
  },

  // DELETE request with error handling
  delete: async (url, config = {}) => {
    try {
      const response = await api.delete(url, config);
      return { success: true, data: response.data };
    } catch (error) {
      return { success: false, error: error.response?.data || error.message };
    }
  },

  // Upload file with progress
  upload: async (url, formData, onProgress = null) => {
    try {
      const config = {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
        onUploadProgress: (progressEvent) => {
          if (onProgress) {
            const percentCompleted = Math.round(
              (progressEvent.loaded * 100) / progressEvent.total
            );
            onProgress(percentCompleted);
          }
        },
      };

      const response = await api.post(url, formData, config);
      return { success: true, data: response.data };
    } catch (error) {
      return { success: false, error: error.response?.data || error.message };
    }
  },

  // Download file
  download: async (url, filename = null) => {
    try {
      const response = await api.get(url, {
        responseType: 'blob',
      });

      // Create blob link to download
      const blob = new Blob([response.data]);
      const link = document.createElement('a');
      link.href = window.URL.createObjectURL(blob);
      
      // Set filename from response headers or parameter
      const contentDisposition = response.headers['content-disposition'];
      if (contentDisposition && !filename) {
        const filenameMatch = contentDisposition.match(/filename="(.+)"/);
        if (filenameMatch) {
          filename = filenameMatch[1];
        }
      }
      
      link.download = filename || 'download';
      link.click();
      
      // Clean up
      window.URL.revokeObjectURL(link.href);
      
      return { success: true };
    } catch (error) {
      return { success: false, error: error.response?.data || error.message };
    }
  },
};

export default api;
