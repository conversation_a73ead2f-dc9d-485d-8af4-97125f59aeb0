const express = require('express');
const router = express.Router();
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const { db } = require('../config/database');

// Simple user management (for demo purposes)
// In production, you would want a proper user management system

// Create users table if it doesn't exist
db.run(`
  CREATE TABLE IF NOT EXISTS users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    username TEXT UNIQUE NOT NULL,
    email TEXT UNIQUE NOT NULL,
    password TEXT NOT NULL,
    role TEXT DEFAULT 'user',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
  )
`);

// Create default admin user if no users exist
db.get('SELECT COUNT(*) as count FROM users', (err, row) => {
  if (!err && row.count === 0) {
    const defaultPassword = bcrypt.hashSync('admin123', 10);
    db.run(
      'INSERT INTO users (username, email, password, role) VALUES (?, ?, ?, ?)',
      ['admin', '<EMAIL>', defaultPassword, 'admin'],
      (err) => {
        if (!err) {
          console.log('Default admin user created: admin / admin123');
          console.log('تم إنشاء مستخدم المدير الافتراضي: admin / admin123');
        }
      }
    );
  }
});

// Middleware to verify JWT token
const authenticateToken = (req, res, next) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    return res.status(401).json({
      success: false,
      error: 'Access token required',
      message_ar: 'رمز الوصول مطلوب'
    });
  }

  jwt.verify(token, process.env.JWT_SECRET || 'default_secret', (err, user) => {
    if (err) {
      return res.status(403).json({
        success: false,
        error: 'Invalid or expired token',
        message_ar: 'رمز غير صحيح أو منتهي الصلاحية'
      });
    }
    req.user = user;
    next();
  });
};

// Login
router.post('/login', async (req, res) => {
  try {
    const { username, password } = req.body;

    if (!username || !password) {
      return res.status(400).json({
        success: false,
        error: 'Username and password are required',
        message_ar: 'اسم المستخدم وكلمة المرور مطلوبان'
      });
    }

    // Find user
    db.get(
      'SELECT * FROM users WHERE username = ? OR email = ?',
      [username, username],
      (err, user) => {
        if (err) {
          console.error('Database error:', err);
          return res.status(500).json({
            success: false,
            error: 'Database error',
            message_ar: 'خطأ في قاعدة البيانات'
          });
        }

        if (!user) {
          return res.status(401).json({
            success: false,
            error: 'Invalid credentials',
            message_ar: 'بيانات الدخول غير صحيحة'
          });
        }

        // Verify password
        if (!bcrypt.compareSync(password, user.password)) {
          return res.status(401).json({
            success: false,
            error: 'Invalid credentials',
            message_ar: 'بيانات الدخول غير صحيحة'
          });
        }

        // Generate JWT token
        const token = jwt.sign(
          {
            id: user.id,
            username: user.username,
            email: user.email,
            role: user.role
          },
          process.env.JWT_SECRET || 'default_secret',
          { expiresIn: process.env.JWT_EXPIRES_IN || '7d' }
        );

        res.json({
          success: true,
          data: {
            token,
            user: {
              id: user.id,
              username: user.username,
              email: user.email,
              role: user.role
            }
          },
          message: 'Login successful',
          message_ar: 'تم تسجيل الدخول بنجاح'
        });
      }
    );
  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json({
      success: false,
      error: 'Login failed',
      message_ar: 'فشل في تسجيل الدخول'
    });
  }
});

// Register (optional - for creating new users)
router.post('/register', async (req, res) => {
  try {
    const { username, email, password, role = 'user' } = req.body;

    if (!username || !email || !password) {
      return res.status(400).json({
        success: false,
        error: 'Username, email, and password are required',
        message_ar: 'اسم المستخدم والبريد الإلكتروني وكلمة المرور مطلوبة'
      });
    }

    // Check if user already exists
    db.get(
      'SELECT id FROM users WHERE username = ? OR email = ?',
      [username, email],
      (err, existingUser) => {
        if (err) {
          console.error('Database error:', err);
          return res.status(500).json({
            success: false,
            error: 'Database error',
            message_ar: 'خطأ في قاعدة البيانات'
          });
        }

        if (existingUser) {
          return res.status(400).json({
            success: false,
            error: 'Username or email already exists',
            message_ar: 'اسم المستخدم أو البريد الإلكتروني موجود مسبقاً'
          });
        }

        // Hash password
        const hashedPassword = bcrypt.hashSync(password, 10);

        // Create user
        db.run(
          'INSERT INTO users (username, email, password, role) VALUES (?, ?, ?, ?)',
          [username, email, hashedPassword, role],
          function(err) {
            if (err) {
              console.error('Error creating user:', err);
              return res.status(500).json({
                success: false,
                error: 'Failed to create user',
                message_ar: 'فشل في إنشاء المستخدم'
              });
            }

            res.status(201).json({
              success: true,
              data: {
                id: this.lastID,
                username,
                email,
                role
              },
              message: 'User created successfully',
              message_ar: 'تم إنشاء المستخدم بنجاح'
            });
          }
        );
      }
    );
  } catch (error) {
    console.error('Registration error:', error);
    res.status(500).json({
      success: false,
      error: 'Registration failed',
      message_ar: 'فشل في التسجيل'
    });
  }
});

// Get current user profile
router.get('/profile', authenticateToken, (req, res) => {
  res.json({
    success: true,
    data: {
      id: req.user.id,
      username: req.user.username,
      email: req.user.email,
      role: req.user.role
    },
    message: 'Profile retrieved successfully',
    message_ar: 'تم استرجاع الملف الشخصي بنجاح'
  });
});

// Update profile
router.put('/profile', authenticateToken, async (req, res) => {
  try {
    const { username, email, name, phone } = req.body;
    const userId = req.user.id;

    // Validate input
    if (!username || !email || !name) {
      return res.status(400).json({
        success: false,
        error: 'Username, email, and name are required',
        message_ar: 'اسم المستخدم والبريد الإلكتروني والاسم مطلوبة'
      });
    }

    // Check if username or email already exists (excluding current user)
    const existingUser = await new Promise((resolve, reject) => {
      db.get(
        'SELECT id FROM users WHERE (username = ? OR email = ?) AND id != ?',
        [username, email, userId],
        (err, row) => {
          if (err) reject(err);
          else resolve(row);
        }
      );
    });

    if (existingUser) {
      return res.status(400).json({
        success: false,
        error: 'Username or email already exists',
        message_ar: 'اسم المستخدم أو البريد الإلكتروني موجود بالفعل'
      });
    }

    // Update user profile
    await new Promise((resolve, reject) => {
      db.run(
        `UPDATE users SET
          username = ?,
          email = ?,
          name = ?,
          phone = ?,
          updated_at = CURRENT_TIMESTAMP
         WHERE id = ?`,
        [username, email, name, phone || null, userId],
        function(err) {
          if (err) reject(err);
          else resolve();
        }
      );
    });

    // Get updated user data
    const updatedUser = await new Promise((resolve, reject) => {
      db.get(
        'SELECT id, username, email, name, phone, role, created_at FROM users WHERE id = ?',
        [userId],
        (err, row) => {
          if (err) reject(err);
          else resolve(row);
        }
      );
    });

    res.json({
      success: true,
      message: 'Profile updated successfully',
      message_ar: 'تم تحديث الملف الشخصي بنجاح',
      user: updatedUser
    });

  } catch (error) {
    console.error('Error updating profile:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to update profile',
      message_ar: 'فشل في تحديث الملف الشخصي'
    });
  }
});

// Update password
router.put('/password', authenticateToken, async (req, res) => {
  try {
    const { currentPassword, newPassword, current_password, new_password } = req.body;

    // Support both naming conventions
    const currentPwd = currentPassword || current_password;
    const newPwd = newPassword || new_password;

    if (!currentPwd || !newPwd) {
      return res.status(400).json({
        success: false,
        error: 'Current password and new password are required',
        message_ar: 'كلمة المرور الحالية والجديدة مطلوبتان'
      });
    }

    // Get current user
    db.get('SELECT password FROM users WHERE id = ?', [req.user.id], (err, user) => {
      if (err || !user) {
        return res.status(500).json({
          success: false,
          error: 'User not found',
          message_ar: 'المستخدم غير موجود'
        });
      }

      // Verify current password
      if (!bcrypt.compareSync(currentPwd, user.password)) {
        return res.status(400).json({
          success: false,
          error: 'Current password is incorrect',
          message_ar: 'كلمة المرور الحالية غير صحيحة'
        });
      }

      // Hash new password
      const hashedNewPassword = bcrypt.hashSync(newPwd, 10);

      // Update password
      db.run(
        'UPDATE users SET password = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
        [hashedNewPassword, req.user.id],
        (err) => {
          if (err) {
            console.error('Error updating password:', err);
            return res.status(500).json({
              success: false,
              error: 'Failed to update password',
              message_ar: 'فشل في تحديث كلمة المرور'
            });
          }

          res.json({
            success: true,
            message: 'Password updated successfully',
            message_ar: 'تم تحديث كلمة المرور بنجاح'
          });
        }
      );
    });
  } catch (error) {
    console.error('Password update error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to update password',
      message_ar: 'فشل في تحديث كلمة المرور'
    });
  }
});

// Get user statistics
router.get('/stats', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.id;

    // Get client count for this user
    const clientCount = await new Promise((resolve, reject) => {
      db.get(
        'SELECT COUNT(*) as count FROM clients WHERE created_by = ?',
        [userId],
        (err, row) => {
          if (err) reject(err);
          else resolve(row.count);
        }
      );
    });

    // Get message count for this user
    const messageCount = await new Promise((resolve, reject) => {
      db.get(
        'SELECT COUNT(*) as count FROM messages WHERE created_by = ?',
        [userId],
        (err, row) => {
          if (err) reject(err);
          else resolve(row.count);
        }
      );
    });

    // Get interaction count for messages created by this user
    const interactionCount = await new Promise((resolve, reject) => {
      db.get(
        `SELECT COUNT(*) as count FROM interactions i
         JOIN messages m ON i.message_id = m.id
         WHERE m.created_by = ?`,
        [userId],
        (err, row) => {
          if (err) reject(err);
          else resolve(row.count);
        }
      );
    });

    res.json({
      success: true,
      data: {
        clients: clientCount,
        messages: messageCount,
        interactions: interactionCount
      },
      message: 'User statistics retrieved successfully',
      message_ar: 'تم استرجاع إحصائيات المستخدم بنجاح'
    });

  } catch (error) {
    console.error('Error getting user stats:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get user statistics',
      message_ar: 'فشل في استرجاع إحصائيات المستخدم'
    });
  }
});

// Verify token (for frontend to check if token is still valid)
router.get('/verify', authenticateToken, (req, res) => {
  res.json({
    success: true,
    data: {
      valid: true,
      user: {
        id: req.user.id,
        username: req.user.username,
        email: req.user.email,
        role: req.user.role
      }
    },
    message: 'Token is valid',
    message_ar: 'الرمز صحيح'
  });
});

// Logout (client-side should remove token)
router.post('/logout', (req, res) => {
  res.json({
    success: true,
    message: 'Logged out successfully',
    message_ar: 'تم تسجيل الخروج بنجاح'
  });
});

module.exports = { router, authenticateToken };
