const { db } = require('../config/database');

class Interaction {
  // Get all interactions with pagination and filtering
  static getAll(page = 1, limit = 10, filters = {}) {
    return new Promise((resolve, reject) => {
      const offset = (page - 1) * limit;
      let query = `
        SELECT i.*, c.name as client_name, c.email as client_email, 
               m.title as message_title, m.language as message_language
        FROM interactions i
        LEFT JOIN clients c ON i.client_id = c.id
        LEFT JOIN messages m ON i.message_id = m.id
        WHERE 1=1
      `;
      let countQuery = 'SELECT COUNT(*) as total FROM interactions i WHERE 1=1';
      const params = [];

      // Apply filters
      if (filters.client_id) {
        query += ' AND i.client_id = ?';
        countQuery += ' AND i.client_id = ?';
        params.push(filters.client_id);
      }

      if (filters.message_id) {
        query += ' AND i.message_id = ?';
        countQuery += ' AND i.message_id = ?';
        params.push(filters.message_id);
      }

      if (filters.type) {
        query += ' AND i.type = ?';
        countQuery += ' AND i.type = ?';
        params.push(filters.type);
      }

      if (filters.status) {
        query += ' AND i.status = ?';
        countQuery += ' AND i.status = ?';
        params.push(filters.status);
      }

      if (filters.date_from) {
        query += ' AND i.created_at >= ?';
        countQuery += ' AND i.created_at >= ?';
        params.push(filters.date_from);
      }

      if (filters.date_to) {
        query += ' AND i.created_at <= ?';
        countQuery += ' AND i.created_at <= ?';
        params.push(filters.date_to);
      }

      query += ' ORDER BY i.created_at DESC LIMIT ? OFFSET ?';
      params.push(limit, offset);

      // Get total count
      db.get(countQuery, params.slice(0, -2), (err, countResult) => {
        if (err) {
          reject(err);
          return;
        }

        // Get interactions
        db.all(query, params, (err, rows) => {
          if (err) {
            reject(err);
          } else {
            resolve({
              interactions: rows,
              total: countResult.total,
              page,
              limit,
              totalPages: Math.ceil(countResult.total / limit)
            });
          }
        });
      });
    });
  }

  // Get interaction by ID
  static getById(id) {
    return new Promise((resolve, reject) => {
      const query = `
        SELECT i.*, c.name as client_name, c.email as client_email, 
               m.title as message_title, m.language as message_language
        FROM interactions i
        LEFT JOIN clients c ON i.client_id = c.id
        LEFT JOIN messages m ON i.message_id = m.id
        WHERE i.id = ?
      `;

      db.get(query, [id], (err, row) => {
        if (err) {
          reject(err);
        } else {
          resolve(row);
        }
      });
    });
  }

  // Create new interaction
  static create(interactionData) {
    return new Promise((resolve, reject) => {
      const {
        client_id, message_id, type, status, sent_at, opened_at, 
        clicked_at, bounced_at, error_message, metadata
      } = interactionData;

      const query = `
        INSERT INTO interactions (
          client_id, message_id, type, status, sent_at, opened_at,
          clicked_at, bounced_at, error_message, metadata
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `;

      db.run(query, [
        client_id, message_id, type, status || 'pending', sent_at, opened_at,
        clicked_at, bounced_at, error_message, metadata
      ], function(err) {
        if (err) {
          reject(err);
        } else {
          resolve({ id: this.lastID, ...interactionData });
        }
      });
    });
  }

  // Update interaction
  static update(id, interactionData) {
    return new Promise((resolve, reject) => {
      const {
        status, sent_at, opened_at, clicked_at, bounced_at, error_message, metadata
      } = interactionData;

      const query = `
        UPDATE interactions 
        SET status = ?, sent_at = ?, opened_at = ?, clicked_at = ?, 
            bounced_at = ?, error_message = ?, metadata = ?
        WHERE id = ?
      `;

      db.run(query, [
        status, sent_at, opened_at, clicked_at, bounced_at, error_message, metadata, id
      ], function(err) {
        if (err) {
          reject(err);
        } else {
          resolve({ id, changes: this.changes });
        }
      });
    });
  }

  // Record email sent
  static recordSent(client_id, message_id, metadata = null) {
    return new Promise((resolve, reject) => {
      const query = `
        INSERT INTO interactions (client_id, message_id, type, status, sent_at, metadata)
        VALUES (?, ?, 'email_sent', 'sent', datetime('now'), ?)
      `;

      db.run(query, [client_id, message_id, metadata], function(err) {
        if (err) {
          reject(err);
        } else {
          resolve({ id: this.lastID, client_id, message_id, type: 'email_sent' });
        }
      });
    });
  }

  // Record email opened
  static recordOpened(client_id, message_id, metadata = null) {
    return new Promise((resolve, reject) => {
      // First check if there's already a sent interaction
      const checkQuery = `
        SELECT id FROM interactions 
        WHERE client_id = ? AND message_id = ? AND type = 'email_sent'
        ORDER BY created_at DESC LIMIT 1
      `;

      db.get(checkQuery, [client_id, message_id], (err, sentInteraction) => {
        if (err) {
          reject(err);
          return;
        }

        if (sentInteraction) {
          // Update the existing interaction
          const updateQuery = `
            UPDATE interactions 
            SET opened_at = datetime('now'), metadata = ?
            WHERE id = ?
          `;
          
          db.run(updateQuery, [metadata, sentInteraction.id], function(err) {
            if (err) {
              reject(err);
            } else {
              resolve({ id: sentInteraction.id, type: 'email_opened' });
            }
          });
        } else {
          // Create new interaction for opened
          const insertQuery = `
            INSERT INTO interactions (client_id, message_id, type, status, opened_at, metadata)
            VALUES (?, ?, 'email_opened', 'opened', datetime('now'), ?)
          `;

          db.run(insertQuery, [client_id, message_id, metadata], function(err) {
            if (err) {
              reject(err);
            } else {
              resolve({ id: this.lastID, client_id, message_id, type: 'email_opened' });
            }
          });
        }
      });
    });
  }

  // Record email clicked
  static recordClicked(client_id, message_id, metadata = null) {
    return new Promise((resolve, reject) => {
      // First check if there's already a sent interaction
      const checkQuery = `
        SELECT id FROM interactions 
        WHERE client_id = ? AND message_id = ? AND type = 'email_sent'
        ORDER BY created_at DESC LIMIT 1
      `;

      db.get(checkQuery, [client_id, message_id], (err, sentInteraction) => {
        if (err) {
          reject(err);
          return;
        }

        if (sentInteraction) {
          // Update the existing interaction
          const updateQuery = `
            UPDATE interactions 
            SET clicked_at = datetime('now'), metadata = ?
            WHERE id = ?
          `;
          
          db.run(updateQuery, [metadata, sentInteraction.id], function(err) {
            if (err) {
              reject(err);
            } else {
              resolve({ id: sentInteraction.id, type: 'email_clicked' });
            }
          });
        } else {
          // Create new interaction for clicked
          const insertQuery = `
            INSERT INTO interactions (client_id, message_id, type, status, clicked_at, metadata)
            VALUES (?, ?, 'email_clicked', 'clicked', datetime('now'), ?)
          `;

          db.run(insertQuery, [client_id, message_id, metadata], function(err) {
            if (err) {
              reject(err);
            } else {
              resolve({ id: this.lastID, client_id, message_id, type: 'email_clicked' });
            }
          });
        }
      });
    });
  }

  // Get interaction statistics
  static getStatistics(filters = {}) {
    return new Promise((resolve, reject) => {
      let whereClause = 'WHERE 1=1';
      const params = [];

      if (filters.date_from) {
        whereClause += ' AND created_at >= ?';
        params.push(filters.date_from);
      }

      if (filters.date_to) {
        whereClause += ' AND created_at <= ?';
        params.push(filters.date_to);
      }

      if (filters.message_id) {
        whereClause += ' AND message_id = ?';
        params.push(filters.message_id);
      }

      if (filters.client_id) {
        whereClause += ' AND client_id = ?';
        params.push(filters.client_id);
      }

      const queries = {
        total: `SELECT COUNT(*) as count FROM interactions ${whereClause}`,
        sent: `SELECT COUNT(*) as count FROM interactions ${whereClause} AND (type = 'email_sent' OR status = 'sent')`,
        opened: `SELECT COUNT(*) as count FROM interactions ${whereClause} AND opened_at IS NOT NULL`,
        clicked: `SELECT COUNT(*) as count FROM interactions ${whereClause} AND clicked_at IS NOT NULL`,
        bounced: `SELECT COUNT(*) as count FROM interactions ${whereClause} AND bounced_at IS NOT NULL`,
        byType: `SELECT type, COUNT(*) as count FROM interactions ${whereClause} GROUP BY type`,
        byStatus: `SELECT status, COUNT(*) as count FROM interactions ${whereClause} GROUP BY status`
      };

      const results = {};
      let completed = 0;
      const totalQueries = Object.keys(queries).length;

      Object.entries(queries).forEach(([key, query]) => {
        if (key === 'byType' || key === 'byStatus') {
          db.all(query, params, (err, rows) => {
            if (err) {
              reject(err);
              return;
            }
            results[key] = rows;
            completed++;
            if (completed === totalQueries) {
              resolve(results);
            }
          });
        } else {
          db.get(query, params, (err, row) => {
            if (err) {
              reject(err);
              return;
            }
            results[key] = row.count;
            completed++;
            if (completed === totalQueries) {
              resolve(results);
            }
          });
        }
      });
    });
  }

  // Delete interaction
  static delete(id) {
    return new Promise((resolve, reject) => {
      db.run('DELETE FROM interactions WHERE id = ?', [id], function(err) {
        if (err) {
          reject(err);
        } else {
          resolve({ id, changes: this.changes });
        }
      });
    });
  }
}

module.exports = Interaction;
