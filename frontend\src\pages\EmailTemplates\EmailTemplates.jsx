import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { 
  Plus, 
  Edit, 
  Trash2, 
  Eye, 
  Save, 
  X, 
  Code,
  Monitor,
  FileText
} from 'lucide-react';
import { toast } from 'react-hot-toast';
import apiHelpers from '../../utils/apiHelpers';
import LoadingSpinner from '../../components/UI/LoadingSpinner';

const EmailTemplates = () => {
  const { t, i18n } = useTranslation();
  const isRTL = i18n.language === 'ar';

  const [templates, setTemplates] = useState([]);
  const [loading, setLoading] = useState(true);
  const [showForm, setShowForm] = useState(false);
  const [editingTemplate, setEditingTemplate] = useState(null);
  const [showPreview, setShowPreview] = useState(false);
  const [previewHtml, setPreviewHtml] = useState('');
  const [formData, setFormData] = useState({
    name: '',
    name_ar: '',
    description: '',
    description_ar: '',
    html_content: '',
    variables: [],
    category: 'general'
  });

  const [previewVariables, setPreviewVariables] = useState({});

  useEffect(() => {
    fetchTemplates();
  }, []);

  const fetchTemplates = async () => {
    try {
      setLoading(true);
      const response = await apiHelpers.get('/email-templates');
      if (response.success) {
        setTemplates(response.data);
      }
    } catch (error) {
      console.error('Error fetching templates:', error);
      toast.error(
        isRTL 
          ? 'فشل في تحميل القوالب' 
          : 'Failed to load templates'
      );
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!formData.name.trim() || !formData.html_content.trim()) {
      toast.error(
        isRTL 
          ? 'اسم القالب ومحتوى HTML مطلوبان' 
          : 'Template name and HTML content are required'
      );
      return;
    }

    try {
      const response = editingTemplate
        ? await apiHelpers.put(`/email-templates/${editingTemplate.id}`, formData)
        : await apiHelpers.post('/email-templates', formData);

      if (response.success) {
        toast.success(
          isRTL 
            ? (editingTemplate ? 'تم تحديث القالب بنجاح' : 'تم إنشاء القالب بنجاح')
            : (editingTemplate ? 'Template updated successfully' : 'Template created successfully')
        );
        
        resetForm();
        fetchTemplates();
      }
    } catch (error) {
      console.error('Error saving template:', error);
      toast.error(
        isRTL 
          ? 'فشل في حفظ القالب' 
          : 'Failed to save template'
      );
    }
  };

  const handleEdit = (template) => {
    setEditingTemplate(template);
    setFormData({
      name: template.name || '',
      name_ar: template.name_ar || '',
      description: template.description || '',
      description_ar: template.description_ar || '',
      html_content: template.html_content || '',
      variables: template.variables || [],
      category: template.category || 'general'
    });
    setShowForm(true);
  };

  const handleDelete = async (templateId) => {
    if (!window.confirm(
      isRTL 
        ? 'هل أنت متأكد من حذف هذا القالب؟' 
        : 'Are you sure you want to delete this template?'
    )) {
      return;
    }

    try {
      const response = await apiHelpers.delete(`/email-templates/${templateId}`);
      if (response.success) {
        toast.success(
          isRTL 
            ? 'تم حذف القالب بنجاح' 
            : 'Template deleted successfully'
        );
        fetchTemplates();
      }
    } catch (error) {
      console.error('Error deleting template:', error);
      const errorMessage = error.response?.data?.error || error.message;
      toast.error(
        isRTL 
          ? `فشل في حذف القالب: ${errorMessage}` 
          : `Failed to delete template: ${errorMessage}`
      );
    }
  };

  const handlePreview = async (template) => {
    try {
      // Set default preview variables
      const defaultVariables = {
        direction: isRTL ? 'rtl' : 'ltr',
        language: isRTL ? 'ar' : 'en',
        subject: isRTL ? 'موضوع الرسالة' : 'Email Subject',
        company_name: isRTL ? 'اسم الشركة' : 'Company Name',
        title: isRTL ? 'عنوان الرسالة' : 'Email Title',
        greeting: isRTL ? 'مرحباً' : 'Hello',
        client_name: isRTL ? 'اسم العميل' : 'Client Name',
        content: isRTL ? 'محتوى الرسالة هنا...' : 'Email content goes here...',
        company_address: isRTL ? 'عنوان الشركة' : 'Company Address',
        unsubscribe_url: '#',
        unsubscribe_text: isRTL ? 'إلغاء الاشتراك' : 'Unsubscribe',
        tracking_pixel: '/api/tracking/pixel/preview'
      };

      setPreviewVariables(defaultVariables);

      const response = await apiHelpers.post(`/email-templates/${template.id}/preview`, {
        variables: defaultVariables
      });

      if (response.success) {
        setPreviewHtml(response.data.html);
        setShowPreview(true);
      }
    } catch (error) {
      console.error('Error previewing template:', error);
      toast.error(
        isRTL 
          ? 'فشل في معاينة القالب' 
          : 'Failed to preview template'
      );
    }
  };

  const resetForm = () => {
    setFormData({
      name: '',
      name_ar: '',
      description: '',
      description_ar: '',
      html_content: '',
      variables: [],
      category: 'general'
    });
    setEditingTemplate(null);
    setShowForm(false);
  };

  const extractVariables = (htmlContent) => {
    const regex = /\{\{([^}]+)\}\}/g;
    const variables = [];
    let match;
    
    while ((match = regex.exec(htmlContent)) !== null) {
      const variable = match[1].trim();
      if (!variables.includes(variable)) {
        variables.push(variable);
      }
    }
    
    return variables;
  };

  const handleHtmlChange = (value) => {
    setFormData(prev => ({
      ...prev,
      html_content: value,
      variables: extractVariables(value)
    }));
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 p-6">
        <div className="max-w-6xl mx-auto">
          <div className="flex items-center justify-center py-20">
            <LoadingSpinner size="lg" />
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 mb-2">
                {isRTL ? 'إدارة قوالب البريد الإلكتروني' : 'Email Templates Management'}
              </h1>
              <p className="text-gray-600">
                {isRTL 
                  ? 'إنشاء وإدارة قوالب البريد الإلكتروني المخصصة'
                  : 'Create and manage custom email templates'
                }
              </p>
            </div>
            <button
              onClick={() => setShowForm(true)}
              className="flex items-center space-x-2 rtl:space-x-reverse px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
            >
              <Plus className="h-4 w-4" />
              <span>{isRTL ? 'إضافة قالب' : 'Add Template'}</span>
            </button>
          </div>
        </div>

        {/* Templates Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
          {templates.map((template) => (
            <div key={template.id} className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
              <div className="p-6">
                <div className="flex items-start justify-between mb-4">
                  <div>
                    <h3 className="text-lg font-medium text-gray-900 mb-1">
                      {isRTL ? (template.name_ar || template.name) : template.name}
                    </h3>
                    {(template.description || template.description_ar) && (
                      <p className="text-sm text-gray-500">
                        {isRTL ? (template.description_ar || template.description) : template.description}
                      </p>
                    )}
                  </div>
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                    {template.category}
                  </span>
                </div>

                <div className="mb-4">
                  <p className="text-xs text-gray-500 mb-2">
                    {isRTL ? 'المتغيرات المتاحة:' : 'Available Variables:'}
                  </p>
                  <div className="flex flex-wrap gap-1">
                    {template.variables.slice(0, 3).map((variable, index) => (
                      <span key={index} className="inline-flex items-center px-2 py-1 rounded text-xs bg-gray-100 text-gray-700">
                        {`{{${variable}}}`}
                      </span>
                    ))}
                    {template.variables.length > 3 && (
                      <span className="text-xs text-gray-500">
                        +{template.variables.length - 3} {isRTL ? 'أخرى' : 'more'}
                      </span>
                    )}
                  </div>
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2 rtl:space-x-reverse">
                    <button
                      onClick={() => handlePreview(template)}
                      className="p-1 text-blue-600 hover:text-blue-800"
                      title={isRTL ? 'معاينة' : 'Preview'}
                    >
                      <Eye className="h-4 w-4" />
                    </button>
                    <button
                      onClick={() => handleEdit(template)}
                      className="p-1 text-green-600 hover:text-green-800"
                      title={isRTL ? 'تعديل' : 'Edit'}
                    >
                      <Edit className="h-4 w-4" />
                    </button>
                    <button
                      onClick={() => handleDelete(template.id)}
                      className="p-1 text-red-600 hover:text-red-800"
                      title={isRTL ? 'حذف' : 'Delete'}
                    >
                      <Trash2 className="h-4 w-4" />
                    </button>
                  </div>
                  <span className="text-xs text-gray-400">
                    {new Date(template.created_at).toLocaleDateString()}
                  </span>
                </div>
              </div>
            </div>
          ))}
        </div>

        {templates.length === 0 && (
          <div className="text-center py-12">
            <FileText className="h-12 w-12 mx-auto text-gray-300 mb-4" />
            <p className="text-gray-500">
              {isRTL ? 'لا توجد قوالب بعد' : 'No templates yet'}
            </p>
          </div>
        )}

        {/* Add/Edit Form Modal */}
        {showForm && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
            <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
              <div className="flex items-center justify-between p-6 border-b border-gray-200">
                <h3 className="text-lg font-medium text-gray-900">
                  {editingTemplate 
                    ? (isRTL ? 'تعديل القالب' : 'Edit Template')
                    : (isRTL ? 'إضافة قالب جديد' : 'Add New Template')
                  }
                </h3>
                <button
                  onClick={resetForm}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <X className="h-5 w-5" />
                </button>
              </div>

              <form onSubmit={handleSubmit} className="p-6 space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {/* English Name */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      {isRTL ? 'الاسم (إنجليزي) *' : 'Name (English) *'}
                    </label>
                    <input
                      type="text"
                      value={formData.name}
                      onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder={isRTL ? 'أدخل الاسم بالإنجليزية' : 'Enter name in English'}
                      required
                    />
                  </div>

                  {/* Arabic Name */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      {isRTL ? 'الاسم (عربي)' : 'Name (Arabic)'}
                    </label>
                    <input
                      type="text"
                      value={formData.name_ar}
                      onChange={(e) => setFormData({ ...formData, name_ar: e.target.value })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder={isRTL ? 'أدخل الاسم بالعربية' : 'Enter name in Arabic'}
                    />
                  </div>

                  {/* English Description */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      {isRTL ? 'الوصف (إنجليزي)' : 'Description (English)'}
                    </label>
                    <textarea
                      value={formData.description}
                      onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                      rows={2}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder={isRTL ? 'أدخل الوصف بالإنجليزية' : 'Enter description in English'}
                    />
                  </div>

                  {/* Arabic Description */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      {isRTL ? 'الوصف (عربي)' : 'Description (Arabic)'}
                    </label>
                    <textarea
                      value={formData.description_ar}
                      onChange={(e) => setFormData({ ...formData, description_ar: e.target.value })}
                      rows={2}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder={isRTL ? 'أدخل الوصف بالعربية' : 'Enter description in Arabic'}
                    />
                  </div>

                  {/* Category */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      {isRTL ? 'الفئة' : 'Category'}
                    </label>
                    <select
                      value={formData.category}
                      onChange={(e) => setFormData({ ...formData, category: e.target.value })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                      <option value="general">{isRTL ? 'عام' : 'General'}</option>
                      <option value="newsletter">{isRTL ? 'نشرة إخبارية' : 'Newsletter'}</option>
                      <option value="promotional">{isRTL ? 'ترويجي' : 'Promotional'}</option>
                      <option value="transactional">{isRTL ? 'معاملات' : 'Transactional'}</option>
                    </select>
                  </div>
                </div>

                {/* HTML Content */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    {isRTL ? 'محتوى HTML *' : 'HTML Content *'}
                  </label>
                  <textarea
                    value={formData.html_content}
                    onChange={(e) => handleHtmlChange(e.target.value)}
                    rows={12}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 font-mono text-sm"
                    placeholder={isRTL ? 'أدخل محتوى HTML للقالب...' : 'Enter HTML content for the template...'}
                    required
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    {isRTL 
                      ? 'استخدم {{variable_name}} للمتغيرات القابلة للاستبدال'
                      : 'Use {{variable_name}} for replaceable variables'
                    }
                  </p>
                </div>

                {/* Variables Preview */}
                {formData.variables.length > 0 && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      {isRTL ? 'المتغيرات المكتشفة:' : 'Detected Variables:'}
                    </label>
                    <div className="flex flex-wrap gap-2">
                      {formData.variables.map((variable, index) => (
                        <span key={index} className="inline-flex items-center px-3 py-1 rounded-full text-sm bg-blue-100 text-blue-800">
                          {`{{${variable}}}`}
                        </span>
                      ))}
                    </div>
                  </div>
                )}

                {/* Submit Buttons */}
                <div className="flex items-center justify-end space-x-4 rtl:space-x-reverse pt-4 border-t border-gray-200">
                  <button
                    type="button"
                    onClick={resetForm}
                    className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
                  >
                    {isRTL ? 'إلغاء' : 'Cancel'}
                  </button>
                  <button
                    type="submit"
                    className="flex items-center space-x-2 rtl:space-x-reverse px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                  >
                    <Save className="h-4 w-4" />
                    <span>
                      {editingTemplate 
                        ? (isRTL ? 'تحديث' : 'Update')
                        : (isRTL ? 'حفظ' : 'Save')
                      }
                    </span>
                  </button>
                </div>
              </form>
            </div>
          </div>
        )}

        {/* Preview Modal */}
        {showPreview && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
            <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden">
              <div className="flex items-center justify-between p-6 border-b border-gray-200">
                <h3 className="text-lg font-medium text-gray-900">
                  {isRTL ? 'معاينة القالب' : 'Template Preview'}
                </h3>
                <button
                  onClick={() => setShowPreview(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <X className="h-5 w-5" />
                </button>
              </div>
              <div className="p-6 overflow-y-auto max-h-[calc(90vh-120px)]">
                <div 
                  className="border border-gray-200 rounded-lg p-4 bg-gray-50"
                  dangerouslySetInnerHTML={{ __html: previewHtml }}
                />
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default EmailTemplates;
