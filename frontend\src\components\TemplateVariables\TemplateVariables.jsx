import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Settings, Plus, Trash2, AlertCircle } from 'lucide-react';

const TemplateVariables = ({ template, variables, onVariablesChange }) => {
  const { t, i18n } = useTranslation();
  const isRTL = i18n.language === 'ar';
  
  const [customVariables, setCustomVariables] = useState({});
  const [detectedVariables, setDetectedVariables] = useState([]);

  // Known system variables that shouldn't be shown as custom
  const systemVariables = [
    'direction', 'language', 'subject', 'company_name', 'company_name_ar', 'company_name_en',
    'company_address', 'company_address_ar', 'company_address_en', 'title', 'title_ar', 'title_en',
    'greeting', 'greeting_ar', 'greeting_en', 'client_name', 'content', 'unsubscribe_url',
    'unsubscribe_text', 'unsubscribe_text_ar', 'unsubscribe_text_en', 'thank_you', 'thank_you_ar',
    'thank_you_en', 'best_regards', 'best_regards_ar', 'best_regards_en', 'tracking_pixel'
  ];

  // Extract variables from template content
  useEffect(() => {
    if (!template?.html_content) {
      setDetectedVariables([]);
      return;
    }

    // Find all {{variable}} patterns
    const variablePattern = /\{\{([^}]+)\}\}/g;
    const matches = template.html_content.match(variablePattern) || [];
    
    // Extract variable names and filter out system variables
    const variables = matches
      .map(match => match.replace(/[{}]/g, '').trim())
      .filter((variable, index, array) => array.indexOf(variable) === index) // Remove duplicates
      .filter(variable => !systemVariables.includes(variable))
      .sort();

    setDetectedVariables(variables);

    // Initialize custom variables with existing values or empty strings
    const initialVariables = {};
    variables.forEach(variable => {
      initialVariables[variable] = variables[variable] || '';
    });
    setCustomVariables(initialVariables);

  }, [template?.html_content, variables]);

  // Update parent component when variables change
  useEffect(() => {
    if (onVariablesChange) {
      onVariablesChange(customVariables);
    }
  }, [customVariables, onVariablesChange]);

  const handleVariableChange = (variableName, value) => {
    setCustomVariables(prev => ({
      ...prev,
      [variableName]: value
    }));
  };

  const addCustomVariable = () => {
    const variableName = prompt(
      isRTL 
        ? 'أدخل اسم المتغير الجديد (بدون {{}})'
        : 'Enter new variable name (without {{}})'
    );
    
    if (variableName && variableName.trim()) {
      const cleanName = variableName.trim();
      if (!detectedVariables.includes(cleanName) && !systemVariables.includes(cleanName)) {
        setDetectedVariables(prev => [...prev, cleanName].sort());
        setCustomVariables(prev => ({
          ...prev,
          [cleanName]: ''
        }));
      }
    }
  };

  const removeCustomVariable = (variableName) => {
    setDetectedVariables(prev => prev.filter(v => v !== variableName));
    setCustomVariables(prev => {
      const newVars = { ...prev };
      delete newVars[variableName];
      return newVars;
    });
  };

  if (!template) {
    return null;
  }

  return (
    <div className="bg-white rounded-lg shadow-sm border p-6">
      <div className="flex items-center gap-2 mb-4">
        <Settings className="h-5 w-5 text-blue-600" />
        <h3 className="text-lg font-semibold text-gray-900">
          {isRTL ? 'متغيرات القالب المخصصة' : 'Custom Template Variables'}
        </h3>
      </div>

      {detectedVariables.length === 0 ? (
        <div className="text-center py-8">
          <AlertCircle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <p className="text-gray-500">
            {isRTL 
              ? 'لم يتم العثور على متغيرات مخصصة في هذا القالب'
              : 'No custom variables found in this template'
            }
          </p>
          <p className="text-sm text-gray-400 mt-2">
            {isRTL 
              ? 'استخدم {{variable_name}} في القالب لإنشاء متغيرات مخصصة'
              : 'Use {{variable_name}} in template to create custom variables'
            }
          </p>
        </div>
      ) : (
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <p className="text-sm text-gray-600">
              {isRTL 
                ? `تم العثور على ${detectedVariables.length} متغير مخصص`
                : `Found ${detectedVariables.length} custom variable(s)`
              }
            </p>
            <button
              onClick={addCustomVariable}
              className="btn btn-sm btn-secondary flex items-center gap-1"
            >
              <Plus className="h-3 w-3" />
              {isRTL ? 'إضافة متغير' : 'Add Variable'}
            </button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {detectedVariables.map((variable) => (
              <div key={variable} className="space-y-2">
                <div className="flex items-center justify-between">
                  <label className="block text-sm font-medium text-gray-700">
                    <code className="bg-gray-100 px-2 py-1 rounded text-xs">
                      {`{{${variable}}}`}
                    </code>
                  </label>
                  <button
                    onClick={() => removeCustomVariable(variable)}
                    className="text-red-600 hover:text-red-800 p-1"
                    title={isRTL ? 'حذف المتغير' : 'Remove variable'}
                  >
                    <Trash2 className="h-3 w-3" />
                  </button>
                </div>
                
                <div className="space-y-2">
                  {/* Arabic value */}
                  <div>
                    <label className="block text-xs text-gray-500 mb-1">
                      {isRTL ? 'القيمة بالعربية' : 'Arabic Value'}
                    </label>
                    <input
                      type="text"
                      className="input text-sm"
                      placeholder={isRTL ? 'أدخل القيمة بالعربية' : 'Enter Arabic value'}
                      value={customVariables[`${variable}_ar`] || ''}
                      onChange={(e) => handleVariableChange(`${variable}_ar`, e.target.value)}
                    />
                  </div>
                  
                  {/* English value */}
                  <div>
                    <label className="block text-xs text-gray-500 mb-1">
                      {isRTL ? 'القيمة بالإنجليزية' : 'English Value'}
                    </label>
                    <input
                      type="text"
                      className="input text-sm"
                      placeholder={isRTL ? 'أدخل القيمة بالإنجليزية' : 'Enter English value'}
                      value={customVariables[`${variable}_en`] || ''}
                      onChange={(e) => handleVariableChange(`${variable}_en`, e.target.value)}
                    />
                  </div>
                  
                  {/* Default value (language-based) */}
                  <div>
                    <label className="block text-xs text-gray-500 mb-1">
                      {isRTL ? 'القيمة الافتراضية' : 'Default Value'}
                    </label>
                    <input
                      type="text"
                      className="input text-sm"
                      placeholder={isRTL ? 'القيمة الافتراضية' : 'Default value'}
                      value={customVariables[variable] || ''}
                      onChange={(e) => handleVariableChange(variable, e.target.value)}
                    />
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Instructions */}
      <div className="mt-6 p-4 bg-blue-50 rounded-lg">
        <h5 className="font-medium text-blue-900 mb-2">
          {isRTL ? 'تعليمات الاستخدام:' : 'Usage Instructions:'}
        </h5>
        <ul className="text-sm text-blue-800 space-y-1">
          <li>
            {isRTL 
              ? '• استخدم {{variable_name}} في القالب لإنشاء متغيرات مخصصة'
              : '• Use {{variable_name}} in template to create custom variables'
            }
          </li>
          <li>
            {isRTL 
              ? '• املأ القيم بكلا اللغتين لضمان التوافق'
              : '• Fill values in both languages for compatibility'
            }
          </li>
          <li>
            {isRTL 
              ? '• القيمة الافتراضية ستظهر حسب لغة العميل'
              : '• Default value will show based on client language'
            }
          </li>
          <li>
            {isRTL 
              ? '• يمكنك إضافة متغيرات جديدة باستخدام زر "إضافة متغير"'
              : '• You can add new variables using "Add Variable" button'
            }
          </li>
        </ul>
      </div>
    </div>
  );
};

export default TemplateVariables;
