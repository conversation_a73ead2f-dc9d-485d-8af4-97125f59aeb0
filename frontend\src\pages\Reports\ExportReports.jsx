import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { toast } from 'react-hot-toast';
import { FiDownload, FiFileText, FiTable, FiBarChart, FiUsers, FiMail } from 'react-icons/fi';
import LoadingSpinner from '../../components/UI/LoadingSpinner';
import { apiHelpers, endpoints } from '../../services/api';

const ExportReports = () => {
  const { t, i18n } = useTranslation();
  const isRTL = i18n.language === 'ar';

  const [loading, setLoading] = useState(false);
  const [selectedReports, setSelectedReports] = useState([]);
  const [exportFormat, setExportFormat] = useState('csv');
  const [dateRange, setDateRange] = useState({
    from: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
    to: new Date().toISOString().split('T')[0]
  });

  // Available reports
  const availableReports = [
    {
      id: 'dashboard',
      name: isRTL ? 'إحصائيات لوحة التحكم' : 'Dashboard Statistics',
      description: isRTL ? 'إجمالي العملاء والرسائل والتفاعلات' : 'Total clients, messages, and interactions',
      icon: FiBarChart,
      color: 'blue'
    },
    {
      id: 'clients',
      name: isRTL ? 'بيانات العملاء' : 'Client Data',
      description: isRTL ? 'قائمة شاملة بجميع العملاء' : 'Comprehensive list of all clients',
      icon: FiUsers,
      color: 'green'
    },
    {
      id: 'messages',
      name: isRTL ? 'بيانات الرسائل' : 'Message Data',
      description: isRTL ? 'قائمة بجميع الرسائل المرسلة' : 'List of all sent messages',
      icon: FiMail,
      color: 'purple'
    },
    {
      id: 'interactions',
      name: isRTL ? 'بيانات التفاعلات' : 'Interaction Data',
      description: isRTL ? 'سجل جميع التفاعلات مع الرسائل' : 'Record of all message interactions',
      icon: FiTable,
      color: 'orange'
    }
  ];

  // Handle report selection
  const handleReportToggle = (reportId) => {
    setSelectedReports(prev => 
      prev.includes(reportId) 
        ? prev.filter(id => id !== reportId)
        : [...prev, reportId]
    );
  };

  // Handle export
  const handleExport = async () => {
    if (selectedReports.length === 0) {
      toast.error(isRTL ? 'يرجى اختيار تقرير واحد على الأقل' : 'Please select at least one report');
      return;
    }

    setLoading(true);

    try {
      for (const reportType of selectedReports) {
        const params = new URLSearchParams({
          format: exportFormat,
          date_from: dateRange.from,
          date_to: dateRange.to
        });

        const url = `${endpoints.reports.export(reportType)}?${params}`;
        
        // Create download link
        const link = document.createElement('a');
        link.href = url;
        link.download = `${reportType}_report.${exportFormat}`;
        
        // Add authorization header for fetch
        const response = await fetch(url, {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`
          }
        });

        if (response.ok) {
          const blob = await response.blob();
          const downloadUrl = window.URL.createObjectURL(blob);
          link.href = downloadUrl;
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
          window.URL.revokeObjectURL(downloadUrl);
        } else {
          throw new Error(`Failed to export ${reportType}`);
        }
      }

      toast.success(isRTL ? 'تم تصدير التقارير بنجاح' : 'Reports exported successfully');
    } catch (error) {
      console.error('Export error:', error);
      toast.error(isRTL ? 'فشل في تصدير التقارير' : 'Failed to export reports');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3 rtl:space-x-reverse">
          <FiDownload className="h-8 w-8 text-blue-600" />
          <div>
            <h1 className="text-3xl font-bold text-gray-900">
              {isRTL ? 'تصدير التقارير' : 'Export Reports'}
            </h1>
            <p className="mt-2 text-gray-600">
              {isRTL ? 'اختر التقارير التي تريد تصديرها' : 'Select the reports you want to export'}
            </p>
          </div>
        </div>
      </div>

      {/* Export Configuration */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Report Selection */}
        <div className="lg:col-span-2">
          <div className="card">
            <div className="card-header">
              <h3 className="text-lg font-semibold text-gray-900">
                {isRTL ? 'اختيار التقارير' : 'Select Reports'}
              </h3>
            </div>
            <div className="card-body">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {availableReports.map((report) => {
                  const Icon = report.icon;
                  const isSelected = selectedReports.includes(report.id);
                  
                  return (
                    <div
                      key={report.id}
                      onClick={() => handleReportToggle(report.id)}
                      className={`p-4 border-2 rounded-lg cursor-pointer transition-all ${
                        isSelected
                          ? `border-${report.color}-500 bg-${report.color}-50`
                          : 'border-gray-200 hover:border-gray-300'
                      }`}
                    >
                      <div className="flex items-start space-x-3 rtl:space-x-reverse">
                        <div className={`p-2 rounded-lg bg-${report.color}-100`}>
                          <Icon className={`h-5 w-5 text-${report.color}-600`} />
                        </div>
                        <div className="flex-1">
                          <div className="flex items-center space-x-2 rtl:space-x-reverse">
                            <h4 className="font-medium text-gray-900">{report.name}</h4>
                            {isSelected && (
                              <div className={`w-2 h-2 rounded-full bg-${report.color}-500`}></div>
                            )}
                          </div>
                          <p className="text-sm text-gray-600 mt-1">{report.description}</p>
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          </div>
        </div>

        {/* Export Settings */}
        <div className="space-y-6">
          {/* Format Selection */}
          <div className="card">
            <div className="card-header">
              <h3 className="text-lg font-semibold text-gray-900">
                {isRTL ? 'تنسيق التصدير' : 'Export Format'}
              </h3>
            </div>
            <div className="card-body space-y-4">
              <div className="space-y-3">
                <label className="flex items-center space-x-3 rtl:space-x-reverse">
                  <input
                    type="radio"
                    name="format"
                    value="csv"
                    checked={exportFormat === 'csv'}
                    onChange={(e) => setExportFormat(e.target.value)}
                    className="form-radio"
                  />
                  <div>
                    <div className="font-medium">CSV</div>
                    <div className="text-sm text-gray-600">
                      {isRTL ? 'ملف جدول بيانات' : 'Spreadsheet file'}
                    </div>
                  </div>
                </label>

                <label className="flex items-center space-x-3 rtl:space-x-reverse">
                  <input
                    type="radio"
                    name="format"
                    value="pdf"
                    checked={exportFormat === 'pdf'}
                    onChange={(e) => setExportFormat(e.target.value)}
                    className="form-radio"
                  />
                  <div>
                    <div className="font-medium">PDF</div>
                    <div className="text-sm text-gray-600">
                      {isRTL ? 'ملف PDF منسق' : 'Formatted PDF file'}
                    </div>
                  </div>
                </label>
              </div>
            </div>
          </div>

          {/* Date Range */}
          <div className="card">
            <div className="card-header">
              <h3 className="text-lg font-semibold text-gray-900">
                {isRTL ? 'نطاق التاريخ' : 'Date Range'}
              </h3>
            </div>
            <div className="card-body space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  {isRTL ? 'من تاريخ' : 'From Date'}
                </label>
                <input
                  type="date"
                  value={dateRange.from}
                  onChange={(e) => setDateRange(prev => ({ ...prev, from: e.target.value }))}
                  className="input"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  {isRTL ? 'إلى تاريخ' : 'To Date'}
                </label>
                <input
                  type="date"
                  value={dateRange.to}
                  onChange={(e) => setDateRange(prev => ({ ...prev, to: e.target.value }))}
                  className="input"
                />
              </div>
            </div>
          </div>

          {/* Export Button */}
          <button
            onClick={handleExport}
            disabled={loading || selectedReports.length === 0}
            className="btn btn-primary w-full flex items-center justify-center space-x-2 rtl:space-x-reverse"
          >
            {loading ? (
              <LoadingSpinner size="small" />
            ) : (
              <>
                <FiDownload className="h-4 w-4" />
                <span>
                  {isRTL ? 'تصدير التقارير' : 'Export Reports'} 
                  {selectedReports.length > 0 && ` (${selectedReports.length})`}
                </span>
              </>
            )}
          </button>

          {/* Selected Reports Summary */}
          {selectedReports.length > 0 && (
            <div className="card">
              <div className="card-header">
                <h4 className="font-medium text-gray-900">
                  {isRTL ? 'التقارير المحددة' : 'Selected Reports'}
                </h4>
              </div>
              <div className="card-body">
                <div className="space-y-2">
                  {selectedReports.map(reportId => {
                    const report = availableReports.find(r => r.id === reportId);
                    return (
                      <div key={reportId} className="flex items-center space-x-2 rtl:space-x-reverse text-sm">
                        <div className={`w-2 h-2 rounded-full bg-${report.color}-500`}></div>
                        <span>{report.name}</span>
                      </div>
                    );
                  })}
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ExportReports;
