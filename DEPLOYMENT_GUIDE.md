# 🚀 **دليل النشر والاستضافة - نظام التسويق الإلكتروني**

## 📋 **جدول المحتويات**

1. [متطلبات النظام](#متطلبات-النظام)
2. [إعد<PERSON> الخادم](#إعداد-الخادم)
3. [تحضير الملفات](#تحضير-الملفات)
4. [رفع الملفات](#رفع-الملفات)
5. [إعداد قاعدة البيانات](#إعداد-قاعدة-البيانات)
6. [تكوين البيئة](#تكوين-البيئة)
7. [تشغيل النظام](#تشغيل-النظام)
8. [إعداد النطاق والـ SSL](#إعداد-النطاق-والـ-ssl)
9. [النسخ الاحتياطي](#النسخ-الاحتياطي)
10. [استكشاف الأخطاء](#استكشاف-الأخطاء)

---

## 🖥️ **متطلبات النظام**

### **متطلبات الخادم الأساسية:**

**نظام التشغيل:**
- ✅ Ubuntu 20.04 LTS أو أحدث (مفضل)
- ✅ CentOS 8 أو أحدث
- ✅ Debian 10 أو أحدث
- ✅ Windows Server 2019 أو أحدث

**المواصفات الدنيا:**
- **المعالج**: 2 Core CPU
- **الذاكرة**: 2 GB RAM
- **التخزين**: 20 GB SSD
- **النطاق الترددي**: 100 Mbps

**المواصفات المُوصى بها:**
- **المعالج**: 4 Core CPU
- **الذاكرة**: 4 GB RAM
- **التخزين**: 50 GB SSD
- **النطاق الترددي**: 1 Gbps

### **البرامج المطلوبة:**

**Node.js:**
- الإصدار 18.x أو أحدث
- npm 9.x أو أحدث

**خادم الويب:**
- Nginx (مفضل)
- Apache HTTP Server
- أو أي خادم ويب يدعم reverse proxy

**أدوات إضافية:**
- Git
- PM2 (لإدارة العمليات)
- UFW أو iptables (للحماية)

---

## ⚙️ **إعداد الخادم**

### **الخطوة 1: تحديث النظام**

**Ubuntu/Debian:**
```bash
sudo apt update && sudo apt upgrade -y
sudo apt install curl wget git unzip -y
```

**CentOS/RHEL:**
```bash
sudo yum update -y
sudo yum install curl wget git unzip -y
```

### **الخطوة 2: تثبيت Node.js**

**الطريقة الأولى: باستخدام NodeSource:**
```bash
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs
```

**الطريقة الثانية: باستخدام NVM:**
```bash
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash
source ~/.bashrc
nvm install 18
nvm use 18
```

**التحقق من التثبيت:**
```bash
node --version  # يجب أن يظهر v18.x.x
npm --version   # يجب أن يظهر 9.x.x
```

### **الخطوة 3: تثبيت PM2**

```bash
sudo npm install -g pm2
pm2 --version
```

### **الخطوة 4: تثبيت Nginx**

**Ubuntu/Debian:**
```bash
sudo apt install nginx -y
sudo systemctl start nginx
sudo systemctl enable nginx
```

**CentOS/RHEL:**
```bash
sudo yum install nginx -y
sudo systemctl start nginx
sudo systemctl enable nginx
```

### **الخطوة 5: إعداد الحماية**

**تفعيل UFW:**
```bash
sudo ufw enable
sudo ufw allow ssh
sudo ufw allow 'Nginx Full'
sudo ufw allow 3000  # منفذ التطبيق
sudo ufw status
```

---

## 📁 **تحضير الملفات**

### **الخطوة 1: إنشاء مجلد التطبيق**

```bash
sudo mkdir -p /var/www/marketing-email
sudo chown $USER:$USER /var/www/marketing-email
cd /var/www/marketing-email
```

### **الخطوة 2: رفع الملفات**

**الطريقة الأولى: باستخدام Git (مفضل):**
```bash
git clone <repository-url> .
```

**الطريقة الثانية: رفع يدوي:**
1. ضغط مجلد المشروع في ملف ZIP
2. رفع الملف للخادم باستخدام SCP أو FTP
3. فك الضغط:
```bash
unzip marketing-email.zip
mv marketing-email/* .
rm -rf marketing-email marketing-email.zip
```

### **الخطوة 3: تثبيت التبعيات**

**Backend:**
```bash
cd backend
npm install --production
```

**Frontend:**
```bash
cd ../frontend
npm install
npm run build
```

---

## 🗄️ **إعداد قاعدة البيانات**

### **إنشاء قاعدة البيانات:**

قاعدة البيانات SQLite ستُنشأ تلقائياً عند أول تشغيل. تأكد من الصلاحيات:

```bash
cd /var/www/marketing-email/backend
mkdir -p data
chmod 755 data
```

### **إعداد البيانات الأولية:**

```bash
# تشغيل النظام لأول مرة لإنشاء الجداول
cd /var/www/marketing-email/backend
npm start &
sleep 10
pkill -f "node"
```

---

## 🔧 **تكوين البيئة**

### **إنشاء ملف البيئة:**

```bash
cd /var/www/marketing-email/backend
cp .env.example .env
nano .env
```

**محتوى ملف .env:**
```env
# Server Configuration
PORT=3000
NODE_ENV=production

# Database
DB_PATH=./data/marketing.db

# JWT Secret (غير هذا لقيمة عشوائية قوية)
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production

# Email Configuration (سيتم تكوينها من الواجهة)
SMTP_HOST=
SMTP_PORT=
SMTP_USER=
SMTP_PASS=

# File Upload
MAX_FILE_SIZE=10485760
UPLOAD_PATH=./uploads

# Security
BCRYPT_ROUNDS=12
SESSION_TIMEOUT=24h

# Frontend URL
FRONTEND_URL=http://your-domain.com
```

### **تعيين الصلاحيات:**

```bash
chmod 600 .env
chmod -R 755 /var/www/marketing-email
chmod -R 777 /var/www/marketing-email/backend/data
chmod -R 777 /var/www/marketing-email/backend/uploads
```

---

## 🚀 **تشغيل النظام**

### **إنشاء ملف PM2:**

```bash
cd /var/www/marketing-email
nano ecosystem.config.js
```

**محتوى ecosystem.config.js:**
```javascript
module.exports = {
  apps: [{
    name: 'marketing-email-backend',
    script: './backend/server.js',
    cwd: '/var/www/marketing-email',
    instances: 1,
    exec_mode: 'fork',
    env: {
      NODE_ENV: 'production',
      PORT: 3000
    },
    error_file: './logs/err.log',
    out_file: './logs/out.log',
    log_file: './logs/combined.log',
    time: true,
    max_memory_restart: '1G',
    restart_delay: 4000,
    max_restarts: 10,
    min_uptime: '10s'
  }]
};
```

### **إنشاء مجلد السجلات:**

```bash
mkdir -p /var/www/marketing-email/logs
```

### **تشغيل التطبيق:**

```bash
cd /var/www/marketing-email
pm2 start ecosystem.config.js
pm2 save
pm2 startup
```

### **التحقق من التشغيل:**

```bash
pm2 status
pm2 logs marketing-email-backend
curl http://localhost:3000/api/health
```

---

## 🌐 **إعداد Nginx**

### **إنشاء ملف التكوين:**

```bash
sudo nano /etc/nginx/sites-available/marketing-email
```

**محتوى ملف التكوين:**
```nginx
server {
    listen 80;
    server_name your-domain.com www.your-domain.com;
    
    # Frontend files
    root /var/www/marketing-email/frontend/dist;
    index index.html;
    
    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;
    
    # Frontend routes
    location / {
        try_files $uri $uri/ /index.html;
        add_header Cache-Control "no-cache, no-store, must-revalidate";
        add_header Pragma "no-cache";
        add_header Expires "0";
    }
    
    # Static assets with caching
    location /assets/ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    # API routes
    location /api/ {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        proxy_read_timeout 300s;
        proxy_connect_timeout 75s;
    }
    
    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;
}
```

### **تفعيل الموقع:**

```bash
sudo ln -s /etc/nginx/sites-available/marketing-email /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

---

## 🔒 **إعداد SSL (HTTPS)**

### **تثبيت Certbot:**

```bash
sudo apt install snapd
sudo snap install core; sudo snap refresh core
sudo snap install --classic certbot
sudo ln -s /snap/bin/certbot /usr/bin/certbot
```

### **الحصول على شهادة SSL:**

```bash
sudo certbot --nginx -d your-domain.com -d www.your-domain.com
```

### **تجديد تلقائي:**

```bash
sudo crontab -e
# أضف هذا السطر:
0 12 * * * /usr/bin/certbot renew --quiet
```

---

## 💾 **النسخ الاحتياطي**

### **إنشاء سكريبت النسخ الاحتياطي:**

```bash
sudo nano /usr/local/bin/backup-marketing-email.sh
```

**محتوى السكريبت:**
```bash
#!/bin/bash

# إعدادات النسخ الاحتياطي
APP_DIR="/var/www/marketing-email"
BACKUP_DIR="/var/backups/marketing-email"
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_NAME="marketing-email-backup-$DATE"

# إنشاء مجلد النسخ الاحتياطي
mkdir -p $BACKUP_DIR

# نسخ احتياطي لقاعدة البيانات
echo "Creating database backup..."
cp $APP_DIR/backend/data/marketing.db $BACKUP_DIR/$BACKUP_NAME-database.db

# نسخ احتياطي للملفات المرفوعة
echo "Creating uploads backup..."
tar -czf $BACKUP_DIR/$BACKUP_NAME-uploads.tar.gz -C $APP_DIR/backend uploads/

# نسخ احتياطي لملف البيئة
echo "Creating config backup..."
cp $APP_DIR/backend/.env $BACKUP_DIR/$BACKUP_NAME-env.txt

# حذف النسخ القديمة (أكثر من 30 يوم)
find $BACKUP_DIR -name "marketing-email-backup-*" -mtime +30 -delete

echo "Backup completed: $BACKUP_NAME"
```

### **تعيين الصلاحيات وجدولة النسخ:**

```bash
sudo chmod +x /usr/local/bin/backup-marketing-email.sh
sudo crontab -e
# أضف هذا السطر للنسخ الاحتياطي اليومي في الساعة 2 صباحاً:
0 2 * * * /usr/local/bin/backup-marketing-email.sh
```

---

## 🔍 **مراقبة النظام**

### **مراقبة PM2:**

```bash
# عرض حالة التطبيق
pm2 status

# عرض السجلات
pm2 logs marketing-email-backend

# عرض معلومات مفصلة
pm2 show marketing-email-backend

# إعادة تشغيل
pm2 restart marketing-email-backend

# إيقاف
pm2 stop marketing-email-backend
```

### **مراقبة استخدام الموارد:**

```bash
# استخدام المعالج والذاكرة
htop

# مساحة القرص
df -h

# سجلات النظام
sudo journalctl -u nginx -f
```

### **إعداد تنبيهات:**

```bash
# تثبيت أداة مراقبة
sudo apt install monit

# تكوين مراقبة PM2
sudo nano /etc/monit/conf.d/marketing-email
```

**محتوى ملف المراقبة:**
```
check process marketing-email-backend with pidfile /var/www/marketing-email/.pm2/pids/marketing-email-backend-0.pid
  start program = "/usr/bin/pm2 start /var/www/marketing-email/ecosystem.config.js"
  stop program = "/usr/bin/pm2 stop marketing-email-backend"
  if failed port 3000 protocol http
    request /api/health
    with timeout 10 seconds
    then restart
  if 5 restarts within 5 cycles then timeout
```

---

## 🛠️ **استكشاف الأخطاء**

### **مشاكل شائعة وحلولها:**

#### **1. التطبيق لا يعمل:**

**التحقق من الحالة:**
```bash
pm2 status
pm2 logs marketing-email-backend --lines 50
```

**الحلول المحتملة:**
- تحقق من ملف .env
- تأكد من صلاحيات المجلدات
- تحقق من توفر المنفذ 3000

#### **2. قاعدة البيانات لا تعمل:**

```bash
# تحقق من وجود الملف
ls -la /var/www/marketing-email/backend/data/

# تحقق من الصلاحيات
chmod 777 /var/www/marketing-email/backend/data/
```

#### **3. مشاكل Nginx:**

```bash
# تحقق من التكوين
sudo nginx -t

# عرض السجلات
sudo tail -f /var/log/nginx/error.log

# إعادة تحميل التكوين
sudo systemctl reload nginx
```

#### **4. مشاكل SSL:**

```bash
# تجديد الشهادة
sudo certbot renew --dry-run

# تحقق من انتهاء الصلاحية
sudo certbot certificates
```

#### **5. مشاكل الأداء:**

```bash
# مراقبة استخدام الموارد
pm2 monit

# زيادة الذاكرة المخصصة
pm2 restart marketing-email-backend --max-memory-restart 2G
```

### **سجلات مهمة:**

```bash
# سجلات التطبيق
tail -f /var/www/marketing-email/logs/combined.log

# سجلات Nginx
tail -f /var/log/nginx/access.log
tail -f /var/log/nginx/error.log

# سجلات النظام
sudo journalctl -f
```

---

## 🔄 **التحديثات**

### **تحديث التطبيق:**

```bash
cd /var/www/marketing-email

# إيقاف التطبيق
pm2 stop marketing-email-backend

# نسخ احتياطي
/usr/local/bin/backup-marketing-email.sh

# تحديث الكود
git pull origin main

# تحديث التبعيات
cd backend && npm install --production
cd ../frontend && npm install && npm run build

# إعادة تشغيل
pm2 start marketing-email-backend
```

### **تحديث Node.js:**

```bash
# باستخدام NVM
nvm install 20
nvm use 20

# إعادة تثبيت PM2
npm install -g pm2
pm2 update
```

---

## 📊 **تحسين الأداء**

### **تحسين قاعدة البيانات:**

```bash
# ضغط قاعدة البيانات
sqlite3 /var/www/marketing-email/backend/data/marketing.db "VACUUM;"

# إعادة فهرسة
sqlite3 /var/www/marketing-email/backend/data/marketing.db "REINDEX;"
```

### **تحسين Nginx:**

```nginx
# إضافة للتكوين
worker_processes auto;
worker_connections 1024;

# تحسين الذاكرة المؤقتة
proxy_cache_path /var/cache/nginx levels=1:2 keys_zone=my_cache:10m max_size=10g
                 inactive=60m use_temp_path=off;
```

### **تحسين PM2:**

```javascript
// في ecosystem.config.js
{
  instances: 'max', // استخدام جميع المعالجات
  exec_mode: 'cluster',
  max_memory_restart: '2G',
  node_args: '--max-old-space-size=2048'
}
```

---

## ✅ **قائمة التحقق النهائية**

### **قبل النشر:**
- [ ] تم تثبيت جميع المتطلبات
- [ ] تم تكوين ملف .env بشكل صحيح
- [ ] تم تعيين صلاحيات المجلدات
- [ ] تم اختبار الاتصال بقاعدة البيانات
- [ ] تم بناء Frontend بنجاح

### **بعد النشر:**
- [ ] التطبيق يعمل على المنفذ 3000
- [ ] Nginx يعيد التوجيه بشكل صحيح
- [ ] SSL يعمل بشكل صحيح
- [ ] النسخ الاحتياطي مجدول
- [ ] المراقبة مفعلة

### **اختبار شامل:**
- [ ] تسجيل الدخول يعمل
- [ ] إضافة عميل جديد
- [ ] إرسال رسالة اختبار
- [ ] عرض التقارير
- [ ] تحميل الملفات

---

## 📞 **الدعم والمساعدة**

### **في حالة المشاكل:**

1. **راجع السجلات أولاً**
2. **تحقق من قائمة الأخطاء الشائعة**
3. **تأكد من التكوين**
4. **أعد تشغيل الخدمات**

### **معلومات مفيدة:**

- **مجلد التطبيق**: `/var/www/marketing-email`
- **ملفات السجلات**: `/var/www/marketing-email/logs/`
- **قاعدة البيانات**: `/var/www/marketing-email/backend/data/marketing.db`
- **النسخ الاحتياطي**: `/var/backups/marketing-email/`

**تم إنشاء الدليل بنجاح! 🎉**

---

*آخر تحديث: يناير 2024*
*إصدار الدليل: 1.0*

---
