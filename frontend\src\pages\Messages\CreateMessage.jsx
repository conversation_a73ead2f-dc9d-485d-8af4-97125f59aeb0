import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate, useLocation } from 'react-router-dom';
import {
  Send,
  Save,
  Eye,
  Users,
  Calendar,
  Filter,
  Code,
  Monitor,
  ArrowLeft,
  ArrowRight,
  Clock,
  Target,
  Mail,
  X
} from 'lucide-react';
import { toast } from 'react-hot-toast';
import apiHelpers from '../../utils/apiHelpers';
import LoadingSpinner from '../../components/UI/LoadingSpinner';
import CustomLinkGenerator from '../../components/CustomLinkGenerator/CustomLinkGenerator';
import TemplateVariablesSimple from '../../components/TemplateVariables/TemplateVariablesSimple';

const CreateMessage = () => {
  const { t, i18n } = useTranslation();
  const isRTL = i18n.language === 'ar';
  const navigate = useNavigate();
  const location = useLocation();

  const [loading, setLoading] = useState(false);
  const [dataLoading, setDataLoading] = useState(true);
  const [templates, setTemplates] = useState([]);
  const [clients, setClients] = useState([]);
  const [categories, setCategories] = useState([]);
  const [currentStep, setCurrentStep] = useState(1);
  const [showPreview, setShowPreview] = useState(false);
  const [previewHtml, setPreviewHtml] = useState('');
  const [customLinks, setCustomLinks] = useState([]);
  const [templateVariables, setTemplateVariables] = useState({});

  const [messageData, setMessageData] = useState({
    title: '',
    title_ar: '',
    subject: '',
    subject_ar: '',
    content: '',
    content_ar: '',
    template_id: '',
    type: 'one_time', // one_time or recurring
    campaign_type: 'manual',
    scheduled_at: '',
    recurring_frequency: '', // daily, weekly, monthly
    target_criteria: {
      all_clients: true,
      categories: [],
      status: [],
      interest_levels: [],
      custom_filter: ''
    },
    variables: {}
  });

  const [targetedClients, setTargetedClients] = useState([]);
  const [clientsCount, setClientsCount] = useState(0);

  useEffect(() => {
    const loadInitialData = async () => {
      setDataLoading(true);
      try {
        await Promise.all([
          fetchTemplates(),
          fetchClients(),
          fetchCategories()
        ]);
      } catch (error) {
        console.error('Error loading initial data:', error);
      } finally {
        setDataLoading(false);
      }
    };

    loadInitialData();
  }, []);

  // Load duplicate data if provided
  useEffect(() => {
    if (location.state?.duplicateData) {
      const duplicateData = location.state.duplicateData;
      console.log('📋 Loading duplicate data:', duplicateData);

      setMessageData(prev => ({
        ...prev,
        ...duplicateData,
        // Reset some fields for new message
        id: undefined,
        status: 'draft',
        created_at: undefined,
        updated_at: undefined
      }));

      toast.success(
        isRTL
          ? 'تم تحميل بيانات الرسالة المنسوخة'
          : 'Duplicate message data loaded'
      );
    }
  }, [location.state, isRTL]);

  // Check if client_id is provided in URL params (after duplicate data is loaded)
  useEffect(() => {
    const urlParams = new URLSearchParams(location.search);
    const clientId = urlParams.get('client_id');
    if (clientId && !location.state?.duplicateData) {
      setMessageData(prev => ({
        ...prev,
        recipient_type: 'selected',
        selected_clients: [clientId]
      }));
    }
  }, [location.search, location.state]);

  useEffect(() => {
    if (messageData.target_criteria && clients.length > 0) {
      const timeoutId = setTimeout(() => {
        calculateTargetedClients();
      }, 300); // 300ms debounce

      return () => clearTimeout(timeoutId);
    }
  }, [messageData.target_criteria, clients]);

  // Set default template when templates are loaded
  useEffect(() => {
    if (templates.length > 0 && !messageData.template_id && !location.state?.duplicateData) {
      // Find default template or use first template
      const defaultTemplate = templates.find(t => t.is_default) || templates[0];
      if (defaultTemplate) {
        setMessageData(prev => ({
          ...prev,
          template_id: defaultTemplate.id
        }));
        console.log('📧 Set default template:', defaultTemplate.name);
      }
    }
  }, [templates, messageData.template_id, location.state]);

  const fetchTemplates = async () => {
    try {
      if (templates.length > 0) return; // Prevent duplicate calls
      const response = await apiHelpers.get('/email-templates');
      if (response.success) {
        setTemplates(response.data);
      }
    } catch (error) {
      console.error('Error fetching templates:', error);
    }
  };

  const fetchClients = async () => {
    try {
      if (clients.length > 0) return; // Prevent duplicate calls
      const response = await apiHelpers.get('/clients?limit=1000');
      if (response.success) {
        setClients(response.data.clients || []);
        console.log(`📋 Loaded ${response.data.clients?.length || 0} clients for targeting`);
      }
    } catch (error) {
      console.error('Error fetching clients:', error);
    }
  };

  const fetchCategories = async () => {
    try {
      if (categories.length > 0) return; // Prevent duplicate calls
      const response = await apiHelpers.get('/client-categories');
      if (response.success) {
        setCategories(response.data);
      }
    } catch (error) {
      console.error('Error fetching categories:', error);
    }
  };

  const calculateTargetedClients = () => {
    console.log('🎯 Calculating targeted clients:', {
      totalClients: clients.length,
      criteria: messageData.target_criteria,
      recipientType: messageData.recipient_type,
      selectedClients: messageData.selected_clients
    });

    let filtered = clients.filter(client => !client.unsubscribed);
    console.log(`📊 After filtering unsubscribed: ${filtered.length} clients`);

    // التحقق من نوع المستقبلين
    if (messageData.recipient_type === 'selected' && messageData.selected_clients && messageData.selected_clients.length > 0) {
      console.log('🔍 Using selected clients:', messageData.selected_clients);
      filtered = filtered.filter(client =>
        messageData.selected_clients.includes(client.id.toString()) ||
        messageData.selected_clients.includes(client.id)
      );
      console.log(`✅ Selected clients found: ${filtered.length}`);
    } else if (!messageData.target_criteria.all_clients) {
      console.log('🔍 Applying specific targeting criteria...');
      // Filter by categories
      if (messageData.target_criteria.categories.length > 0) {
        filtered = filtered.filter(client =>
          messageData.target_criteria.categories.includes(client.category)
        );
      }

      // Filter by status
      if (messageData.target_criteria.status.length > 0) {
        filtered = filtered.filter(client =>
          messageData.target_criteria.status.includes(client.status)
        );
      }

      // Filter by interest levels
      if (messageData.target_criteria.interest_levels.length > 0) {
        filtered = filtered.filter(client =>
          messageData.target_criteria.interest_levels.includes(client.interest_level)
        );
      }
    } else {
      console.log('✅ Using all clients (all_clients = true)');
    }

    console.log(`🎯 Final targeted clients: ${filtered.length}`);
    setTargetedClients(filtered);
    setClientsCount(filtered.length);
  };

  const handleStepChange = (step) => {
    setCurrentStep(step);
  };

  const handleNext = () => {
    if (currentStep < 4) {
      setCurrentStep(currentStep + 1);
    }
  };

  const handlePrevious = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handlePreview = async () => {
    try {
      if (!messageData.template_id) {
        toast.error(isRTL ? 'يرجى اختيار قالب أولاً' : 'Please select a template first');
        return;
      }

      const selectedTemplate = templates.find(t => t.id == messageData.template_id);
      if (!selectedTemplate) return;

      // Prepare variables for preview
      const previewVariables = {
        direction: isRTL ? 'rtl' : 'ltr',
        language: isRTL ? 'ar' : 'en',
        subject: isRTL ? (messageData.subject_ar || messageData.subject) : messageData.subject,
        company_name: isRTL ? 'اسم الشركة' : 'Company Name',
        title: isRTL ? (messageData.title_ar || messageData.title) : messageData.title,
        greeting: isRTL ? 'مرحباً' : 'Hello',
        client_name: isRTL ? 'اسم العميل' : 'Client Name',
        content: isRTL ? (messageData.content_ar || messageData.content) : messageData.content,
        company_address: isRTL ? 'عنوان الشركة' : 'Company Address',
        unsubscribe_url: '#unsubscribe',
        unsubscribe_text: isRTL ? 'إلغاء الاشتراك' : 'Unsubscribe',
        tracking_pixel: '/api/tracking/pixel/preview',
        ...messageData.variables
      };

      const response = await apiHelpers.post(`/email-templates/${messageData.template_id}/preview`, {
        variables: previewVariables
      });

      if (response.success) {
        setPreviewHtml(response.data.html);
        setShowPreview(true);
      }
    } catch (error) {
      console.error('Error previewing message:', error);
      toast.error(isRTL ? 'فشل في معاينة الرسالة' : 'Failed to preview message');
    }
  };

  const handleSave = async (isDraft = true) => {
    // Prevent multiple saves
    if (loading) return;

    try {
      setLoading(true);

      const payload = {
        ...messageData,
        status: isDraft ? 'draft' : 'ready',
        target_clients: targetedClients.map(c => c.id),
        total_recipients: clientsCount
      };

      const response = await apiHelpers.post('/messages', payload);

      if (response.success) {
        toast.success(
          isDraft
            ? (isRTL ? 'تم حفظ المسودة بنجاح' : 'Draft saved successfully')
            : (isRTL ? 'تم إنشاء الرسالة بنجاح' : 'Message created successfully')
        );
        navigate('/messages');
      }
    } catch (error) {
      console.error('Error saving message:', error);
      toast.error(isRTL ? 'فشل في حفظ الرسالة' : 'Failed to save message');
    } finally {
      setLoading(false);
    }
  };

  const handleSend = async () => {
    // Prevent multiple sends
    if (loading) return;

    try {
      setLoading(true);

      if (clientsCount === 0) {
        toast.error(isRTL ? 'لا يوجد عملاء مستهدفين' : 'No targeted clients');
        return;
      }

      // التحقق من إعدادات الجدولة
      let finalStatus = 'sending';
      let scheduledAt = null;

      if (messageData.send_type === 'scheduled' && messageData.scheduled_date && messageData.scheduled_time) {
        // تحويل التاريخ والوقت إلى timestamp
        const scheduledDateTime = new Date(`${messageData.scheduled_date}T${messageData.scheduled_time}`);
        const now = new Date();

        if (scheduledDateTime <= now) {
          toast.error(isRTL ? 'وقت الجدولة يجب أن يكون في المستقبل' : 'Scheduled time must be in the future');
          return;
        }

        finalStatus = 'scheduled';
        scheduledAt = scheduledDateTime.toISOString();
      }

      const payload = {
        ...messageData,
        status: finalStatus,
        scheduled_at: scheduledAt,
        target_clients: targetedClients.map(c => c.id),
        total_recipients: clientsCount
      };

      const endpoint = finalStatus === 'scheduled' ? '/messages' : '/messages/send';
      const response = await apiHelpers.post(endpoint, payload);

      if (response.success) {
        const successMessage = finalStatus === 'scheduled'
          ? (isRTL ? 'تم جدولة الرسالة بنجاح' : 'Message scheduled successfully')
          : (isRTL ? 'تم إرسال الرسالة بنجاح' : 'Message sent successfully');

        toast.success(successMessage);
        navigate('/messages');
      }
    } catch (error) {
      console.error('Error sending message:', error);
      toast.error(isRTL ? 'فشل في إرسال الرسالة' : 'Failed to send message');
    } finally {
      setLoading(false);
    }
  };

  // Function to insert custom link into content
  useEffect(() => {
    window.insertCustomLink = (htmlLink) => {
      // Insert into English content
      const currentContent = messageData.content;
      const newContent = currentContent + '\n\n' + htmlLink;
      setMessageData(prev => ({ ...prev, content: newContent }));

      toast.success(
        isRTL
          ? 'تم إدراج الرابط في المحتوى'
          : 'Link inserted into content'
      );
    };

    return () => {
      delete window.insertCustomLink;
    };
  }, [messageData.content, isRTL]);

  const handleTemplateVariablesChange = (variables) => {
    setTemplateVariables(variables);
    setMessageData(prev => ({
      ...prev,
      variables: variables
    }));
  };

  const renderStepContent = () => {
    switch (currentStep) {
      case 1:
        return renderBasicInfo();
      case 2:
        return renderTargeting();
      case 3:
        return renderContent();
      case 4:
        return renderReview();
      default:
        return null;
    }
  };

  const renderBasicInfo = () => (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-medium text-gray-900 mb-4">
          {isRTL ? 'المعلومات الأساسية' : 'Basic Information'}
        </h3>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {/* English Title */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            {isRTL ? 'العنوان (إنجليزي) *' : 'Title (English) *'}
          </label>
          <input
            type="text"
            value={messageData.title}
            onChange={(e) => setMessageData({ ...messageData, title: e.target.value })}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder={isRTL ? 'أدخل العنوان بالإنجليزية' : 'Enter title in English'}
            required
          />
        </div>

        {/* Arabic Title */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            {isRTL ? 'العنوان (عربي)' : 'Title (Arabic)'}
          </label>
          <input
            type="text"
            value={messageData.title_ar}
            onChange={(e) => setMessageData({ ...messageData, title_ar: e.target.value })}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder={isRTL ? 'أدخل العنوان بالعربية' : 'Enter title in Arabic'}
          />
        </div>

        {/* English Subject */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            {isRTL ? 'موضوع البريد (إنجليزي) *' : 'Email Subject (English) *'}
          </label>
          <input
            type="text"
            value={messageData.subject}
            onChange={(e) => setMessageData({ ...messageData, subject: e.target.value })}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder={isRTL ? 'أدخل موضوع البريد بالإنجليزية' : 'Enter email subject in English'}
            required
          />
        </div>

        {/* Arabic Subject */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            {isRTL ? 'موضوع البريد (عربي)' : 'Email Subject (Arabic)'}
          </label>
          <input
            type="text"
            value={messageData.subject_ar}
            onChange={(e) => setMessageData({ ...messageData, subject_ar: e.target.value })}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder={isRTL ? 'أدخل موضوع البريد بالعربية' : 'Enter email subject in Arabic'}
          />
        </div>

        {/* Message Type */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            {isRTL ? 'نوع الرسالة' : 'Message Type'}
          </label>
          <select
            value={messageData.type}
            onChange={(e) => setMessageData({ ...messageData, type: e.target.value })}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="one_time">{isRTL ? 'مرة واحدة' : 'One Time'}</option>
            <option value="recurring">{isRTL ? 'دورية' : 'Recurring'}</option>
          </select>
        </div>

        {/* Recurring Frequency (if recurring) */}
        {messageData.type === 'recurring' && (
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              {isRTL ? 'تكرار الإرسال' : 'Frequency'}
            </label>
            <select
              value={messageData.recurring_frequency}
              onChange={(e) => setMessageData({ ...messageData, recurring_frequency: e.target.value })}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">{isRTL ? 'اختر التكرار' : 'Select Frequency'}</option>
              <option value="daily">{isRTL ? 'يومي' : 'Daily'}</option>
              <option value="weekly">{isRTL ? 'أسبوعي' : 'Weekly'}</option>
              <option value="monthly">{isRTL ? 'شهري' : 'Monthly'}</option>
            </select>
          </div>
        )}

        {/* Scheduled Date */}
        <div className="md:col-span-2">
          <label className="block text-sm font-medium text-gray-700 mb-1">
            {isRTL ? 'موعد الإرسال (اختياري)' : 'Schedule Date (Optional)'}
          </label>
          <input
            type="datetime-local"
            value={messageData.scheduled_at}
            onChange={(e) => setMessageData({ ...messageData, scheduled_at: e.target.value })}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
          <p className="text-xs text-gray-500 mt-1">
            {isRTL
              ? 'اتركه فارغاً للإرسال الفوري'
              : 'Leave empty for immediate sending'
            }
          </p>
        </div>
      </div>
    </div>
  );

  const renderTargeting = () => (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-medium text-gray-900 mb-4">
          {isRTL ? 'تحديد العملاء المستهدفين' : 'Target Audience'}
        </h3>
      </div>

      <div className="space-y-4">
        {/* All Clients Option */}
        <div className="flex items-center">
          <input
            type="radio"
            id="all_clients"
            name="targeting"
            checked={messageData.target_criteria.all_clients}
            onChange={() => setMessageData({
              ...messageData,
              target_criteria: { ...messageData.target_criteria, all_clients: true }
            })}
            className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
          />
          <label htmlFor="all_clients" className="ml-2 rtl:mr-2 rtl:ml-0 block text-sm text-gray-900">
            {isRTL ? 'جميع العملاء النشطين' : 'All Active Clients'}
          </label>
        </div>

        {/* Custom Targeting */}
        <div className="flex items-center">
          <input
            type="radio"
            id="custom_targeting"
            name="targeting"
            checked={!messageData.target_criteria.all_clients}
            onChange={() => setMessageData({
              ...messageData,
              target_criteria: { ...messageData.target_criteria, all_clients: false }
            })}
            className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
          />
          <label htmlFor="custom_targeting" className="ml-2 rtl:mr-2 rtl:ml-0 block text-sm text-gray-900">
            {isRTL ? 'استهداف مخصص' : 'Custom Targeting'}
          </label>
        </div>

        {/* Custom Targeting Options */}
        {!messageData.target_criteria.all_clients && (
          <div className="ml-6 rtl:mr-6 rtl:ml-0 space-y-4 p-4 bg-gray-50 rounded-lg">
            {/* Categories */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                {isRTL ? 'الفئات' : 'Categories'}
              </label>
              <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                {categories.map((category) => (
                  <label key={category.id} className="flex items-center">
                    <input
                      type="checkbox"
                      checked={messageData.target_criteria.categories.includes(category.name.toLowerCase())}
                      onChange={(e) => {
                        const categories = e.target.checked
                          ? [...messageData.target_criteria.categories, category.name.toLowerCase()]
                          : messageData.target_criteria.categories.filter(c => c !== category.name.toLowerCase());
                        setMessageData({
                          ...messageData,
                          target_criteria: { ...messageData.target_criteria, categories }
                        });
                      }}
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                    <span className="ml-2 rtl:mr-2 rtl:ml-0 text-sm text-gray-700">
                      {isRTL ? (category.name_ar || category.name) : category.name}
                    </span>
                  </label>
                ))}
              </div>
            </div>

            {/* Status */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                {isRTL ? 'الحالة' : 'Status'}
              </label>
              <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                {['active', 'inactive', 'pending'].map((status) => (
                  <label key={status} className="flex items-center">
                    <input
                      type="checkbox"
                      checked={messageData.target_criteria.status.includes(status)}
                      onChange={(e) => {
                        const statuses = e.target.checked
                          ? [...messageData.target_criteria.status, status]
                          : messageData.target_criteria.status.filter(s => s !== status);
                        setMessageData({
                          ...messageData,
                          target_criteria: { ...messageData.target_criteria, status: statuses }
                        });
                      }}
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                    <span className="ml-2 rtl:mr-2 rtl:ml-0 text-sm text-gray-700">
                      {status === 'active' ? (isRTL ? 'نشط' : 'Active') :
                       status === 'inactive' ? (isRTL ? 'غير نشط' : 'Inactive') :
                       (isRTL ? 'معلق' : 'Pending')}
                    </span>
                  </label>
                ))}
              </div>
            </div>

            {/* Interest Levels */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                {isRTL ? 'مستوى الاهتمام' : 'Interest Level'}
              </label>
              <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                {['high', 'medium', 'low', 'unknown'].map((level) => (
                  <label key={level} className="flex items-center">
                    <input
                      type="checkbox"
                      checked={messageData.target_criteria.interest_levels.includes(level)}
                      onChange={(e) => {
                        const levels = e.target.checked
                          ? [...messageData.target_criteria.interest_levels, level]
                          : messageData.target_criteria.interest_levels.filter(l => l !== level);
                        setMessageData({
                          ...messageData,
                          target_criteria: { ...messageData.target_criteria, interest_levels: levels }
                        });
                      }}
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                    <span className="ml-2 rtl:mr-2 rtl:ml-0 text-sm text-gray-700">
                      {level === 'high' ? (isRTL ? 'عالي' : 'High') :
                       level === 'medium' ? (isRTL ? 'متوسط' : 'Medium') :
                       level === 'low' ? (isRTL ? 'منخفض' : 'Low') :
                       (isRTL ? 'غير محدد' : 'Unknown')}
                    </span>
                  </label>
                ))}
              </div>
            </div>
          </div>
        )}

        {/* Targeted Clients Count */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex items-center">
            <Target className="h-5 w-5 text-blue-600 mr-2 rtl:ml-2 rtl:mr-0" />
            <span className="text-sm font-medium text-blue-900">
              {isRTL
                ? `العملاء المستهدفين: ${clientsCount} عميل`
                : `Targeted Clients: ${clientsCount} clients`
              }
            </span>
          </div>
        </div>
      </div>
    </div>
  );

  const renderContent = () => (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-medium text-gray-900 mb-4">
          {isRTL ? 'محتوى الرسالة' : 'Message Content'}
        </h3>
      </div>

      <div className="space-y-4">
        {/* Template Selection */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            {isRTL ? 'اختيار القالب *' : 'Select Template *'}
          </label>
          <select
            value={messageData.template_id}
            onChange={(e) => setMessageData({ ...messageData, template_id: e.target.value })}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            required
          >
            <option value="">{isRTL ? 'اختر قالب' : 'Select Template'}</option>
            {templates.map((template) => (
              <option key={template.id} value={template.id}>
                {isRTL ? (template.name_ar || template.name) : template.name}
              </option>
            ))}
          </select>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* English Content */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              {isRTL ? 'المحتوى (إنجليزي) *' : 'Content (English) *'}
            </label>
            <textarea
              value={messageData.content}
              onChange={(e) => setMessageData({ ...messageData, content: e.target.value })}
              rows={8}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder={isRTL ? 'أدخل محتوى الرسالة بالإنجليزية...' : 'Enter message content in English...'}
              required
            />
          </div>

          {/* Arabic Content */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              {isRTL ? 'المحتوى (عربي)' : 'Content (Arabic)'}
            </label>
            <textarea
              value={messageData.content_ar}
              onChange={(e) => setMessageData({ ...messageData, content_ar: e.target.value })}
              rows={8}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder={isRTL ? 'أدخل محتوى الرسالة بالعربية...' : 'Enter message content in Arabic...'}
            />
          </div>
        </div>

        {/* Template Variables */}
        {messageData.template_id && (
          <div>
            <h4 className="text-md font-medium text-gray-900 mb-3">
              {isRTL ? 'متغيرات القالب' : 'Template Variables'}
            </h4>
            <p className="text-sm text-gray-600 mb-4">
              {isRTL
                ? 'املأ هذه المتغيرات لتخصيص القالب حسب احتياجاتك'
                : 'Fill these variables to customize the template according to your needs'
              }
            </p>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  {isRTL ? 'اسم الشركة' : 'Company Name'}
                </label>
                <input
                  type="text"
                  value={messageData.variables?.company_name || ''}
                  onChange={(e) => setMessageData({
                    ...messageData,
                    variables: { ...messageData.variables, company_name: e.target.value }
                  })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder={isRTL ? 'اسم شركتك' : 'Your Company Name'}
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  {isRTL ? 'عنوان الشركة' : 'Company Address'}
                </label>
                <input
                  type="text"
                  value={messageData.variables?.company_address || ''}
                  onChange={(e) => setMessageData({
                    ...messageData,
                    variables: { ...messageData.variables, company_address: e.target.value }
                  })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder={isRTL ? 'عنوان شركتك' : 'Your Company Address'}
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  {isRTL ? 'تحية الافتتاح' : 'Greeting'}
                </label>
                <input
                  type="text"
                  value={messageData.variables?.greeting || ''}
                  onChange={(e) => setMessageData({
                    ...messageData,
                    variables: { ...messageData.variables, greeting: e.target.value }
                  })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder={isRTL ? 'مرحباً' : 'Hello'}
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  {isRTL ? 'نص إلغاء الاشتراك' : 'Unsubscribe Text'}
                </label>
                <input
                  type="text"
                  value={messageData.variables?.unsubscribe_text || ''}
                  onChange={(e) => setMessageData({
                    ...messageData,
                    variables: { ...messageData.variables, unsubscribe_text: e.target.value }
                  })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder={isRTL ? 'إلغاء الاشتراك' : 'Unsubscribe'}
                />
              </div>
            </div>
          </div>
        )}

        {/* Custom Link Generator */}
        <CustomLinkGenerator
          onLinksChange={setCustomLinks}
          messageId={null} // Will be set after message creation
          clientId={null} // Will be set when sending to specific client
        />

        {/* Template Variables */}
        <TemplateVariablesSimple
          template={templates.find(t => t.id == messageData.template_id)}
          variables={templateVariables}
          onVariablesChange={handleTemplateVariablesChange}
        />

        {/* Preview Button */}
        <div className="flex justify-center">
          <button
            type="button"
            onClick={handlePreview}
            className="flex items-center space-x-2 rtl:space-x-reverse px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700"
          >
            <Eye className="h-4 w-4" />
            <span>{isRTL ? 'معاينة الرسالة' : 'Preview Message'}</span>
          </button>
        </div>
      </div>
    </div>
  );

  const renderReview = () => (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-medium text-gray-900 mb-4">
          {isRTL ? 'مراجعة وإرسال' : 'Review & Send'}
        </h3>
      </div>

      <div className="bg-gray-50 rounded-lg p-6 space-y-4">
        {/* Message Info */}
        <div>
          <h4 className="font-medium text-gray-900 mb-2">
            {isRTL ? 'معلومات الرسالة' : 'Message Information'}
          </h4>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div>
              <span className="font-medium">{isRTL ? 'العنوان:' : 'Title:'}</span>
              <span className="ml-2 rtl:mr-2 rtl:ml-0">
                {isRTL ? (messageData.title_ar || messageData.title) : messageData.title}
              </span>
            </div>
            <div>
              <span className="font-medium">{isRTL ? 'الموضوع:' : 'Subject:'}</span>
              <span className="ml-2 rtl:mr-2 rtl:ml-0">
                {isRTL ? (messageData.subject_ar || messageData.subject) : messageData.subject}
              </span>
            </div>
            <div>
              <span className="font-medium">{isRTL ? 'النوع:' : 'Type:'}</span>
              <span className="ml-2 rtl:mr-2 rtl:ml-0">
                {messageData.type === 'one_time'
                  ? (isRTL ? 'مرة واحدة' : 'One Time')
                  : (isRTL ? 'دورية' : 'Recurring')
                }
              </span>
            </div>
            {messageData.scheduled_at && (
              <div>
                <span className="font-medium">{isRTL ? 'موعد الإرسال:' : 'Scheduled:'}</span>
                <span className="ml-2 rtl:mr-2 rtl:ml-0">
                  {new Date(messageData.scheduled_at).toLocaleString('en-US', {
                    year: 'numeric',
                    month: 'short',
                    day: 'numeric',
                    hour: '2-digit',
                    minute: '2-digit',
                    calendar: 'gregory'
                  })}
                </span>
              </div>
            )}
          </div>
        </div>

        {/* Targeting Info */}
        <div>
          <h4 className="font-medium text-gray-900 mb-2">
            {isRTL ? 'الاستهداف' : 'Targeting'}
          </h4>
          <div className="text-sm">
            <div className="flex items-center">
              <Users className="h-4 w-4 text-blue-600 mr-2 rtl:ml-2 rtl:mr-0" />
              <span>
                {isRTL
                  ? `${clientsCount} عميل مستهدف`
                  : `${clientsCount} targeted clients`
                }
              </span>
            </div>
            {!messageData.target_criteria.all_clients && (
              <div className="mt-2 text-gray-600">
                {messageData.target_criteria.categories.length > 0 && (
                  <div>
                    {isRTL ? 'الفئات: ' : 'Categories: '}
                    {messageData.target_criteria.categories.join(', ')}
                  </div>
                )}
                {messageData.target_criteria.status.length > 0 && (
                  <div>
                    {isRTL ? 'الحالة: ' : 'Status: '}
                    {messageData.target_criteria.status.join(', ')}
                  </div>
                )}
              </div>
            )}
          </div>
        </div>

        {/* Template Info */}
        {messageData.template_id && (
          <div>
            <h4 className="font-medium text-gray-900 mb-2">
              {isRTL ? 'القالب' : 'Template'}
            </h4>
            <div className="text-sm">
              {(() => {
                const template = templates.find(t => t.id == messageData.template_id);
                return template ? (
                  <span>{isRTL ? (template.name_ar || template.name) : template.name}</span>
                ) : null;
              })()}
            </div>
          </div>
        )}
      </div>
    </div>
  );

  if (dataLoading) {
    return (
      <div className="min-h-screen bg-gray-50 p-6">
        <div className="max-w-4xl mx-auto">
          <div className="flex items-center justify-center py-20">
            <LoadingSpinner size="lg" />
            <span className="ml-3 rtl:mr-3 rtl:ml-0 text-gray-600">
              {isRTL ? 'جاري تحميل البيانات...' : 'Loading data...'}
            </span>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 mb-2">
                {isRTL ? 'إنشاء رسالة جديدة' : 'Create New Message'}
              </h1>
              <p className="text-gray-600">
                {isRTL
                  ? 'إنشاء وإرسال رسائل بريد إلكتروني للعملاء'
                  : 'Create and send email messages to clients'
                }
              </p>
            </div>
            <button
              onClick={() => navigate('/messages')}
              className="flex items-center space-x-2 rtl:space-x-reverse px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
            >
              {isRTL ? <ArrowRight className="h-4 w-4" /> : <ArrowLeft className="h-4 w-4" />}
              <span>{isRTL ? 'العودة' : 'Back'}</span>
            </button>
          </div>
        </div>

        {/* Steps Navigation */}
        <div className="mb-8">
          <nav className="flex items-center justify-center">
            <ol className="flex items-center space-x-4 rtl:space-x-reverse">
              {[
                { step: 1, name: isRTL ? 'المعلومات الأساسية' : 'Basic Info', icon: Mail },
                { step: 2, name: isRTL ? 'الاستهداف' : 'Targeting', icon: Target },
                { step: 3, name: isRTL ? 'المحتوى' : 'Content', icon: Code },
                { step: 4, name: isRTL ? 'المراجعة' : 'Review', icon: Eye }
              ].map((item, index) => {
                const Icon = item.icon;
                const isActive = currentStep === item.step;
                const isCompleted = currentStep > item.step;

                return (
                  <li key={item.step} className="flex items-center">
                    <button
                      onClick={() => handleStepChange(item.step)}
                      className={`flex items-center space-x-2 rtl:space-x-reverse px-4 py-2 rounded-lg transition-colors ${
                        isActive
                          ? 'bg-blue-600 text-white'
                          : isCompleted
                            ? 'bg-green-100 text-green-800 hover:bg-green-200'
                            : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                      }`}
                    >
                      <Icon className="h-4 w-4" />
                      <span className="text-sm font-medium">{item.name}</span>
                    </button>
                    {index < 3 && (
                      <div className={`w-8 h-0.5 mx-2 ${
                        currentStep > item.step ? 'bg-green-400' : 'bg-gray-300'
                      }`} />
                    )}
                  </li>
                );
              })}
            </ol>
          </nav>
        </div>

        {/* Main Content */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
          {renderStepContent()}
        </div>

        {/* Navigation Buttons */}
        <div className="flex items-center justify-between">
          <button
            onClick={handlePrevious}
            disabled={currentStep === 1}
            className={`flex items-center space-x-2 rtl:space-x-reverse px-4 py-2 rounded-md ${
              currentStep === 1
                ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                : 'bg-gray-600 text-white hover:bg-gray-700'
            }`}
          >
            {isRTL ? <ArrowRight className="h-4 w-4" /> : <ArrowLeft className="h-4 w-4" />}
            <span>{isRTL ? 'السابق' : 'Previous'}</span>
          </button>

          <div className="flex items-center space-x-3 rtl:space-x-reverse">
            {currentStep === 4 ? (
              <>
                <button
                  onClick={() => handleSave(true)}
                  disabled={loading}
                  className="flex items-center space-x-2 rtl:space-x-reverse px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <Save className="h-4 w-4" />
                  {loading && <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-600"></div>}
                  <span>{loading ? (isRTL ? 'جاري الحفظ...' : 'Saving...') : (isRTL ? 'حفظ كمسودة' : 'Save as Draft')}</span>
                </button>
                <button
                  onClick={handleSend}
                  disabled={loading || clientsCount === 0}
                  className="flex items-center space-x-2 rtl:space-x-reverse px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <Send className="h-4 w-4" />
                  {loading && <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>}
                  <span>{loading ? (isRTL ? 'جاري الإرسال...' : 'Sending...') : (isRTL ? 'إرسال الآن' : 'Send Now')}</span>
                </button>
              </>
            ) : (
              <button
                onClick={handleNext}
                className="flex items-center space-x-2 rtl:space-x-reverse px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
              >
                <span>{isRTL ? 'التالي' : 'Next'}</span>
                {isRTL ? <ArrowLeft className="h-4 w-4" /> : <ArrowRight className="h-4 w-4" />}
              </button>
            )}
          </div>
        </div>

        {/* Preview Modal */}
        {showPreview && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
            <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden">
              <div className="flex items-center justify-between p-6 border-b border-gray-200">
                <h3 className="text-lg font-medium text-gray-900">
                  {isRTL ? 'معاينة الرسالة' : 'Message Preview'}
                </h3>
                <button
                  onClick={() => setShowPreview(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <X className="h-5 w-5" />
                </button>
              </div>
              <div className="p-6 overflow-y-auto max-h-[calc(90vh-120px)]">
                <div
                  className="border border-gray-200 rounded-lg p-4 bg-gray-50"
                  dangerouslySetInnerHTML={{ __html: previewHtml }}
                />
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default CreateMessage;