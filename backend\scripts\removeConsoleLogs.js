const fs = require('fs');
const path = require('path');

// <PERSON>ript to remove console.log statements from production files
const removeConsoleLogsFromFile = (filePath) => {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    
    // Replace console.log with development-only logging
    content = content.replace(
      /console\.log\((.*?)\);?/g,
      'if (process.env.NODE_ENV === \'development\') { console.log($1); }'
    );
    
    // Replace console.error with development-only logging for non-critical errors
    content = content.replace(
      /console\.error\((.*?)\);?/g,
      'if (process.env.NODE_ENV === \'development\') { console.error($1); }'
    );
    
    // Replace console.warn with development-only logging
    content = content.replace(
      /console\.warn\((.*?)\);?/g,
      'if (process.env.NODE_ENV === \'development\') { console.warn($1); }'
    );
    
    fs.writeFileSync(filePath, content, 'utf8');
    console.log(`✅ Processed: ${filePath}`);
  } catch (error) {
    console.error(`❌ Error processing ${filePath}:`, error.message);
  }
};

// Files to process
const filesToProcess = [
  'routes/messages.js',
  'routes/clients.js',
  'routes/auth.js',
  'routes/reports.js',
  'routes/interactions.js',
  'models/Client.js',
  'models/Message.js',
  'models/Interaction.js',
  'utils/emailService.js'
];

console.log('🔧 Removing console.log statements from production files...');

filesToProcess.forEach(file => {
  const fullPath = path.join(__dirname, '..', file);
  if (fs.existsSync(fullPath)) {
    removeConsoleLogsFromFile(fullPath);
  } else {
    console.warn(`⚠️  File not found: ${fullPath}`);
  }
});

console.log('✅ Console.log removal completed!');
