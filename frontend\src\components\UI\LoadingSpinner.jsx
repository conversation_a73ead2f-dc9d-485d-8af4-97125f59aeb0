import React from 'react';
import { useTranslation } from 'react-i18next';

const LoadingSpinner = ({ 
  size = 'medium', 
  text = null, 
  fullScreen = false,
  className = '' 
}) => {
  const { t } = useTranslation();

  // Size classes
  const sizeClasses = {
    small: 'w-4 h-4 border-2',
    medium: 'w-8 h-8 border-2',
    large: 'w-12 h-12 border-4',
    'extra-large': 'w-16 h-16 border-4'
  };

  const spinnerClass = `
    spinner
    ${sizeClasses[size]}
    ${className}
  `.trim();

  const content = (
    <div className="flex flex-col items-center justify-center space-y-4">
      <div className={spinnerClass}></div>
      {text && (
        <p className="text-sm text-gray-600 animate-pulse">
          {text}
        </p>
      )}
    </div>
  );

  if (fullScreen) {
    return (
      <div className="fixed inset-0 bg-white bg-opacity-75 flex items-center justify-center z-50">
        <div className="text-center">
          <div className={spinnerClass}></div>
          <p className="mt-4 text-sm text-gray-600">
            {text || t('common.loading')}
          </p>
        </div>
      </div>
    );
  }

  return content;
};

export default LoadingSpinner;
