import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { 
  Mail, 
  Settings, 
  CheckCircle, 
  XCircle, 
  AlertTriangle,
  Eye,
  EyeOff,
  Send,
  RefreshCw,
  HelpCircle,
  ExternalLink
} from 'lucide-react';
import { useForm } from 'react-hook-form';
import toast from 'react-hot-toast';
import LoadingSpinner from '../UI/LoadingSpinner';
import { apiHelpers } from '../../services/api';

const EmailProviderSetup = ({ onConfigurationChange }) => {
  const { t, i18n } = useTranslation();
  const [loading, setLoading] = useState(false);
  const [testing, setTesting] = useState(false);
  const [providers, setProviders] = useState([]);
  const [detectedProvider, setDetectedProvider] = useState(null);
  const [showPassword, setShowPassword] = useState(false);
  const [testResult, setTestResult] = useState(null);
  const [showInstructions, setShowInstructions] = useState(false);

  const isRTL = i18n.language === 'ar';

  const { register, handleSubmit, watch, setValue, formState: { errors } } = useForm({
    defaultValues: {
      email: '',
      password: '',
      provider: 'auto',
      testEmail: ''
    }
  });

  const watchedEmail = watch('email');
  const watchedProvider = watch('provider');

  useEffect(() => {
    loadProviders();
  }, []);

  useEffect(() => {
    if (watchedEmail && watchedEmail.includes('@')) {
      detectProvider(watchedEmail);
    }
  }, [watchedEmail]);

  const loadProviders = async () => {
    try {
      setLoading(true);
      const result = await apiHelpers.get('/api/email-config/providers');
      
      if (result.success) {
        setProviders(result.data.providers);
      }
    } catch (error) {
      console.error('Error loading providers:', error);
      toast.error(t('settings.error_loading_providers'));
    } finally {
      setLoading(false);
    }
  };

  const detectProvider = async (email) => {
    try {
      const result = await apiHelpers.post('/api/email-config/detect', { email });
      
      if (result.success && result.data.detected) {
        setDetectedProvider(result.data);
        setValue('provider', result.data.key);
        
        toast.success(
          isRTL 
            ? `تم اكتشاف ${result.data.provider} تلقائياً`
            : `Auto-detected ${result.data.provider}`
        );
      } else {
        setDetectedProvider(null);
        setValue('provider', 'custom');
      }
    } catch (error) {
      console.error('Error detecting provider:', error);
    }
  };

  const testConfiguration = async (data) => {
    try {
      setTesting(true);
      setTestResult(null);

      const result = await apiHelpers.post('/api/email-config/test', {
        email: data.email,
        password: data.password,
        provider: data.provider === 'auto' ? null : data.provider
      });

      if (result.success) {
        setTestResult({
          success: true,
          message: result.message,
          provider: result.data.provider
        });
        toast.success(t('settings.connection_successful'));
      } else {
        setTestResult({
          success: false,
          message: result.error,
          suggestions: result.data?.suggestions
        });
        toast.error(t('settings.connection_failed'));
      }
    } catch (error) {
      setTestResult({
        success: false,
        message: error.message
      });
      toast.error(t('settings.connection_failed'));
    } finally {
      setTesting(false);
    }
  };

  const sendTestEmail = async (data) => {
    if (!data.testEmail) {
      toast.error(t('settings.test_email_required'));
      return;
    }

    try {
      setLoading(true);
      
      const result = await apiHelpers.post('/api/email-config/test-send', {
        testEmail: data.testEmail
      });

      if (result.success) {
        toast.success(t('settings.test_email_sent'));
      }
    } catch (error) {
      toast.error(t('settings.test_email_failed'));
    } finally {
      setLoading(false);
    }
  };

  const getProviderInstructions = (providerKey) => {
    const provider = providers.find(p => p.key === providerKey);
    return provider?.setupInstructions || {};
  };

  const selectedProvider = providers.find(p => p.key === watchedProvider);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-medium text-gray-900">
            {t('settings.email_provider_setup')}
          </h3>
          <p className="text-sm text-gray-600 mt-1">
            {isRTL 
              ? 'إعداد مزود البريد الإلكتروني تلقائياً أو يدوياً'
              : 'Configure email provider automatically or manually'
            }
          </p>
        </div>
        <button
          onClick={() => setShowInstructions(!showInstructions)}
          className="btn btn-outline btn-sm flex items-center space-x-2 rtl:space-x-reverse"
        >
          <HelpCircle className="h-4 w-4" />
          <span>{isRTL ? 'المساعدة' : 'Help'}</span>
        </button>
      </div>

      {/* Instructions Panel */}
      {showInstructions && (
        <div className="card bg-blue-50 border-blue-200">
          <div className="card-body">
            <h4 className="font-medium text-blue-900 mb-3">
              {isRTL ? 'كيفية الإعداد:' : 'How to Setup:'}
            </h4>
            <div className="space-y-2 text-sm text-blue-800">
              <p>
                {isRTL 
                  ? '1. أدخل عنوان بريدك الإلكتروني - سيتم اكتشاف المزود تلقائياً'
                  : '1. Enter your email address - provider will be auto-detected'
                }
              </p>
              <p>
                {isRTL 
                  ? '2. أدخل كلمة مرور التطبيق (ليس كلمة المرور العادية)'
                  : '2. Enter App Password (not your regular password)'
                }
              </p>
              <p>
                {isRTL 
                  ? '3. اختبر التكوين قبل الحفظ'
                  : '3. Test configuration before saving'
                }
              </p>
            </div>
          </div>
        </div>
      )}

      <form onSubmit={handleSubmit(testConfiguration)} className="space-y-6">
        {/* Email Address */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            {t('settings.email_address')}
          </label>
          <div className="relative">
            <div className={`absolute inset-y-0 ${isRTL ? 'right-0 pr-3' : 'left-0 pl-3'} flex items-center pointer-events-none`}>
              <Mail className="h-5 w-5 text-gray-400" />
            </div>
            <input
              type="email"
              className={`input ${isRTL ? 'pr-10' : 'pl-10'}`}
              placeholder="<EMAIL>"
              {...register('email', {
                required: t('validation.required_field'),
                pattern: {
                  value: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
                  message: t('validation.invalid_email')
                }
              })}
            />
          </div>
          {errors.email && (
            <p className="mt-1 text-sm text-red-600">{errors.email.message}</p>
          )}
        </div>

        {/* Auto-detected Provider Info */}
        {detectedProvider && (
          <div className="card bg-green-50 border-green-200">
            <div className="card-body">
              <div className="flex items-center space-x-3 rtl:space-x-reverse">
                <CheckCircle className="h-5 w-5 text-green-600" />
                <div>
                  <p className="font-medium text-green-900">
                    {detectedProvider.message}
                  </p>
                  <p className="text-sm text-green-700">
                    {isRTL ? 'الإعدادات:' : 'Settings:'} {detectedProvider.configuration.host}:{detectedProvider.configuration.port}
                  </p>
                  {detectedProvider.requiresAppPassword && (
                    <p className="text-sm text-orange-600 mt-1">
                      {isRTL 
                        ? '⚠️ يتطلب كلمة مرور التطبيق'
                        : '⚠️ Requires App Password'
                      }
                    </p>
                  )}
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Provider Selection */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            {isRTL ? 'مزود البريد الإلكتروني' : 'Email Provider'}
          </label>
          <select className="input" {...register('provider')}>
            <option value="auto">{isRTL ? 'اكتشاف تلقائي' : 'Auto-detect'}</option>
            {providers.map((provider) => (
              <option key={provider.key} value={provider.key}>
                {provider.name}
              </option>
            ))}
            <option value="custom">{isRTL ? 'تكوين مخصص' : 'Custom Configuration'}</option>
          </select>
        </div>

        {/* App Password Instructions */}
        {selectedProvider?.requiresAppPassword && (
          <div className="card bg-yellow-50 border-yellow-200">
            <div className="card-body">
              <div className="flex items-start space-x-3 rtl:space-x-reverse">
                <AlertTriangle className="h-5 w-5 text-yellow-600 mt-0.5" />
                <div>
                  <h4 className="font-medium text-yellow-900 mb-2">
                    {isRTL ? 'كلمة مرور التطبيق مطلوبة' : 'App Password Required'}
                  </h4>
                  <div className="text-sm text-yellow-800 space-y-1">
                    {getProviderInstructions(watchedProvider)[isRTL ? 'ar' : 'en']?.map((instruction, index) => (
                      <p key={index}>{instruction}</p>
                    ))}
                  </div>
                  <a
                    href={`https://support.google.com/accounts/answer/185833`}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="inline-flex items-center space-x-1 rtl:space-x-reverse text-sm text-blue-600 hover:text-blue-800 mt-2"
                  >
                    <span>{isRTL ? 'دليل مفصل' : 'Detailed Guide'}</span>
                    <ExternalLink className="h-3 w-3" />
                  </a>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Password */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            {selectedProvider?.requiresAppPassword 
              ? (isRTL ? 'كلمة مرور التطبيق' : 'App Password')
              : t('settings.password')
            }
          </label>
          <div className="relative">
            <div className={`absolute inset-y-0 ${isRTL ? 'right-0 pr-3' : 'left-0 pl-3'} flex items-center pointer-events-none`}>
              <Settings className="h-5 w-5 text-gray-400" />
            </div>
            <input
              type={showPassword ? 'text' : 'password'}
              className={`input ${isRTL ? 'pr-10 pl-10' : 'pl-10 pr-10'}`}
              placeholder="••••••••••••••••"
              {...register('password', {
                required: t('validation.required_field')
              })}
            />
            <button
              type="button"
              className={`absolute inset-y-0 ${isRTL ? 'left-0 pl-3' : 'right-0 pr-3'} flex items-center`}
              onClick={() => setShowPassword(!showPassword)}
            >
              {showPassword ? (
                <EyeOff className="h-5 w-5 text-gray-400" />
              ) : (
                <Eye className="h-5 w-5 text-gray-400" />
              )}
            </button>
          </div>
          {errors.password && (
            <p className="mt-1 text-sm text-red-600">{errors.password.message}</p>
          )}
        </div>

        {/* Test Configuration Button */}
        <div className="flex justify-center">
          <button
            type="submit"
            disabled={testing}
            className="btn btn-primary flex items-center space-x-2 rtl:space-x-reverse"
          >
            {testing ? (
              <LoadingSpinner size="small" />
            ) : (
              <RefreshCw className="h-4 w-4" />
            )}
            <span>{isRTL ? 'اختبار التكوين' : 'Test Configuration'}</span>
          </button>
        </div>

        {/* Test Result */}
        {testResult && (
          <div className={`card ${testResult.success ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'}`}>
            <div className="card-body">
              <div className="flex items-start space-x-3 rtl:space-x-reverse">
                {testResult.success ? (
                  <CheckCircle className="h-5 w-5 text-green-600 mt-0.5" />
                ) : (
                  <XCircle className="h-5 w-5 text-red-600 mt-0.5" />
                )}
                <div className="flex-1">
                  <p className={`font-medium ${testResult.success ? 'text-green-900' : 'text-red-900'}`}>
                    {testResult.message}
                  </p>
                  {testResult.provider && (
                    <p className={`text-sm mt-1 ${testResult.success ? 'text-green-700' : 'text-red-700'}`}>
                      {isRTL ? 'المزود:' : 'Provider:'} {testResult.provider}
                    </p>
                  )}
                  {testResult.suggestions && (
                    <div className="mt-2">
                      <p className="text-sm font-medium text-red-800 mb-1">
                        {isRTL ? 'اقتراحات:' : 'Suggestions:'}
                      </p>
                      <ul className="text-sm text-red-700 space-y-1">
                        {testResult.suggestions[isRTL ? 'ar' : 'en']?.map((suggestion, index) => (
                          <li key={index}>• {suggestion}</li>
                        ))}
                      </ul>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Test Email Section */}
        {testResult?.success && (
          <div className="border-t pt-6">
            <h4 className="text-md font-medium text-gray-900 mb-3">
              {isRTL ? 'إرسال رسالة اختبار' : 'Send Test Email'}
            </h4>
            <div className="flex space-x-3 rtl:space-x-reverse">
              <div className="flex-1">
                <input
                  type="email"
                  className="input"
                  placeholder={isRTL ? 'البريد الإلكتروني للاختبار' : 'Test email address'}
                  {...register('testEmail')}
                />
              </div>
              <button
                type="button"
                onClick={handleSubmit(sendTestEmail)}
                disabled={loading}
                className="btn btn-outline flex items-center space-x-2 rtl:space-x-reverse"
              >
                {loading ? (
                  <LoadingSpinner size="small" />
                ) : (
                  <Send className="h-4 w-4" />
                )}
                <span>{isRTL ? 'إرسال' : 'Send'}</span>
              </button>
            </div>
          </div>
        )}
      </form>
    </div>
  );
};

export default EmailProviderSetup;
