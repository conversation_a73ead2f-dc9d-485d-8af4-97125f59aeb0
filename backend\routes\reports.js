const express = require('express');
const router = express.Router();
const Client = require('../models/Client');
const Message = require('../models/Message');
const Interaction = require('../models/Interaction');
const { db } = require('../config/database');

// Get dashboard overview statistics
router.get('/dashboard', async (req, res) => {
  try {
    console.log('📊 Dashboard statistics requested');
    const [clientStats, messageStats, interactionStats] = await Promise.all([
      Client.getStatistics(),
      Message.getStatistics(),
      Interaction.getStatistics()
    ]);

    console.log('📈 Statistics results:', {
      clients: clientStats,
      messages: messageStats,
      interactions: interactionStats
    });

    // Calculate rates
    const openRate = interactionStats.sent > 0 ? 
      ((interactionStats.opened / interactionStats.sent) * 100).toFixed(2) : 0;
    const clickRate = interactionStats.sent > 0 ? 
      ((interactionStats.clicked / interactionStats.sent) * 100).toFixed(2) : 0;

    res.json({
      success: true,
      data: {
        clients: clientStats,
        messages: messageStats,
        interactions: interactionStats,
        performance: {
          open_rate: parseFloat(openRate),
          click_rate: parseFloat(clickRate),
          total_sent: interactionStats.sent,
          total_opened: interactionStats.opened,
          total_clicked: interactionStats.clicked
        }
      },
      message: 'Dashboard statistics retrieved successfully',
      message_ar: 'تم استرجاع إحصائيات لوحة التحكم بنجاح'
    });
  } catch (error) {
    console.error('Error fetching dashboard statistics:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch dashboard statistics',
      message_ar: 'فشل في استرجاع إحصائيات لوحة التحكم'
    });
  }
});

// Get email performance report
router.get('/email-performance', async (req, res) => {
  try {
    const { date_from, date_to, message_id } = req.query;
    
    let whereClause = 'WHERE 1=1';
    const params = [];

    if (date_from) {
      whereClause += ' AND i.created_at >= ?';
      params.push(date_from);
    }

    if (date_to) {
      whereClause += ' AND i.created_at <= ?';
      params.push(date_to);
    }

    if (message_id) {
      whereClause += ' AND i.message_id = ?';
      params.push(message_id);
    }

    const query = `
      SELECT 
        m.id as message_id,
        m.title,
        m.language,
        m.created_at as message_created,
        COUNT(CASE WHEN i.type = 'email_sent' THEN 1 END) as sent_count,
        COUNT(CASE WHEN i.opened_at IS NOT NULL THEN 1 END) as opened_count,
        COUNT(CASE WHEN i.clicked_at IS NOT NULL THEN 1 END) as clicked_count,
        COUNT(CASE WHEN i.bounced_at IS NOT NULL THEN 1 END) as bounced_count,
        ROUND(
          (COUNT(CASE WHEN i.opened_at IS NOT NULL THEN 1 END) * 100.0) / 
          NULLIF(COUNT(CASE WHEN i.type = 'email_sent' THEN 1 END), 0), 2
        ) as open_rate,
        ROUND(
          (COUNT(CASE WHEN i.clicked_at IS NOT NULL THEN 1 END) * 100.0) / 
          NULLIF(COUNT(CASE WHEN i.type = 'email_sent' THEN 1 END), 0), 2
        ) as click_rate
      FROM messages m
      LEFT JOIN interactions i ON m.id = i.message_id
      ${whereClause}
      GROUP BY m.id, m.title, m.language, m.created_at
      HAVING sent_count > 0
      ORDER BY m.created_at DESC
    `;

    db.all(query, params, (err, rows) => {
      if (err) {
        console.error('Error fetching email performance:', err);
        return res.status(500).json({
          success: false,
          error: 'Failed to fetch email performance',
          message_ar: 'فشل في استرجاع أداء البريد الإلكتروني'
        });
      }

      res.json({
        success: true,
        data: rows,
        message: 'Email performance report retrieved successfully',
        message_ar: 'تم استرجاع تقرير أداء البريد الإلكتروني بنجاح'
      });
    });
  } catch (error) {
    console.error('Error fetching email performance:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch email performance',
      message_ar: 'فشل في استرجاع أداء البريد الإلكتروني'
    });
  }
});

// Get client engagement report
router.get('/client-engagement', async (req, res) => {
  try {
    const { date_from, date_to, category, language } = req.query;
    
    let whereClause = 'WHERE 1=1';
    const params = [];

    if (date_from) {
      whereClause += ' AND i.created_at >= ?';
      params.push(date_from);
    }

    if (date_to) {
      whereClause += ' AND i.created_at <= ?';
      params.push(date_to);
    }

    if (category) {
      whereClause += ' AND c.category = ?';
      params.push(category);
    }

    if (language) {
      whereClause += ' AND c.language = ?';
      params.push(language);
    }

    const query = `
      SELECT 
        c.id as client_id,
        c.name,
        c.email,
        c.category,
        c.language,
        COUNT(CASE WHEN i.type = 'email_sent' THEN 1 END) as emails_received,
        COUNT(CASE WHEN i.opened_at IS NOT NULL THEN 1 END) as emails_opened,
        COUNT(CASE WHEN i.clicked_at IS NOT NULL THEN 1 END) as emails_clicked,
        MAX(i.opened_at) as last_opened,
        MAX(i.clicked_at) as last_clicked,
        ROUND(
          (COUNT(CASE WHEN i.opened_at IS NOT NULL THEN 1 END) * 100.0) / 
          NULLIF(COUNT(CASE WHEN i.type = 'email_sent' THEN 1 END), 0), 2
        ) as engagement_rate
      FROM clients c
      LEFT JOIN interactions i ON c.id = i.client_id
      ${whereClause}
      GROUP BY c.id, c.name, c.email, c.category, c.language
      HAVING emails_received > 0
      ORDER BY engagement_rate DESC, emails_received DESC
    `;

    db.all(query, params, (err, rows) => {
      if (err) {
        console.error('Error fetching client engagement:', err);
        return res.status(500).json({
          success: false,
          error: 'Failed to fetch client engagement',
          message_ar: 'فشل في استرجاع تفاعل العملاء'
        });
      }

      res.json({
        success: true,
        data: rows,
        message: 'Client engagement report retrieved successfully',
        message_ar: 'تم استرجاع تقرير تفاعل العملاء بنجاح'
      });
    });
  } catch (error) {
    console.error('Error fetching client engagement:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch client engagement',
      message_ar: 'فشل في استرجاع تفاعل العملاء'
    });
  }
});

// Get time-based analytics
router.get('/analytics/timeline', async (req, res) => {
  try {
    const { date_from, date_to, interval = 'day' } = req.query;
    
    let dateFormat;
    switch (interval) {
      case 'hour':
        dateFormat = '%Y-%m-%d %H:00:00';
        break;
      case 'week':
        dateFormat = '%Y-%W';
        break;
      case 'month':
        dateFormat = '%Y-%m';
        break;
      default:
        dateFormat = '%Y-%m-%d';
    }

    let whereClause = 'WHERE 1=1';
    const params = [];

    if (date_from) {
      whereClause += ' AND created_at >= ?';
      params.push(date_from);
    }

    if (date_to) {
      whereClause += ' AND created_at <= ?';
      params.push(date_to);
    }

    const query = `
      SELECT 
        strftime('${dateFormat}', created_at) as period,
        COUNT(CASE WHEN type = 'email_sent' THEN 1 END) as sent,
        COUNT(CASE WHEN opened_at IS NOT NULL THEN 1 END) as opened,
        COUNT(CASE WHEN clicked_at IS NOT NULL THEN 1 END) as clicked,
        COUNT(CASE WHEN bounced_at IS NOT NULL THEN 1 END) as bounced
      FROM interactions
      ${whereClause}
      GROUP BY strftime('${dateFormat}', created_at)
      ORDER BY period ASC
    `;

    db.all(query, params, (err, rows) => {
      if (err) {
        console.error('Error fetching timeline analytics:', err);
        return res.status(500).json({
          success: false,
          error: 'Failed to fetch timeline analytics',
          message_ar: 'فشل في استرجاع التحليلات الزمنية'
        });
      }

      res.json({
        success: true,
        data: rows,
        message: 'Timeline analytics retrieved successfully',
        message_ar: 'تم استرجاع التحليلات الزمنية بنجاح'
      });
    });
  } catch (error) {
    console.error('Error fetching timeline analytics:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch timeline analytics',
      message_ar: 'فشل في استرجاع التحليلات الزمنية'
    });
  }
});

// Get top performing content
router.get('/top-content', async (req, res) => {
  try {
    const { limit = 10, metric = 'open_rate' } = req.query;

    let orderBy;
    switch (metric) {
      case 'click_rate':
        orderBy = 'click_rate DESC';
        break;
      case 'sent_count':
        orderBy = 'sent_count DESC';
        break;
      default:
        orderBy = 'open_rate DESC';
    }

    const query = `
      SELECT 
        m.id,
        m.title,
        m.language,
        m.category,
        m.created_at,
        COUNT(CASE WHEN i.type = 'email_sent' THEN 1 END) as sent_count,
        COUNT(CASE WHEN i.opened_at IS NOT NULL THEN 1 END) as opened_count,
        COUNT(CASE WHEN i.clicked_at IS NOT NULL THEN 1 END) as clicked_count,
        ROUND(
          (COUNT(CASE WHEN i.opened_at IS NOT NULL THEN 1 END) * 100.0) / 
          NULLIF(COUNT(CASE WHEN i.type = 'email_sent' THEN 1 END), 0), 2
        ) as open_rate,
        ROUND(
          (COUNT(CASE WHEN i.clicked_at IS NOT NULL THEN 1 END) * 100.0) / 
          NULLIF(COUNT(CASE WHEN i.type = 'email_sent' THEN 1 END), 0), 2
        ) as click_rate
      FROM messages m
      LEFT JOIN interactions i ON m.id = i.message_id
      GROUP BY m.id, m.title, m.language, m.category, m.created_at
      HAVING sent_count > 0
      ORDER BY ${orderBy}
      LIMIT ?
    `;

    db.all(query, [parseInt(limit)], (err, rows) => {
      if (err) {
        console.error('Error fetching top content:', err);
        return res.status(500).json({
          success: false,
          error: 'Failed to fetch top content',
          message_ar: 'فشل في استرجاع أفضل المحتوى'
        });
      }

      res.json({
        success: true,
        data: rows,
        message: 'Top performing content retrieved successfully',
        message_ar: 'تم استرجاع أفضل المحتوى أداءً بنجاح'
      });
    });
  } catch (error) {
    console.error('Error fetching top content:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch top content',
      message_ar: 'فشل في استرجاع أفضل المحتوى'
    });
  }
});

// Export report data
router.get('/export/:type', async (req, res) => {
  try {
    const { type } = req.params;
    const { format = 'json', date_from, date_to } = req.query;

    let data;
    let filename;

    switch (type) {
      case 'clients':
        const clientResult = await Client.getAll(1, 10000, {
          date_from,
          date_to
        });
        data = clientResult.clients;
        filename = 'clients_export';
        break;

      case 'messages':
        const messageResult = await Message.getAll(1, 10000, {
          date_from,
          date_to
        });
        data = messageResult.messages;
        filename = 'messages_export';
        break;

      case 'interactions':
        const interactionResult = await Interaction.getAll(1, 10000, {
          date_from,
          date_to
        });
        data = interactionResult.interactions;
        filename = 'interactions_export';
        break;

      default:
        return res.status(400).json({
          success: false,
          error: 'Invalid export type',
          message_ar: 'نوع التصدير غير صحيح'
        });
    }

    if (format === 'csv') {
      // Convert to CSV
      if (data.length === 0) {
        return res.status(404).json({
          success: false,
          error: 'No data to export',
          message_ar: 'لا توجد بيانات للتصدير'
        });
      }

      const headers = Object.keys(data[0]).join(',');
      const rows = data.map(row => 
        Object.values(row).map(value => 
          typeof value === 'string' && value.includes(',') ? `"${value}"` : value
        ).join(',')
      );
      
      const csv = [headers, ...rows].join('\n');

      res.setHeader('Content-Type', 'text/csv');
      res.setHeader('Content-Disposition', `attachment; filename="${filename}.csv"`);
      res.send(csv);
    } else {
      // Return JSON
      res.json({
        success: true,
        data,
        message: 'Data exported successfully',
        message_ar: 'تم تصدير البيانات بنجاح'
      });
    }
  } catch (error) {
    console.error('Error exporting data:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to export data',
      message_ar: 'فشل في تصدير البيانات'
    });
  }
});

module.exports = router;
