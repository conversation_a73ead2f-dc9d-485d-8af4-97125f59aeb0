import React from 'react';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { Home, ArrowLeft, Search } from 'lucide-react';

const NotFound = () => {
  const navigate = useNavigate();
  const { t, i18n } = useTranslation();
  
  const isRTL = i18n.language === 'ar';

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full text-center">
        {/* 404 Illustration */}
        <div className="mb-8">
          <div className="text-9xl font-bold text-primary-600 mb-4">
            404
          </div>
          <div className="h-32 w-32 bg-gray-200 rounded-full mx-auto flex items-center justify-center">
            <Search className="h-16 w-16 text-gray-400" />
          </div>
        </div>

        {/* Error Message */}
        <h1 className="text-3xl font-bold text-gray-900 mb-4">
          {isRTL ? 'الصفحة غير موجودة' : 'Page Not Found'}
        </h1>
        
        <p className="text-gray-600 mb-8">
          {isRTL 
            ? 'عذراً، الصفحة التي تبحث عنها غير موجودة أو تم نقلها إلى موقع آخر.'
            : 'Sorry, the page you are looking for doesn\'t exist or has been moved.'
          }
        </p>

        {/* Action Buttons */}
        <div className="space-y-4">
          <button
            onClick={() => navigate('/dashboard')}
            className="w-full btn btn-primary flex items-center justify-center space-x-2 rtl:space-x-reverse"
          >
            <Home className="h-5 w-5" />
            <span>{isRTL ? 'العودة للرئيسية' : 'Go to Dashboard'}</span>
          </button>
          
          <button
            onClick={() => navigate(-1)}
            className="w-full btn btn-outline flex items-center justify-center space-x-2 rtl:space-x-reverse"
          >
            <ArrowLeft className="h-5 w-5" />
            <span>{isRTL ? 'العودة للصفحة السابقة' : 'Go Back'}</span>
          </button>
        </div>

        {/* Help Text */}
        <div className="mt-8 text-sm text-gray-500">
          <p>
            {isRTL 
              ? 'إذا كنت تعتقد أن هذا خطأ، يرجى الاتصال بالدعم الفني.'
              : 'If you think this is an error, please contact support.'
            }
          </p>
        </div>
      </div>
    </div>
  );
};

export default NotFound;
