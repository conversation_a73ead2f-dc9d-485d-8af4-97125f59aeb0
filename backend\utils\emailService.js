const nodemailer = require('nodemailer');
const { db } = require('../config/database');

// Email service providers configuration
const EMAIL_PROVIDERS = {
  // Popular email providers
  gmail: {
    host: 'smtp.gmail.com',
    port: 587,
    secure: false,
    requiresAppPassword: true,
    name: 'Google / Gmail',
    domains: ['gmail.com', 'googlemail.com'],
    category: 'popular'
  },
  outlook: {
    host: 'smtp-mail.outlook.com',
    port: 587,
    secure: false,
    requiresAppPassword: true,
    name: '365 / Outlook',
    domains: ['outlook.com', 'hotmail.com', 'live.com', 'msn.com'],
    category: 'popular'
  },
  yahoo: {
    host: 'smtp.mail.yahoo.com',
    port: 587,
    secure: false,
    requiresAppPassword: true,
    name: 'Yahoo Mail',
    domains: ['yahoo.com', 'yahoo.co.uk', 'yahoo.ca', 'ymail.com'],
    category: 'popular'
  },
  icloud: {
    host: 'smtp.mail.me.com',
    port: 587,
    secure: false,
    requiresAppPassword: true,
    name: 'iCloud Mail',
    domains: ['icloud.com', 'me.com', 'mac.com'],
    category: 'popular'
  },
  zoho: {
    host: 'smtp.zoho.com',
    port: 587,
    secure: false,
    requiresAppPassword: false,
    name: 'Zoho Mail',
    domains: ['zoho.com', 'zohomail.com'],
    category: 'popular'
  },

  // Professional email services
  sendgrid: {
    host: 'smtp.sendgrid.net',
    port: 587,
    secure: false,
    requiresApiKey: true,
    name: 'SendGrid',
    username: 'apikey',
    category: 'professional'
  },
  mailgun: {
    host: 'smtp.mailgun.org',
    port: 587,
    secure: false,
    requiresApiKey: true,
    name: 'Mailgun',
    category: 'professional'
  },
  amazonses: {
    host: 'email-smtp.us-east-1.amazonaws.com',
    port: 587,
    secure: false,
    requiresApiKey: true,
    name: 'Amazon SES',
    category: 'professional'
  },
  mailjet: {
    host: 'in-v3.mailjet.com',
    port: 587,
    secure: false,
    requiresApiKey: true,
    name: 'Mailjet',
    category: 'professional'
  },
  postmark: {
    host: 'smtp.postmarkapp.com',
    port: 587,
    secure: false,
    requiresApiKey: true,
    name: 'Postmark',
    category: 'professional'
  },
  brevo: {
    host: 'smtp-relay.brevo.com',
    port: 587,
    secure: false,
    requiresApiKey: true,
    name: 'Brevo',
    category: 'professional'
  },
  elasticemail: {
    host: 'smtp.elasticemail.com',
    port: 2525,
    secure: false,
    requiresApiKey: true,
    name: 'Elastic Email',
    category: 'professional'
  },
  mailersend: {
    host: 'smtp.mailersend.net',
    port: 587,
    secure: false,
    requiresApiKey: true,
    name: 'MailerSend',
    category: 'professional'
  },
  sparkpost: {
    host: 'smtp.sparkpostmail.com',
    port: 587,
    secure: false,
    requiresApiKey: true,
    name: 'SparkPost',
    category: 'professional'
  },
  smtp2go: {
    host: 'mail.smtp2go.com',
    port: 587,
    secure: false,
    requiresApiKey: true,
    name: 'SMTP2GO',
    category: 'professional'
  },
  smtpcom: {
    host: 'smtp.smtp.com',
    port: 587,
    secure: false,
    requiresApiKey: true,
    name: 'SMTP.com',
    category: 'professional'
  },
  sendlayer: {
    host: 'smtp.sendlayer.com',
    port: 587,
    secure: false,
    requiresApiKey: true,
    name: 'SendLayer',
    category: 'professional'
  },

  // Custom/cPanel
  cpanel: {
    host: 'mail.{domain}',
    port: 587,
    secure: false,
    requiresAppPassword: false,
    name: 'cPanel Email',
    domains: [],
    isCPanel: true,
    category: 'custom'
  },
  other: {
    host: '',
    port: 587,
    secure: false,
    requiresAppPassword: false,
    name: 'Other SMTP',
    category: 'custom'
  },
  aol: {
    host: 'smtp.aol.com',
    port: 587,
    secure: false,
    requiresAppPassword: true,
    name: 'AOL Mail',
    domains: ['aol.com']
  },
  protonmail: {
    host: 'smtp.protonmail.com',
    port: 587,
    secure: false,
    requiresAppPassword: false,
    name: 'ProtonMail',
    domains: ['protonmail.com', 'pm.me']
  }
};

class EmailService {
  constructor() {
    this.transporter = null;
    this.isConfigured = false;
    this.detectedProvider = null;
    this.dbSettings = {};
    this.initializeTransporter();
  }

  // Load SMTP settings from database
  async loadSettingsFromDatabase() {
    return new Promise((resolve) => {
      db.all('SELECT key, value FROM settings WHERE key LIKE "smtp_%"', [], (err, rows) => {
        if (err) {
          console.error('Error loading SMTP settings from database:', err);
          resolve({});
          return;
        }

        const settings = {};
        rows.forEach(row => {
          settings[row.key] = row.value;
        });

        this.dbSettings = settings;
        resolve(settings);
      });
    });
  }

  // Initialize transporter with database settings
  async initializeWithDatabaseSettings() {
    console.log('🔍 Loading SMTP settings from database...');
    const settings = await this.loadSettingsFromDatabase();
    console.log('📧 Database SMTP settings:', Object.keys(settings));

    if (settings.smtp_host && settings.smtp_username && settings.smtp_password) {
      console.log('✅ Found complete SMTP settings in database');
      const config = {
        host: settings.smtp_host,
        port: parseInt(settings.smtp_port) || 587,
        secure: settings.smtp_secure === 'true',
        auth: {
          user: settings.smtp_username,
          pass: settings.smtp_password
        },
        tls: {
          rejectUnauthorized: false
        }
      };

      // Special handling for cPanel emails
      if (settings.smtp_host.includes('mail.') || settings.smtp_host.includes('cpanel') || settings.smtp_host.includes('server.')) {
        config.tls = {
          rejectUnauthorized: false,
          ciphers: 'SSLv3',
          secureProtocol: 'TLSv1_method'
        };

        // Additional cPanel-specific settings
        config.connectionTimeout = 60000; // 60 seconds
        config.greetingTimeout = 30000; // 30 seconds
        config.socketTimeout = 60000; // 60 seconds

        console.log('🔧 Applied cPanel-specific settings:', {
          host: settings.smtp_host,
          port: settings.smtp_port,
          secure: settings.smtp_secure,
          tls: 'relaxed'
        });
      }

      try {
        this.transporter = nodemailer.createTransport(config);

        // Test the connection
        await this.transporter.verify();

        this.isConfigured = true;
        this.detectedProvider = { name: 'Database Settings', key: 'database' };

        console.log('✅ Email service configured from database settings');
        console.log('✅ تم تكوين خدمة البريد الإلكتروني من إعدادات قاعدة البيانات');
        return true;
      } catch (error) {
        console.warn('⚠️  Database SMTP settings failed verification:', error.message);
        this.isConfigured = false;
        this.transporter = null;
        return false;
      }
    } else {
      console.log('❌ Incomplete SMTP settings in database:', {
        has_host: !!settings.smtp_host,
        has_username: !!settings.smtp_username,
        has_password: !!settings.smtp_password
      });
    }

    return false;
  }

  // Create test SMTP settings for development
  async createTestSMTPSettings() {
    try {
      console.log('🧪 Creating test SMTP settings for development...');

      // Check if SMTP settings already exist
      const existingSettings = await this.loadSettingsFromDatabase();
      if (existingSettings.smtp_host) {
        console.log('📧 SMTP settings already exist in database');
        return;
      }

      // Create test SMTP settings using a test email service
      const testSettings = {
        smtp_host: 'smtp.ethereal.email',
        smtp_port: '587',
        smtp_secure: 'false',
        smtp_username: '<EMAIL>',
        smtp_password: 'test123456',
        smtp_from_name: 'Marketing Email System',
        smtp_from_email: '<EMAIL>'
      };

      // Insert test settings into database
      const { db } = require('../config/database');

      for (const [key, value] of Object.entries(testSettings)) {
        await new Promise((resolve, reject) => {
          db.run(
            'INSERT OR REPLACE INTO settings (key, value, description) VALUES (?, ?, ?)',
            [key, value, `Test SMTP setting for ${key}`],
            function(err) {
              if (err) {
                console.error(`Error inserting ${key}:`, err);
                reject(err);
              } else {
                resolve();
              }
            }
          );
        });
      }

      console.log('✅ Test SMTP settings created successfully');
      console.log('✅ تم إنشاء إعدادات SMTP التجريبية بنجاح');

      // Try to initialize with the new settings
      const dbConfigured = await this.initializeWithDatabaseSettings();
      if (dbConfigured) {
        return;
      }

    } catch (error) {
      console.error('Error creating test SMTP settings:', error);
    }
  }

  // Auto-detect email provider from email address
  detectEmailProvider(email) {
    if (!email) return null;

    const domain = email.split('@')[1]?.toLowerCase();
    if (!domain) return null;

    // Check known providers first
    for (const [providerKey, provider] of Object.entries(EMAIL_PROVIDERS)) {
      if (provider.domains && provider.domains.some(d => domain.includes(d))) {
        return { key: providerKey, ...provider };
      }
    }

    // If not a known provider, assume it's cPanel or custom hosting
    return {
      key: 'cpanel',
      host: `mail.${domain}`,
      port: 587,
      secure: false,
      requiresAppPassword: false,
      name: `cPanel (${domain})`,
      isCPanel: true,
      domain: domain
    };
  }

  // Initialize email transporter with auto-detection
  async initializeTransporter() {
    try {
      // First try to load from database
      const dbConfigured = await this.initializeWithDatabaseSettings();
      if (dbConfigured) {
        return;
      }

      // Try to create test SMTP settings if none exist
      await this.createTestSMTPSettings();

      // Fallback to environment variables
      const email = process.env.SMTP_USER;
      const password = process.env.SMTP_PASS;

      if (!email || !password || email.includes('example.com') || password.includes('password-here')) {
        console.log('Email service not configured - please set SMTP_USER and SMTP_PASS in .env file');
        console.log('خدمة البريد الإلكتروني غير مكونة - يرجى تعيين SMTP_USER و SMTP_PASS في ملف .env');
        console.log('Example: SMTP_USER=<EMAIL>');
        console.log('Example: SMTP_PASS=your-app-password');
        return;
      }

      let config;

      // Check if manual SMTP configuration is provided
      if (process.env.SMTP_HOST && process.env.SMTP_PORT) {
        config = {
          host: process.env.SMTP_HOST,
          port: parseInt(process.env.SMTP_PORT),
          secure: process.env.SMTP_SECURE === 'true' || process.env.SMTP_PORT == 465,
          auth: {
            user: email,
            pass: password
          },
          tls: {
            rejectUnauthorized: false
          }
        };
        this.detectedProvider = { name: 'Custom SMTP', key: 'custom' };
        console.log('Using manual SMTP configuration');
        console.log('استخدام تكوين SMTP يدوي');
      } else {
        // Auto-detect provider
        const provider = this.detectEmailProvider(email);

        if (provider) {
          config = {
            host: provider.host,
            port: provider.port,
            secure: provider.secure,
            auth: {
              user: email,
              pass: password
            },
            tls: {
              rejectUnauthorized: false
            }
          };
          this.detectedProvider = provider;
          console.log(`Auto-detected email provider: ${provider.name}`);
          console.log(`تم اكتشاف مزود البريد تلقائياً: ${provider.name}`);

          if (provider.requiresAppPassword) {
            console.log(`Note: ${provider.name} requires App Password, not regular password`);
            console.log(`ملاحظة: ${provider.name} يتطلب كلمة مرور التطبيق وليس كلمة المرور العادية`);
          }
        } else {
          // Fallback to common settings
          config = {
            host: 'smtp.' + email.split('@')[1],
            port: 587,
            secure: false,
            auth: {
              user: email,
              pass: password
            },
            tls: {
              rejectUnauthorized: false
            }
          };
          this.detectedProvider = { name: 'Auto-detected', key: 'auto' };
          console.log('Using auto-detected SMTP settings');
          console.log('استخدام إعدادات SMTP المكتشفة تلقائياً');
        }
      }

      this.transporter = nodemailer.createTransport(config);
      this.isConfigured = true;

      console.log('Email service configured successfully');
      console.log('تم تكوين خدمة البريد الإلكتروني بنجاح');

    } catch (error) {
      console.error('Error configuring email service:', error);
      console.error('خطأ في تكوين خدمة البريد الإلكتروني:', error);
    }
  }

  // Reload configuration (useful after settings update)
  async reloadConfiguration() {
    console.log('🔄 Reloading email configuration...');
    this.isConfigured = false;
    this.transporter = null;
    await this.initializeTransporter();
  }

  // Test email configuration
  async testConnection() {
    if (!this.isConfigured) {
      throw new Error('Email service not configured');
    }

    try {
      await this.transporter.verify();
      return { success: true, message: 'Email configuration is valid' };
    } catch (error) {
      throw new Error(`Email configuration test failed: ${error.message}`);
    }
  }

  // Send single email
  async sendEmail(to, subject, content, options = {}) {
    if (!this.isConfigured) {
      throw new Error('Email service not configured');
    }

    try {
      const mailOptions = {
        from: process.env.SMTP_USER,
        to: to,
        subject: subject,
        html: content,
        ...options
      };

      const result = await this.transporter.sendMail(mailOptions);
      
      return {
        success: true,
        messageId: result.messageId,
        response: result.response
      };
    } catch (error) {
      throw new Error(`Failed to send email: ${error.message}`);
    }
  }

  // Send bulk emails with rate limiting
  async sendBulkEmails(recipients, subject, content, options = {}) {
    if (!this.isConfigured) {
      throw new Error('Email service not configured');
    }

    const results = [];
    const rateLimit = parseInt(process.env.EMAIL_RATE_LIMIT) || 100; // emails per hour
    const delayBetweenEmails = Math.ceil(3600000 / rateLimit); // milliseconds

    for (let i = 0; i < recipients.length; i++) {
      const recipient = recipients[i];
      
      try {
        // Personalize content if needed
        let personalizedContent = content;
        let personalizedSubject = subject;

        if (recipient.name) {
          personalizedContent = content.replace(/\{name\}/g, recipient.name);
          personalizedSubject = subject.replace(/\{name\}/g, recipient.name);
        }

        const result = await this.sendEmail(
          recipient.email,
          personalizedSubject,
          personalizedContent,
          options
        );

        results.push({
          email: recipient.email,
          success: true,
          messageId: result.messageId,
          clientId: recipient.id
        });

        // Rate limiting delay
        if (i < recipients.length - 1) {
          await this.delay(delayBetweenEmails);
        }

      } catch (error) {
        results.push({
          email: recipient.email,
          success: false,
          error: error.message,
          clientId: recipient.id
        });
      }
    }

    return results;
  }

  // Generate email template
  generateEmailTemplate(content, language = 'ar') {
    const isRTL = language === 'ar';
    
    return `
    <!DOCTYPE html>
    <html dir="${isRTL ? 'rtl' : 'ltr'}" lang="${language}">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Email</title>
        <style>
            body {
                font-family: ${isRTL ? 'Tahoma, Arial, sans-serif' : 'Arial, sans-serif'};
                line-height: 1.6;
                color: #333;
                max-width: 600px;
                margin: 0 auto;
                padding: 20px;
                direction: ${isRTL ? 'rtl' : 'ltr'};
            }
            .header {
                background-color: #f8f9fa;
                padding: 20px;
                text-align: center;
                border-radius: 8px 8px 0 0;
            }
            .content {
                background-color: #ffffff;
                padding: 30px;
                border: 1px solid #e9ecef;
            }
            .footer {
                background-color: #f8f9fa;
                padding: 15px;
                text-align: center;
                font-size: 12px;
                color: #6c757d;
                border-radius: 0 0 8px 8px;
            }
            .btn {
                display: inline-block;
                padding: 12px 24px;
                background-color: #007bff;
                color: white;
                text-decoration: none;
                border-radius: 4px;
                margin: 10px 0;
            }
            .btn:hover {
                background-color: #0056b3;
            }
        </style>
    </head>
    <body>
        <div class="header">
            <h2>${isRTL ? 'نظام التسويق الإلكتروني' : 'Email Marketing System'}</h2>
        </div>
        <div class="content">
            ${content}
        </div>
        <div class="footer">
            <p>${isRTL ? 'هذه رسالة تلقائية، يرجى عدم الرد عليها' : 'This is an automated message, please do not reply'}</p>
        </div>
    </body>
    </html>
    `;
  }

  // Add tracking pixels and links
  addTracking(content, clientId, messageId, baseUrl) {
    // Add tracking pixel for open tracking
    const trackingPixel = `<img src="${baseUrl}/api/interactions/track/open/${clientId}/${messageId}" width="1" height="1" style="display:none;" />`;
    
    // Add click tracking to links
    const trackedContent = content.replace(
      /<a\s+href="([^"]+)"([^>]*)>/gi,
      `<a href="${baseUrl}/api/interactions/track/click/${clientId}/${messageId}?url=$1"$2>`
    );

    return trackedContent + trackingPixel;
  }

  // Utility function for delays
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  // Validate email address
  static validateEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  // Get supported email providers
  getSupportedProviders() {
    return Object.entries(EMAIL_PROVIDERS).map(([key, provider]) => ({
      key,
      name: provider.name,
      domains: provider.domains,
      requiresAppPassword: provider.requiresAppPassword,
      setupInstructions: this.getSetupInstructions(key)
    }));
  }

  // Get setup instructions for each provider
  getSetupInstructions(providerKey) {
    const instructions = {
      gmail: {
        ar: [
          '1. اذهب إلى إعدادات حساب Google',
          '2. فعل التحقق بخطوتين',
          '3. أنشئ كلمة مرور التطبيق',
          '4. استخدم كلمة مرور التطبيق في SMTP_PASS'
        ],
        en: [
          '1. Go to Google Account settings',
          '2. Enable 2-Step Verification',
          '3. Generate App Password',
          '4. Use App Password in SMTP_PASS'
        ]
      },
      outlook: {
        ar: [
          '1. اذهب إلى إعدادات الأمان في Microsoft',
          '2. فعل التحقق بخطوتين',
          '3. أنشئ كلمة مرور التطبيق',
          '4. استخدم كلمة مرور التطبيق في SMTP_PASS'
        ],
        en: [
          '1. Go to Microsoft Security settings',
          '2. Enable 2-Step Verification',
          '3. Generate App Password',
          '4. Use App Password in SMTP_PASS'
        ]
      },
      yahoo: {
        ar: [
          '1. اذهب إلى إعدادات الأمان في Yahoo',
          '2. فعل التحقق بخطوتين',
          '3. أنشئ كلمة مرور التطبيق',
          '4. استخدم كلمة مرور التطبيق في SMTP_PASS'
        ],
        en: [
          '1. Go to Yahoo Security settings',
          '2. Enable 2-Step Verification',
          '3. Generate App Password',
          '4. Use App Password in SMTP_PASS'
        ]
      }
    };

    return instructions[providerKey] || {
      ar: ['استخدم بيانات SMTP العادية'],
      en: ['Use regular SMTP credentials']
    };
  }

  // Test configuration for specific provider
  async testProviderConfiguration(email, password, providerKey = null) {
    const provider = providerKey ? EMAIL_PROVIDERS[providerKey] : this.detectEmailProvider(email);

    if (!provider) {
      throw new Error('Unsupported email provider');
    }

    const testTransporter = nodemailer.createTransport({
      host: provider.host,
      port: provider.port,
      secure: provider.secure,
      auth: {
        user: email,
        pass: password
      },
      tls: {
        rejectUnauthorized: false
      }
    });

    try {
      await testTransporter.verify();
      return {
        success: true,
        provider: provider.name,
        message: 'Configuration is valid'
      };
    } catch (error) {
      return {
        success: false,
        provider: provider.name,
        error: error.message,
        suggestions: this.getErrorSuggestions(error.message, provider)
      };
    }
  }

  // Get error suggestions based on error message
  getErrorSuggestions(errorMessage, provider) {
    const suggestions = {
      ar: [],
      en: []
    };

    if (errorMessage.includes('Invalid login') || errorMessage.includes('authentication')) {
      if (provider.requiresAppPassword) {
        suggestions.ar.push('تأكد من استخدام كلمة مرور التطبيق وليس كلمة المرور العادية');
        suggestions.en.push('Make sure to use App Password, not regular password');
      }
      suggestions.ar.push('تحقق من صحة البريد الإلكتروني وكلمة المرور');
      suggestions.en.push('Check email and password credentials');
    }

    if (errorMessage.includes('connection') || errorMessage.includes('timeout')) {
      suggestions.ar.push('تحقق من اتصال الإنترنت');
      suggestions.en.push('Check internet connection');
      suggestions.ar.push('تأكد من عدم حجب منافذ SMTP');
      suggestions.en.push('Ensure SMTP ports are not blocked');
    }

    return suggestions;
  }

  // Get email service status with detailed information
  getStatus() {
    return {
      configured: this.isConfigured,
      provider: this.detectedProvider,
      host: process.env.SMTP_HOST || (this.detectedProvider ? this.detectedProvider.host : null),
      user: process.env.SMTP_USER || null,
      rateLimit: parseInt(process.env.EMAIL_RATE_LIMIT) || 100,
      supportedProviders: this.getSupportedProviders().length,
      autoDetected: !process.env.SMTP_HOST
    };
  }
}

// Create singleton instance
const emailService = new EmailService();

module.exports = emailService;
