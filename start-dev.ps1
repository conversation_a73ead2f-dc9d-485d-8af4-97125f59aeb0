# Email Marketing System - Development Starter
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "  Email Marketing System - Development" -ForegroundColor Cyan  
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

Write-Host "Checking dependencies..." -ForegroundColor Yellow

# Check and install backend dependencies
if (!(Test-Path "backend\node_modules")) {
    Write-Host "Installing backend dependencies..." -ForegroundColor Green
    Set-Location backend
    npm install
    Set-Location ..
}

# Check and install frontend dependencies
if (!(Test-Path "frontend\node_modules")) {
    Write-Host "Installing frontend dependencies..." -ForegroundColor Green
    Set-Location frontend
    npm install
    Set-Location ..
}

Write-Host ""
Write-Host "Starting development servers..." -ForegroundColor Green
Write-Host ""
Write-Host "Backend will run on: http://localhost:5000" -ForegroundColor Blue
Write-Host "Frontend will run on: http://localhost:3000" -ForegroundColor Blue
Write-Host ""
Write-Host "Press Ctrl+C to stop servers" -ForegroundColor Yellow
Write-Host ""

# Start backend in new window
Start-Process powershell -ArgumentList "-NoExit", "-Command", "cd '$PWD\backend'; npm run dev"

# Wait a bit then start frontend
Start-Sleep -Seconds 3
Start-Process powershell -ArgumentList "-NoExit", "-Command", "cd '$PWD\frontend'; npm run dev"

Write-Host "Both servers are starting in separate windows..." -ForegroundColor Green
Write-Host "You can close this window now." -ForegroundColor Gray
Write-Host ""

# Keep window open
Read-Host "Press Enter to exit"
