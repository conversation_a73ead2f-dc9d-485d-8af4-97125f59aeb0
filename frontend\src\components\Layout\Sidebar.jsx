import React from 'react';
import { NavLink, useLocation } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import {
  LayoutDashboard,
  Users,
  Tag,
  FileText,
  MessageSquare,
  BarChart3,
  Settings,
  ChevronLeft,
  ChevronRight,
  Mail
} from 'lucide-react';
import { useTheme } from '../../contexts/ThemeContext';

const Sidebar = () => {
  const { t, i18n } = useTranslation();
  const location = useLocation();
  const { sidebarCollapsed, toggleSidebar } = useTheme();
  
  const isRTL = i18n.language === 'ar';

  const navigationItems = [
    {
      name: t('navigation.dashboard'),
      href: '/dashboard',
      icon: LayoutDashboard,
      current: location.pathname === '/dashboard'
    },
    {
      name: t('navigation.clients'),
      href: '/clients',
      icon: Users,
      current: location.pathname.startsWith('/clients') && !location.pathname.startsWith('/client-categories')
    },
    {
      name: isRTL ? 'فئات العملاء' : 'Client Categories',
      href: '/client-categories',
      icon: Tag,
      current: location.pathname.startsWith('/client-categories')
    },
    {
      name: isRTL ? 'قوالب البريد' : 'Email Templates',
      href: '/email-templates',
      icon: FileText,
      current: location.pathname.startsWith('/email-templates')
    },
    {
      name: t('navigation.messages'),
      href: '/messages',
      icon: MessageSquare,
      current: location.pathname.startsWith('/messages')
    },
    {
      name: t('navigation.reports'),
      href: '/reports',
      icon: BarChart3,
      current: location.pathname.startsWith('/reports')
    },
    {
      name: t('navigation.settings'),
      href: '/settings',
      icon: Settings,
      current: location.pathname.startsWith('/settings')
    },
    {
      name: isRTL ? 'إدارة المستخدمين' : 'User Management',
      href: '/users',
      icon: Users,
      current: location.pathname.startsWith('/users')
    },
    {
      name: isRTL ? 'إعدادات SMTP' : 'SMTP Settings',
      href: '/email-test',
      icon: Mail,
      current: location.pathname.startsWith('/email-test')
    }
  ];

  return (
    <div className={`
      fixed top-0 ${isRTL ? 'right-0' : 'left-0'} h-full bg-white shadow-lg border-r border-gray-200 z-30
      transition-all duration-300 ease-in-out theme-transition
      ${sidebarCollapsed ? 'w-16' : 'w-64'}
    `}>
      {/* Logo Section */}
      <div className="flex items-center justify-between h-16 px-4 border-b border-gray-200">
        {!sidebarCollapsed && (
          <div className="flex items-center space-x-2 rtl:space-x-reverse">
            <div className="h-8 w-8 bg-primary rounded-dynamic flex items-center justify-center">
              <Mail className="h-5 w-5 text-white" />
            </div>
            <div className="flex flex-col">
              <span className="text-sm font-semibold text-gray-900 theme-transition">
                {isRTL ? 'التسويق الإلكتروني' : 'Email Marketing'}
              </span>
              <span className="text-xs text-gray-500 theme-transition">
                {isRTL ? 'نظام الإدارة' : 'Management System'}
              </span>
            </div>
          </div>
        )}
        
        {/* Toggle Button */}
        <button
          onClick={toggleSidebar}
          className="p-1.5 rounded-md text-gray-400 hover:text-gray-600 hover:bg-gray-100 transition-colors"
        >
          {sidebarCollapsed ? (
            isRTL ? <ChevronLeft className="h-5 w-5" /> : <ChevronRight className="h-5 w-5" />
          ) : (
            isRTL ? <ChevronRight className="h-5 w-5" /> : <ChevronLeft className="h-5 w-5" />
          )}
        </button>
      </div>

      {/* Navigation */}
      <nav className="mt-8 px-3">
        <div className="space-y-1">
          {navigationItems.map((item) => {
            const Icon = item.icon;
            return (
              <NavLink
                key={item.name}
                to={item.href}
                className={({ isActive }) => `
                  group flex items-center px-3 py-2 text-sm font-medium rounded-dynamic transition-colors theme-transition
                  ${isActive
                    ? 'bg-primary text-white border-r-2 border-primary'
                    : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                  }
                  ${sidebarCollapsed ? 'justify-center' : ''}
                `}
                title={sidebarCollapsed ? item.name : ''}
              >
                <Icon className={`
                  h-5 w-5 flex-shrink-0
                  ${sidebarCollapsed ? '' : (isRTL ? 'ml-3' : 'mr-3')}
                `} />
                {!sidebarCollapsed && (
                  <span className="truncate">{item.name}</span>
                )}
              </NavLink>
            );
          })}
        </div>
      </nav>

      {/* Collapsed Logo */}
      {sidebarCollapsed && (
        <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2">
          <div className="h-8 w-8 bg-primary-600 rounded-lg flex items-center justify-center">
            <Mail className="h-5 w-5 text-white" />
          </div>
        </div>
      )}

      {/* Version Info */}
      {!sidebarCollapsed && (
        <div className="absolute bottom-4 left-4 right-4">
          <div className="text-xs text-gray-400 text-center">
            <p>{isRTL ? 'الإصدار' : 'Version'} 1.0.0</p>
          </div>
        </div>
      )}
    </div>
  );
};

export default Sidebar;
