const { db } = require('../config/database');
const emailService = require('../utils/emailService');
const Message = require('../models/Message');
const Client = require('../models/Client');

class SchedulerService {
  constructor() {
    this.isRunning = false;
    this.interval = null;
  }

  // بدء خدمة الجدولة
  start() {
    if (this.isRunning) {
      console.log('📅 Scheduler service is already running');
      return;
    }

    console.log('🚀 Starting scheduler service...');
    this.isRunning = true;
    
    // فحص كل دقيقة
    this.interval = setInterval(() => {
      this.processScheduledMessages();
      this.processRecurringMessages();
    }, 60000); // كل دقيقة

    console.log('✅ Scheduler service started successfully');
  }

  // إيقاف خدمة الجدولة
  stop() {
    if (!this.isRunning) {
      console.log('📅 Scheduler service is not running');
      return;
    }

    console.log('🛑 Stopping scheduler service...');
    this.isRunning = false;
    
    if (this.interval) {
      clearInterval(this.interval);
      this.interval = null;
    }

    console.log('✅ Scheduler service stopped successfully');
  }

  // معالجة الرسائل المجدولة
  async processScheduledMessages() {
    try {
      const now = new Date().toISOString();
      
      // البحث عن الرسائل المجدولة التي حان وقت إرسالها
      const scheduledMessages = await new Promise((resolve, reject) => {
        db.all(
          `SELECT * FROM messages 
           WHERE status = 'scheduled' 
           AND send_type = 'scheduled' 
           AND scheduled_at <= ? 
           AND scheduled_at IS NOT NULL`,
          [now],
          (err, rows) => {
            if (err) reject(err);
            else resolve(rows);
          }
        );
      });

      console.log(`📅 Found ${scheduledMessages.length} scheduled messages to process`);

      for (const message of scheduledMessages) {
        await this.sendScheduledMessage(message);
      }

    } catch (error) {
      console.error('❌ Error processing scheduled messages:', error);
    }
  }

  // معالجة الرسائل الدورية
  async processRecurringMessages() {
    try {
      const now = new Date().toISOString();
      
      // البحث عن الرسائل الدورية التي حان وقت إرسالها
      const recurringMessages = await new Promise((resolve, reject) => {
        db.all(
          `SELECT * FROM messages 
           WHERE status = 'recurring' 
           AND send_type = 'recurring' 
           AND is_active = 1
           AND next_send_at <= ? 
           AND next_send_at IS NOT NULL`,
          [now],
          (err, rows) => {
            if (err) reject(err);
            else resolve(rows);
          }
        );
      });

      console.log(`🔄 Found ${recurringMessages.length} recurring messages to process`);

      for (const message of recurringMessages) {
        await this.sendRecurringMessage(message);
      }

    } catch (error) {
      console.error('❌ Error processing recurring messages:', error);
    }
  }

  // إرسال رسالة مجدولة
  async sendScheduledMessage(message) {
    try {
      console.log(`📤 Sending scheduled message: ${message.title}`);

      // تحديث حالة الرسالة إلى "sending"
      await new Promise((resolve, reject) => {
        db.run(
          'UPDATE messages SET status = "sending" WHERE id = ?',
          [message.id],
          (err) => {
            if (err) reject(err);
            else resolve();
          }
        );
      });

      // الحصول على العملاء المستهدفين
      const targetClients = await this.getTargetClients(message);

      // إرسال الرسالة
      const sendResult = await this.sendToClients(message, targetClients);

      // تحديث حالة الرسالة
      const finalStatus = sendResult.success ? 'sent' : 'failed';
      await new Promise((resolve, reject) => {
        db.run(
          'UPDATE messages SET status = ?, sent_at = CURRENT_TIMESTAMP WHERE id = ?',
          [finalStatus, message.id],
          (err) => {
            if (err) reject(err);
            else resolve();
          }
        );
      });

      console.log(`✅ Scheduled message sent successfully: ${message.title}`);

    } catch (error) {
      console.error(`❌ Error sending scheduled message ${message.id}:`, error);
      
      // تحديث حالة الرسالة إلى "failed"
      await new Promise((resolve, reject) => {
        db.run(
          'UPDATE messages SET status = "failed" WHERE id = ?',
          [message.id],
          (err) => {
            if (err) reject(err);
            else resolve();
          }
        );
      });
    }
  }

  // إرسال رسالة دورية
  async sendRecurringMessage(message) {
    try {
      console.log(`🔄 Sending recurring message: ${message.title}`);

      // التحقق من انتهاء الرسالة الدورية
      if (this.shouldStopRecurring(message)) {
        await this.stopRecurringMessage(message);
        return;
      }

      // الحصول على العملاء المستهدفين
      const targetClients = await this.getTargetClients(message);

      // إرسال الرسالة
      const sendResult = await this.sendToClients(message, targetClients);

      // تحديث إحصائيات الرسالة الدورية
      const newCount = (message.recurring_count || 0) + 1;
      const nextSendAt = this.calculateNextSendTime(message);

      await new Promise((resolve, reject) => {
        db.run(
          `UPDATE messages SET 
           recurring_count = ?, 
           last_sent_at = CURRENT_TIMESTAMP,
           next_send_at = ?
           WHERE id = ?`,
          [newCount, nextSendAt, message.id],
          (err) => {
            if (err) reject(err);
            else resolve();
          }
        );
      });

      console.log(`✅ Recurring message sent successfully: ${message.title} (${newCount} times)`);

    } catch (error) {
      console.error(`❌ Error sending recurring message ${message.id}:`, error);
    }
  }

  // التحقق من ضرورة إيقاف الرسالة الدورية
  shouldStopRecurring(message) {
    const now = new Date();

    // التحقق من تاريخ الانتهاء
    if (message.recurring_end_date) {
      const endDate = new Date(message.recurring_end_date);
      if (now >= endDate) {
        console.log(`⏰ Recurring message ${message.id} reached end date`);
        return true;
      }
    }

    // التحقق من العدد الأقصى
    if (message.recurring_max_count) {
      const currentCount = message.recurring_count || 0;
      if (currentCount >= message.recurring_max_count) {
        console.log(`🔢 Recurring message ${message.id} reached max count`);
        return true;
      }
    }

    return false;
  }

  // إيقاف الرسالة الدورية
  async stopRecurringMessage(message) {
    await new Promise((resolve, reject) => {
      db.run(
        'UPDATE messages SET is_active = 0, status = "completed" WHERE id = ?',
        [message.id],
        (err) => {
          if (err) reject(err);
          else resolve();
        }
      );
    });

    console.log(`🛑 Recurring message ${message.id} stopped automatically`);
  }

  // حساب وقت الإرسال التالي
  calculateNextSendTime(message) {
    const now = new Date();
    const nextSend = new Date(now);

    switch (message.recurring_unit) {
      case 'minutes':
        nextSend.setMinutes(now.getMinutes() + message.recurring_interval);
        break;
      case 'hours':
        nextSend.setHours(now.getHours() + message.recurring_interval);
        break;
      case 'days':
        nextSend.setDate(now.getDate() + message.recurring_interval);
        break;
      case 'weeks':
        nextSend.setDate(now.getDate() + (message.recurring_interval * 7));
        break;
      case 'months':
        nextSend.setMonth(now.getMonth() + message.recurring_interval);
        break;
      default:
        nextSend.setDate(now.getDate() + 1); // افتراضي: يوم واحد
    }

    return nextSend.toISOString();
  }

  // الحصول على العملاء المستهدفين
  async getTargetClients(message) {
    try {
      if (message.target_clients) {
        // عملاء محددين
        const clientIds = JSON.parse(message.target_clients);
        const clients = await Client.getByIds(clientIds);
        return clients.filter(client => !client.unsubscribed);
      } else {
        // جميع العملاء النشطين
        const clients = await Client.getAll();
        return clients.filter(client => !client.unsubscribed);
      }
    } catch (error) {
      console.error('Error getting target clients:', error);
      return [];
    }
  }

  // إرسال الرسالة للعملاء
  async sendToClients(message, clients) {
    try {
      // هنا يمكن استخدام emailService لإرسال الرسائل
      // هذا مثال مبسط
      console.log(`📧 Sending message to ${clients.length} clients`);
      
      // محاكاة الإرسال
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      return { success: true, sent: clients.length };
    } catch (error) {
      console.error('Error sending to clients:', error);
      return { success: false, error: error.message };
    }
  }
}

// إنشاء instance واحد من الخدمة
const schedulerService = new SchedulerService();

module.exports = schedulerService;
