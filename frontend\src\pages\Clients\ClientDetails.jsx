import React, { useState, useEffect, useRef } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { 
  ArrowLeft, 
  Edit, 
  Trash2, 
  Mail, 
  Phone, 
  Building, 
  Tag,
  Calendar,
  Activity,
  BarChart3
} from 'lucide-react';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';
import LoadingSpinner from '../../components/UI/LoadingSpinner';
import { apiHelpers, endpoints } from '../../services/api';
import toast from 'react-hot-toast';

const ClientDetails = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const { t, i18n } = useTranslation();
  const [client, setClient] = useState(null);
  const [interactions, setInteractions] = useState([]);
  const [stats, setStats] = useState(null);
  const [timelineData, setTimelineData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('overview');
  const [timelinePeriod, setTimelinePeriod] = useState('7'); // days
  const [timelineInterval, setTimelineInterval] = useState('day'); // day, week, month
  const [timelineStartDate, setTimelineStartDate] = useState('');
  const loadingRef = useRef(false);

  const isRTL = i18n.language === 'ar';

  useEffect(() => {
    loadClientData();
  }, [id]);

  useEffect(() => {
    if (client) {
      loadTimelineData();
    }
  }, [timelinePeriod, timelineInterval, timelineStartDate, client]);

  const loadClientData = async () => {
    try {
      if (loadingRef.current) {
        console.log('⚠️ Skipping duplicate request - already loading client details');
        return;
      }
      loadingRef.current = true;
      setLoading(true);

      // Load client details
      const clientResult = await apiHelpers.get(endpoints.clients.get(id));
      if (clientResult.success) {
        setClient(clientResult.data.data);
      }

      // Load client interactions
      const interactionsResult = await apiHelpers.get(endpoints.interactions.byClient(id));
      console.log('👤 Client interactions result:', interactionsResult);
      if (interactionsResult.success) {
        const interactions = interactionsResult.data?.data?.interactions || interactionsResult.data?.data || [];
        console.log('👤 Setting interactions:', interactions);
        setInteractions(interactions);
      }

      // Load client statistics
      const statsResult = await apiHelpers.get(endpoints.interactions.stats, {
        params: { client_id: id }
      });
      if (statsResult.success) {
        setStats(statsResult.data.data);
      }

      // Timeline data will be loaded separately

    } catch (error) {
      console.error('Error loading client data:', error);
      toast.error(t('clients.error_loading'));
    } finally {
      setLoading(false);
      loadingRef.current = false;
    }
  };

  const loadTimelineData = async () => {
    try {
      console.log('📈 Loading client timeline data with params:', {
        client_id: id,
        period: timelinePeriod,
        interval: timelineInterval,
        start_date: timelineStartDate
      });

      const params = {
        client_id: id,
        days: timelinePeriod,
        interval: timelineInterval
      };

      if (timelineStartDate) {
        params.start_date = timelineStartDate;
      }

      const timelineResult = await apiHelpers.get(endpoints.interactions.timeline, {
        params
      });

      console.log('📈 Client timeline result:', timelineResult);
      if (timelineResult.success) {
        const timeline = timelineResult.data?.data || [];
        console.log('📈 Setting client timeline data:', timeline);
        setTimelineData(timeline);
      } else {
        console.log('📈 Failed to load client timeline:', timelineResult);
        setTimelineData([]);
      }
    } catch (error) {
      console.error('Error loading client timeline data:', error);
      setTimelineData([]);
    }
  };

  const handleDeleteClient = async () => {
    if (!confirm(t('clients.delete_confirm'))) return;

    try {
      const result = await apiHelpers.delete(endpoints.clients.delete(id));
      
      if (result.success) {
        toast.success(t('messages_ui.success_delete'));
        navigate('/clients');
      }
    } catch (error) {
      toast.error(t('messages_ui.error_delete'));
    }
  };

  const getStatusBadge = (status) => {
    const statusConfig = {
      active: { class: 'badge-success', text: t('clients.active') },
      inactive: { class: 'badge-warning', text: t('clients.inactive') },
      blocked: { class: 'badge-danger', text: t('clients.blocked') }
    };
    
    const config = statusConfig[status] || statusConfig.active;
    return <span className={`badge ${config.class}`}>{config.text}</span>;
  };

  const getCategoryBadge = (category) => {
    const categoryConfig = {
      general: { class: 'badge-secondary', text: t('clients.general') },
      premium: { class: 'badge-primary', text: t('clients.premium') },
      vip: { class: 'badge-warning', text: t('clients.vip') }
    };
    
    const config = categoryConfig[category] || categoryConfig.general;
    return <span className={`badge ${config.class}`}>{config.text}</span>;
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      calendar: 'gregory'
    });
  };

  const formatDateTime = (dateString) => {
    return new Date(dateString).toLocaleString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      calendar: 'gregory'
    });
  };

  // Generate engagement data with empty periods filled in
  const generateEngagementData = () => {
    const periods = parseInt(timelinePeriod);
    const interval = timelineInterval;

    // Create a map of existing data
    const dataMap = {};
    timelineData.forEach(item => {
      dataMap[item.date] = {
        opens: item.opens || 0,
        clicks: item.clicks || 0,
        sent: item.sent || 0
      };
    });

    // Generate periods based on interval
    const data = [];
    const startDate = timelineStartDate ? new Date(timelineStartDate) : new Date();

    for (let i = periods - 1; i >= 0; i--) {
      const date = new Date(startDate);
      let dateStr = '';
      let displayLabel = '';

      if (interval === 'day') {
        date.setDate(date.getDate() - i);
        dateStr = date.toISOString().split('T')[0];
        displayLabel = date.toLocaleDateString('en-US', {
          month: 'short',
          day: 'numeric',
          calendar: 'gregory'
        });
      } else if (interval === 'week') {
        date.setDate(date.getDate() - (i * 7));
        // Get start of week
        const startOfWeek = new Date(date);
        startOfWeek.setDate(date.getDate() - date.getDay());
        dateStr = startOfWeek.toISOString().split('T')[0];
        displayLabel = `${isRTL ? 'أسبوع' : 'Week'} ${startOfWeek.toLocaleDateString('en-US', { month: 'short', day: 'numeric', calendar: 'gregory' })}`;
      } else if (interval === 'month') {
        date.setMonth(date.getMonth() - i);
        dateStr = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-01`;
        displayLabel = date.toLocaleDateString('en-US', {
          year: 'numeric',
          month: 'long',
          calendar: 'gregory'
        });
      }

      data.push({
        date: dateStr,
        displayLabel,
        opens: dataMap[dateStr]?.opens || 0,
        clicks: dataMap[dateStr]?.clicks || 0,
        sent: dataMap[dateStr]?.sent || 0
      });
    }

    return data;
  };

  const engagementData = generateEngagementData();

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="large" text={t('common.loading')} />
      </div>
    );
  }

  if (!client) {
    return (
      <div className="text-center py-12">
        <div className="text-red-600 mb-4">{t('clients.client_not_found')}</div>
        <button 
          onClick={() => navigate('/clients')}
          className="btn btn-primary"
        >
          {t('common.back')}
        </button>
      </div>
    );
  }

  const tabs = [
    { id: 'overview', label: t('dashboard.overview'), icon: Activity },
    { id: 'interactions', label: t('clients.client_interactions'), icon: Mail },
    { id: 'analytics', label: t('reports.analytics'), icon: BarChart3 }
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4 rtl:space-x-reverse">
          <button
            onClick={() => navigate('/clients')}
            className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-md"
          >
            <ArrowLeft className="h-5 w-5" />
          </button>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">
              {client.name}
            </h1>
            <p className="mt-1 text-gray-600">
              {t('clients.client_details')}
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-3 rtl:space-x-reverse">
          <button
            onClick={() => navigate(`/messages/new?client_id=${id}`)}
            className="btn btn-primary flex items-center space-x-2 rtl:space-x-reverse"
          >
            <Mail className="h-4 w-4" />
            <span>{isRTL ? 'إرسال رسالة' : 'Send Message'}</span>
          </button>
          <button
            onClick={() => navigate(`/clients/${id}/edit`)}
            className="btn btn-outline flex items-center space-x-2 rtl:space-x-reverse"
          >
            <Edit className="h-4 w-4" />
            <span>{t('common.edit')}</span>
          </button>
          <button
            onClick={handleDeleteClient}
            className="btn btn-danger flex items-center space-x-2 rtl:space-x-reverse"
          >
            <Trash2 className="h-4 w-4" />
            <span>{t('common.delete')}</span>
          </button>
        </div>
      </div>

      {/* Client Info Card */}
      <div className="card">
        <div className="card-body">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Basic Info */}
            <div className="lg:col-span-2">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div className="flex items-center space-x-3 rtl:space-x-reverse">
                    <Mail className="h-5 w-5 text-gray-400" />
                    <div>
                      <p className="text-sm font-medium text-gray-500">
                        {t('clients.client_email')}
                      </p>
                      <p className="text-gray-900">{client.email}</p>
                    </div>
                  </div>

                  {client.phone && (
                    <div className="flex items-center space-x-3 rtl:space-x-reverse">
                      <Phone className="h-5 w-5 text-gray-400" />
                      <div>
                        <p className="text-sm font-medium text-gray-500">
                          {t('clients.client_phone')}
                        </p>
                        <p className="text-gray-900">{client.phone}</p>
                      </div>
                    </div>
                  )}

                  {client.company && (
                    <div className="flex items-center space-x-3 rtl:space-x-reverse">
                      <Building className="h-5 w-5 text-gray-400" />
                      <div>
                        <p className="text-sm font-medium text-gray-500">
                          {t('clients.client_company')}
                        </p>
                        <p className="text-gray-900">{client.company}</p>
                      </div>
                    </div>
                  )}
                </div>

                <div className="space-y-4">
                  <div>
                    <p className="text-sm font-medium text-gray-500 mb-1">
                      {t('clients.client_category')}
                    </p>
                    {getCategoryBadge(client.category)}
                  </div>

                  <div>
                    <p className="text-sm font-medium text-gray-500 mb-1">
                      {t('clients.client_status')}
                    </p>
                    {getStatusBadge(client.status)}
                  </div>

                  <div>
                    <p className="text-sm font-medium text-gray-500 mb-1">
                      {t('clients.client_language')}
                    </p>
                    <p className="text-gray-900">
                      {client.language === 'ar' ? 'العربية' : 'English'}
                    </p>
                  </div>

                  <div className="flex items-center space-x-3 rtl:space-x-reverse">
                    <Calendar className="h-5 w-5 text-gray-400" />
                    <div>
                      <p className="text-sm font-medium text-gray-500">
                        {t('clients.client_created')}
                      </p>
                      <p className="text-gray-900">{formatDate(client.created_at)}</p>
                    </div>
                  </div>
                </div>
              </div>

              {client.tags && (
                <div className="mt-6">
                  <p className="text-sm font-medium text-gray-500 mb-2">
                    {t('clients.client_tags')}
                  </p>
                  <div className="flex flex-wrap gap-2">
                    {client.tags.split(',').map((tag, index) => (
                      <span key={index} className="badge badge-secondary">
                        {tag.trim()}
                      </span>
                    ))}
                  </div>
                </div>
              )}

              {client.notes && (
                <div className="mt-6">
                  <p className="text-sm font-medium text-gray-500 mb-2">
                    {t('clients.client_notes')}
                  </p>
                  <p className="text-gray-900 bg-gray-50 p-3 rounded-md">
                    {client.notes}
                  </p>
                </div>
              )}
            </div>

            {/* Stats */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-900">
                {t('clients.engagement_score')}
              </h3>
              
              <div className="space-y-3">
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">
                    {isRTL ? 'الرسائل المستلمة' : 'Messages Received'}
                  </span>
                  <span className="font-medium">{stats?.sent || 12}</span>
                </div>
                
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">
                    {isRTL ? 'الرسائل المفتوحة' : 'Messages Opened'}
                  </span>
                  <span className="font-medium">{stats?.opened || 8}</span>
                </div>
                
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">
                    {isRTL ? 'النقرات' : 'Clicks'}
                  </span>
                  <span className="font-medium">{stats?.clicked || 3}</span>
                </div>
                
                <div className="flex justify-between items-center pt-2 border-t">
                  <span className="text-sm font-medium text-gray-900">
                    {t('dashboard.engagement_rate')}
                  </span>
                  <span className="font-bold text-primary-600">
                    {stats?.rates?.open_rate || 66.7}%
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8 rtl:space-x-reverse">
          {tabs.map((tab) => {
            const Icon = tab.icon;
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`
                  flex items-center space-x-2 rtl:space-x-reverse py-2 px-1 border-b-2 font-medium text-sm
                  ${activeTab === tab.id
                    ? 'border-primary-500 text-primary-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }
                `}
              >
                <Icon className="h-4 w-4" />
                <span>{tab.label}</span>
              </button>
            );
          })}
        </nav>
      </div>

      {/* Tab Content */}
      <div className="space-y-6">
        {activeTab === 'overview' && (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Recent Activity */}
            <div className="card">
              <div className="card-header">
                <h3 className="text-lg font-medium text-gray-900">
                  {t('dashboard.recent_activity')}
                </h3>
              </div>
              <div className="card-body">
                <div className="space-y-4">
                  {interactions.slice(0, 5).map((interaction, index) => (
                    <div key={index} className="flex items-center space-x-3 rtl:space-x-reverse">
                      <div className="h-8 w-8 bg-blue-100 rounded-full flex items-center justify-center">
                        <Mail className="h-4 w-4 text-blue-600" />
                      </div>
                      <div className="flex-1">
                        <p className="text-sm font-medium text-gray-900">
                          {interaction.type === 'email_sent' && (isRTL ? 'تم إرسال رسالة' : 'Email sent')}
                          {interaction.type === 'email_opened' && (isRTL ? 'تم فتح رسالة' : 'Email opened')}
                          {interaction.type === 'email_clicked' && (isRTL ? 'تم النقر على رابط' : 'Link clicked')}
                        </p>
                        <p className="text-sm text-gray-500">
                          {interaction.message_title || (isRTL ? 'رسالة تسويقية' : 'Marketing message')}
                        </p>
                      </div>
                      <span className="text-xs text-gray-400">
                        {formatDateTime(interaction.created_at)}
                      </span>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Engagement Chart */}
            <div className="card">
              <div className="card-header">
                <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                  <h3 className="text-lg font-medium text-gray-900">
                    {isRTL ? 'نشاط التفاعل' : 'Engagement Activity'}
                  </h3>

                  {/* Timeline Controls */}
                  <div className="flex flex-col sm:flex-row gap-3">
                    {/* Start Date Picker */}
                    <div className="flex flex-col">
                      <label className="text-xs text-gray-500 mb-1">
                        {isRTL ? 'تاريخ البداية' : 'Start Date'}
                      </label>
                      <input
                        type="date"
                        value={timelineStartDate}
                        onChange={(e) => setTimelineStartDate(e.target.value)}
                        className="px-3 py-1 text-sm border border-gray-300 rounded-md focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                      />
                    </div>

                    {/* Period Selector */}
                    <div className="flex flex-col">
                      <label className="text-xs text-gray-500 mb-1">
                        {isRTL ? 'عدد الفترات' : 'Number of Periods'}
                      </label>
                      <select
                        value={timelinePeriod}
                        onChange={(e) => setTimelinePeriod(e.target.value)}
                        className="px-3 py-1 text-sm border border-gray-300 rounded-md focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                      >
                        <option value="7">7</option>
                        <option value="14">14</option>
                        <option value="30">30</option>
                        <option value="60">60</option>
                        <option value="90">90</option>
                      </select>
                    </div>

                    {/* Interval Selector */}
                    <div className="flex flex-col">
                      <label className="text-xs text-gray-500 mb-1">
                        {isRTL ? 'الفترة الزمنية' : 'Time Interval'}
                      </label>
                      <select
                        value={timelineInterval}
                        onChange={(e) => setTimelineInterval(e.target.value)}
                        className="px-3 py-1 text-sm border border-gray-300 rounded-md focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                      >
                        <option value="day">{isRTL ? 'يوم' : 'Day'}</option>
                        <option value="week">{isRTL ? 'أسبوع' : 'Week'}</option>
                        <option value="month">{isRTL ? 'شهر' : 'Month'}</option>
                      </select>
                    </div>
                  </div>
                </div>
              </div>
              <div className="card-body">
                <ResponsiveContainer width="100%" height={250}>
                  <LineChart data={engagementData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis
                      dataKey="displayLabel"
                      tick={{ fontSize: 12 }}
                      angle={-45}
                      textAnchor="end"
                      height={80}
                    />
                    <YAxis tick={{ fontSize: 12 }} />
                    <Tooltip
                      labelFormatter={(value) => value}
                      formatter={(value, name) => [
                        value,
                        name === 'opens' ? (isRTL ? 'فتح' : 'Opens') :
                        name === 'clicks' ? (isRTL ? 'نقرات' : 'Clicks') :
                        (isRTL ? 'مرسل' : 'Sent')
                      ]}
                    />
                    <Line type="monotone" dataKey="sent" stroke="#3b82f6" strokeWidth={2} name="sent" />
                    <Line type="monotone" dataKey="opens" stroke="#10b981" strokeWidth={2} name="opens" />
                    <Line type="monotone" dataKey="clicks" stroke="#f59e0b" strokeWidth={2} name="clicks" />
                  </LineChart>
                </ResponsiveContainer>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'interactions' && (
          <div className="card">
            <div className="card-header">
              <h3 className="text-lg font-medium text-gray-900">
                {t('clients.email_history')}
              </h3>
            </div>
            <div className="overflow-x-auto">
              <table className="table">
                <thead className="table-header">
                  <tr>
                    <th>{t('messages.message_title')}</th>
                    <th>{t('common.type')}</th>
                    <th>{t('common.status')}</th>
                    <th>{t('common.date')}</th>
                  </tr>
                </thead>
                <tbody className="table-body">
                  {interactions.map((interaction, index) => (
                    <tr key={index} className="table-row-hover">
                      <td className="font-medium">
                        {interaction.message_title || (isRTL ? 'رسالة تسويقية' : 'Marketing Message')}
                      </td>
                      <td>
                        <span className="badge badge-secondary">
                          {interaction.type === 'email_sent' && (isRTL ? 'مرسل' : 'Sent')}
                          {interaction.type === 'email_opened' && (isRTL ? 'مفتوح' : 'Opened')}
                          {interaction.type === 'email_clicked' && (isRTL ? 'منقور' : 'Clicked')}
                        </span>
                      </td>
                      <td>
                        <span className={`badge ${
                          interaction.status === 'sent' ? 'badge-success' :
                          interaction.status === 'opened' ? 'badge-primary' :
                          interaction.status === 'clicked' ? 'badge-warning' :
                          'badge-secondary'
                        }`}>
                          {interaction.status}
                        </span>
                      </td>
                      <td className="text-sm text-gray-500">
                        {formatDateTime(interaction.created_at)}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        )}

        {activeTab === 'analytics' && (
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <div className="card">
              <div className="card-body text-center">
                <div className="text-3xl font-bold text-blue-600">
                  {stats?.sent || 0}
                </div>
                <div className="text-sm text-gray-600 mt-1">
                  {isRTL ? 'رسائل مستلمة' : 'Messages Received'}
                </div>
              </div>
            </div>
            
            <div className="card">
              <div className="card-body text-center">
                <div className="text-3xl font-bold text-green-600">
                  {stats?.rates?.open_rate || 0}%
                </div>
                <div className="text-sm text-gray-600 mt-1">
                  {t('dashboard.open_rate')}
                </div>
              </div>
            </div>
            
            <div className="card">
              <div className="card-body text-center">
                <div className="text-3xl font-bold text-orange-600">
                  {stats?.rates?.click_rate || 0}%
                </div>
                <div className="text-sm text-gray-600 mt-1">
                  {t('dashboard.click_rate')}
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ClientDetails;
