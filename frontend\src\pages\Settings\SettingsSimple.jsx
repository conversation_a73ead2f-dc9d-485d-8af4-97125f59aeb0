import React, { useState, useEffect, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Settings as SettingsIcon,
  Mail,
  Globe,
  Shield,
  Bell,
  Save,
  User,
  Palette,
  Eye,
  EyeOff,
  TestTube,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Network,
  Clock,
  Info
} from 'lucide-react';
import { useForm } from 'react-hook-form';
import toast from 'react-hot-toast';
import LoadingSpinner from '../../components/UI/LoadingSpinner';
import { useTheme } from '../../contexts/ThemeContext';
import { apiHelpers, endpoints } from '../../services/api';

const Settings = () => {
  console.log('🔧 OLD SettingsSimple component rendering...');

  // Test all providers on component mount (development only)
  useEffect(() => {
    if (import.meta.env.DEV) {
      console.log('🧪 Testing all email providers...');
      Object.entries(emailProviders).forEach(([key, provider]) => {
        try {
          console.log(`✅ Provider ${key} (${provider.name}):`, provider);
        } catch (error) {
          console.error(`❌ Provider ${key} error:`, error);
        }
      });
    }
  }, []);
  
  const { t, i18n } = useTranslation();
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('general');
  const [showPassword, setShowPassword] = useState(false);
  const [testingEmail, setTestingEmail] = useState(false);
  const [emailTestResult, setEmailTestResult] = useState(null);
  const hasLoadedRef = useRef(false);
  const [diagnosingCPanel, setDiagnosingCPanel] = useState(false);
  const [cPanelDiagnosis, setCPanelDiagnosis] = useState(null);
  
  const { 
    theme, 
    fontSize, 
    setTheme, 
    setFontSize, 
    setLanguage,
    getAvailableThemes,
    getAvailableLanguages
  } = useTheme();

  const isRTL = i18n.language === 'ar';

  // Helper function for info toasts
  const showInfoToast = (message) => {
    toast(message, {
      duration: 4000,
      icon: 'ℹ️',
      style: {
        background: '#3b82f6',
        color: '#ffffff',
        direction: isRTL ? 'rtl' : 'ltr',
        fontFamily: isRTL
          ? "'Tahoma', 'Arial', sans-serif"
          : "'Inter', system-ui, sans-serif"
      }
    });
  };

  const { register, handleSubmit, watch, setValue, formState: { errors } } = useForm({
    defaultValues: {
      // General settings
      default_language: i18n.language,
      theme: theme,
      font_size: fontSize,
      
      // Email settings
      email_provider: 'other',
      smtp_host: '',
      smtp_port: 587,
      smtp_username: '',
      smtp_password: '',
      smtp_secure: false,
      smtp_from_name: '',
      smtp_from_email: '',
      test_email: '',
      
      // Notification settings
      email_notifications: true,
      sound_notifications: false,
      
      // System settings
      auto_save: true,
      max_recipients: 1000
    }
  });

  // Load settings from API
  const loadSettings = async () => {
    // Prevent multiple calls
    if (hasLoadedRef.current) return;
    hasLoadedRef.current = true;

    try {
      setLoading(true);
      
      // Try to load from API first
      try {
        const result = await apiHelpers.get(endpoints.settings.list);
        
        if (result.success) {
          // Set form values from API data
          Object.entries(result.data).forEach(([key, value]) => {
            setValue(key, value);
          });
          return;
        }
      } catch (apiError) {
        console.warn('API settings not available, using localStorage fallback');
      }
      
      // Fallback to localStorage
      const savedSettings = localStorage.getItem('app_settings');
      if (savedSettings) {
        const settings = JSON.parse(savedSettings);
        Object.keys(settings).forEach(key => {
          setValue(key, settings[key]);
        });
      }
    } catch (error) {
      console.error('Error loading settings:', error);
      toast.error(t('settings.error_loading_settings') || 'Error loading settings');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadSettings();
  }, []);

  const saveSettings = async (data) => {
    try {
      setLoading(true);
      
      // Try to save to API first, fallback to localStorage
      try {
        const result = await apiHelpers.put(endpoints.settings.update, data);
        if (!result.success) {
          throw new Error('API save failed');
        }
      } catch (apiError) {
        console.warn('API save failed, using localStorage fallback');
        localStorage.setItem('app_settings', JSON.stringify(data));
      }
      
      // Apply theme changes
      if (data.theme !== theme) {
        setTheme(data.theme);
      }
      
      // Apply font size changes
      if (data.font_size !== fontSize) {
        setFontSize(data.font_size);
      }
      
      // Apply language changes
      if (data.default_language !== i18n.language) {
        setLanguage(data.default_language);
      }
      
      toast.success(t('settings.settings_saved') || 'Settings saved successfully');
    } catch (error) {
      console.error('Error saving settings:', error);
      toast.error(t('settings.settings_error') || 'Error saving settings');
    } finally {
      setLoading(false);
    }
  };

  const testEmailConnection = async () => {
    const formData = watch();

    if (!formData.test_email) {
      toast.error(isRTL ? 'يرجى إدخال بريد إلكتروني للاختبار' : 'Please enter a test email address');
      return;
    }

    if (!formData.smtp_host || !formData.smtp_username || !formData.smtp_password) {
      toast.error(isRTL ? 'يرجى ملء جميع إعدادات SMTP' : 'Please fill all SMTP settings');
      return;
    }

    try {
      setTestingEmail(true);
      setEmailTestResult(null);

      const testData = {
        smtp_host: formData.smtp_host,
        smtp_port: parseInt(formData.smtp_port),
        smtp_username: formData.smtp_username,
        smtp_password: formData.smtp_password,
        smtp_secure: formData.smtp_secure,
        test_email: formData.test_email,
        from_name: formData.smtp_from_name || 'Email Marketing System',
        from_email: formData.smtp_from_email || formData.smtp_username
      };

      // Log test data for debugging
      console.log('🧪 Testing email with settings:', {
        host: testData.smtp_host,
        port: testData.smtp_port,
        secure: testData.smtp_secure,
        username: testData.smtp_username,
        from: testData.from_email,
        to: testData.test_email
      });

      // First save the SMTP settings, then test
      try {
        // Save SMTP settings first
        console.log('💾 Saving SMTP settings...');
        const settingsResult = await apiHelpers.put(endpoints.settings.update, {
          smtp_host: testData.smtp_host,
          smtp_port: testData.smtp_port,
          smtp_username: testData.smtp_username,
          smtp_password: testData.smtp_password,
          smtp_secure: testData.smtp_secure,
          smtp_from_name: testData.from_name,
          smtp_from_email: testData.from_email
        });

        console.log('✅ Settings saved, sending test email...');

        // Then send test email
        const testResult = await apiHelpers.post('/email-config/test-send', {
          testEmail: testData.test_email,
          subject: isRTL ? 'اختبار البريد الإلكتروني' : 'Email Test',
          message: isRTL
            ? 'هذه رسالة اختبار من نظام إدارة التسويق الإلكتروني. إذا وصلتك هذه الرسالة، فإن الإعدادات تعمل بشكل صحيح!'
            : 'This is a test email from the Email Marketing Management System. If you received this message, your settings are working correctly!'
        });

        if (testResult.success) {
          console.log('✅ Test email sent successfully');
          setEmailTestResult({ success: true, message: testResult.data.message });
          toast.success(isRTL ? 'تم إرسال البريد الاختباري بنجاح' : 'Test email sent successfully');
        } else {
          throw new Error(testResult.error || 'Test failed');
        }
      } catch (apiError) {
        console.error('❌ Email test failed:', apiError);
        const errorMessage = apiError.response?.data?.error || apiError.message || 'Test failed';
        setEmailTestResult({
          success: false,
          message: errorMessage
        });

        // Provide detailed suggestions based on error
        let suggestion = '';
        let detailedHelp = '';

        if (errorMessage.includes('ECONNREFUSED') || errorMessage.includes('connection refused')) {
          suggestion = isRTL
            ? 'لا يمكن الاتصال بالخادم'
            : 'Cannot connect to server';
          detailedHelp = isRTL
            ? 'جرب: 1) تغيير المنفذ إلى 465 مع تفعيل SSL، 2) تحقق من عنوان الخادم، 3) تأكد من أن مزود الاستضافة يسمح بـ SMTP'
            : 'Try: 1) Change port to 465 with SSL enabled, 2) Check server address, 3) Ensure hosting provider allows SMTP';
        } else if (errorMessage.includes('authentication') || errorMessage.includes('login') || errorMessage.includes('535')) {
          suggestion = isRTL
            ? 'خطأ في المصادقة'
            : 'Authentication failed';
          detailedHelp = isRTL
            ? 'تحقق من: 1) البريد الإلكتروني صحيح، 2) كلمة المرور صحيحة، 3) الحساب مفعل في cPanel'
            : 'Check: 1) Email address is correct, 2) Password is correct, 3) Account is active in cPanel';
        } else if (errorMessage.includes('timeout')) {
          suggestion = isRTL
            ? 'انتهت مهلة الاتصال'
            : 'Connection timeout';
          detailedHelp = isRTL
            ? 'قد يكون: 1) الخادم بطيء، 2) جدار حماية يحجب الاتصال، 3) إعدادات الشبكة'
            : 'Could be: 1) Slow server, 2) Firewall blocking connection, 3) Network settings';
        } else if (errorMessage.includes('certificate') || errorMessage.includes('SSL') || errorMessage.includes('TLS')) {
          suggestion = isRTL
            ? 'مشكلة في شهادة SSL'
            : 'SSL certificate issue';
          detailedHelp = isRTL
            ? 'جرب: 1) إلغاء تفعيل SSL للمنفذ 587، 2) تفعيل SSL للمنفذ 465'
            : 'Try: 1) Disable SSL for port 587, 2) Enable SSL for port 465';
        }

        console.error('💡 Suggestion:', suggestion);
        console.error('🔧 Detailed help:', detailedHelp);

        toast.error(
          (isRTL ? 'فشل في إرسال البريد الاختباري' : 'Failed to send test email') +
          (suggestion ? ': ' + suggestion : '')
        );
      }
    } catch (error) {
      console.error('Email test error:', error);
      setEmailTestResult({ success: false, message: error.message });
      toast.error(isRTL ? 'خطأ في اختبار البريد الإلكتروني' : 'Email test error');
    } finally {
      setTestingEmail(false);
    }
  };

  const diagnoseCPanelSettings = async () => {
    const formData = watch();

    console.log('🔍 Form data:', formData);

    if (!formData.smtp_username || !formData.smtp_password) {
      toast.error(isRTL ? 'يرجى إدخال البريد الإلكتروني وكلمة المرور' : 'Please enter email and password');
      return;
    }

    if (!formData.smtp_username.includes('@')) {
      toast.error(isRTL ? 'يرجى إدخال بريد إلكتروني صحيح' : 'Please enter a valid email address');
      return;
    }

    try {
      setDiagnosingCPanel(true);
      setCPanelDiagnosis(null);

      const domain = formData.smtp_username.split('@')[1];

      const requestData = {
        domain: domain,
        email: formData.smtp_username,
        password: formData.smtp_password
      };

      console.log('🔍 Diagnosing cPanel settings:', {
        domain: domain,
        email: formData.smtp_username,
        passwordLength: formData.smtp_password.length
      });

      console.log('📤 Sending request to /email-config/diagnose-cpanel');

      const result = await apiHelpers.post('/email-config/diagnose-cpanel', requestData);

      console.log('📥 Received response:', result);

      if (result.success && result.data) {
        setCPanelDiagnosis(result.data);

        console.log('✅ Diagnosis data:', result.data);

        if (result.data.recommendation && result.data.recommendation.settings) {
          const config = result.data.recommendation.settings;
          setValue('smtp_host', config.host || '');
          setValue('smtp_port', config.port || 587);
          setValue('smtp_secure', config.secure || false);

          showInfoToast(
            isRTL
              ? `تم العثور على إعدادات تعمل: ${result.data.recommendation.config || 'إعدادات مناسبة'}`
              : `Found working settings: ${result.data.recommendation.config || 'Working configuration'}`
          );
        } else {
          console.log('❌ No working configuration found');
          toast.error(
            isRTL
              ? 'لم يتم العثور على إعدادات تعمل. تحقق من البيانات أو اتصل بمزود الاستضافة'
              : 'No working settings found. Check credentials or contact hosting provider'
          );
        }
      } else {
        console.error('❌ API response error:', result);
        toast.error(
          isRTL
            ? 'فشل في التشخيص. حاول مرة أخرى'
            : 'Diagnosis failed. Please try again'
        );
      }
    } catch (error) {
      console.error('❌ cPanel diagnosis error:', error);
      console.error('Error details:', {
        message: error.message,
        response: error.response?.data,
        status: error.response?.status
      });

      toast.error(
        isRTL
          ? `خطأ في تشخيص إعدادات cPanel: ${error.response?.data?.error || error.message}`
          : `Error diagnosing cPanel settings: ${error.response?.data?.error || error.message}`
      );
    } finally {
      setDiagnosingCPanel(false);
    }
  };

  // Email providers configuration
  const emailProviders = {
    // Popular providers
    gmail: {
      name: 'Google / Gmail',
      host: 'smtp.gmail.com',
      port: 587,
      secure: false,
      requiresAppPassword: true,
      category: 'popular'
    },
    outlook: {
      name: '365 / Outlook',
      host: 'smtp-mail.outlook.com',
      port: 587,
      secure: false,
      requiresAppPassword: true,
      category: 'popular'
    },
    yahoo: {
      name: 'Yahoo Mail',
      host: 'smtp.mail.yahoo.com',
      port: 587,
      secure: false,
      requiresAppPassword: true,
      category: 'popular'
    },
    zoho: {
      name: 'Zoho Mail',
      host: 'smtp.zoho.com',
      port: 587,
      secure: false,
      requiresAppPassword: false,
      category: 'popular'
    },
    icloud: {
      name: 'iCloud Mail',
      host: 'smtp.mail.me.com',
      port: 587,
      secure: false,
      requiresAppPassword: true,
      category: 'popular'
    },

    // Professional services
    sendgrid: {
      name: 'SendGrid',
      host: 'smtp.sendgrid.net',
      port: 587,
      secure: false,
      requiresApiKey: true,
      username: 'apikey',
      category: 'professional'
    },
    mailgun: {
      name: 'Mailgun',
      host: 'smtp.mailgun.org',
      port: 587,
      secure: false,
      requiresApiKey: true,
      category: 'professional'
    },
    amazonses: {
      name: 'Amazon SES',
      host: 'email-smtp.us-east-1.amazonaws.com',
      port: 587,
      secure: false,
      requiresApiKey: true,
      category: 'professional'
    },
    mailjet: {
      name: 'Mailjet',
      host: 'in-v3.mailjet.com',
      port: 587,
      secure: false,
      requiresApiKey: true,
      category: 'professional'
    },
    postmark: {
      name: 'Postmark',
      host: 'smtp.postmarkapp.com',
      port: 587,
      secure: false,
      requiresApiKey: true,
      category: 'professional'
    },
    brevo: {
      name: 'Brevo',
      host: 'smtp-relay.brevo.com',
      port: 587,
      secure: false,
      requiresApiKey: true,
      category: 'professional'
    },
    elasticemail: {
      name: 'Elastic Email',
      host: 'smtp.elasticemail.com',
      port: 2525,
      secure: false,
      requiresApiKey: true,
      category: 'professional'
    },
    mailersend: {
      name: 'MailerSend',
      host: 'smtp.mailersend.net',
      port: 587,
      secure: false,
      requiresApiKey: true,
      category: 'professional'
    },
    sparkpost: {
      name: 'SparkPost',
      host: 'smtp.sparkpostmail.com',
      port: 587,
      secure: false,
      requiresApiKey: true,
      category: 'professional'
    },
    smtp2go: {
      name: 'SMTP2GO',
      host: 'mail.smtp2go.com',
      port: 587,
      secure: false,
      requiresApiKey: true,
      category: 'professional'
    },
    smtpcom: {
      name: 'SMTP.com',
      host: 'smtp.smtp.com',
      port: 587,
      secure: false,
      requiresApiKey: true,
      category: 'professional'
    },
    sendlayer: {
      name: 'SendLayer',
      host: 'smtp.sendlayer.com',
      port: 587,
      secure: false,
      requiresApiKey: true,
      category: 'professional'
    },

    // Custom options
    cpanel_587: {
      name: 'cPanel Email (Port 587)',
      host: '',
      port: 587,
      secure: false,
      requiresAppPassword: false,
      isCPanel: true,
      category: 'custom'
    },
    cpanel_465: {
      name: 'cPanel Email (Port 465 SSL)',
      host: '',
      port: 465,
      secure: true,
      requiresAppPassword: false,
      isCPanel: true,
      category: 'custom'
    },
    cpanel_25: {
      name: 'cPanel Email (Port 25)',
      host: '',
      port: 25,
      secure: false,
      requiresAppPassword: false,
      isCPanel: true,
      category: 'custom'
    },
    other: {
      name: 'Other SMTP',
      host: '',
      port: 587,
      secure: false,
      requiresAppPassword: false,
      category: 'custom'
    }
  };

  const detectEmailProvider = (email) => {
    if (!email || !email.includes('@')) return null;

    const domain = email.split('@')[1].toLowerCase();

    const domainProviders = {
      'gmail.com': 'gmail',
      'googlemail.com': 'gmail',
      'outlook.com': 'outlook',
      'hotmail.com': 'outlook',
      'live.com': 'outlook',
      'msn.com': 'outlook',
      'yahoo.com': 'yahoo',
      'yahoo.co.uk': 'yahoo',
      'yahoo.ca': 'yahoo',
      'ymail.com': 'yahoo',
      'icloud.com': 'icloud',
      'me.com': 'icloud',
      'mac.com': 'icloud',
      'zoho.com': 'zoho',
      'zohomail.com': 'zoho'
    };

    return domainProviders[domain] || 'cpanel_587';
  };

  const watchedEmail = watch('smtp_username');
  const watchedProvider = watch('email_provider');

  // Apply provider settings when provider changes
  const applyProviderSettings = (providerKey, email = null) => {
    try {
      const provider = emailProviders[providerKey];
      if (!provider) {
        console.warn('Provider not found:', providerKey);
        return;
      }

      // Apply basic settings
      setValue('smtp_host', provider.host || '');
      setValue('smtp_port', provider.port || 587);
      setValue('smtp_secure', provider.secure || false);

      // Special handling for different provider types
      if (provider.isCPanel && email && email.includes('@')) {
        const domain = email.split('@')[1];
        if (domain) {
          setValue('smtp_host', `mail.${domain}`);
        }
      }

      if (provider.requiresApiKey && provider.username) {
        setValue('smtp_username', provider.username);
      }

      // Show helpful messages with delay to avoid conflicts
      setTimeout(() => {
        if (provider.requiresApiKey) {
          showInfoToast(
            isRTL
              ? `${provider.name} يتطلب API Key بدلاً من كلمة المرور العادية`
              : `${provider.name} requires API Key instead of regular password`
          );
        } else if (provider.requiresAppPassword) {
          showInfoToast(
            isRTL
              ? `${provider.name} يتطلب كلمة مرور التطبيق`
              : `${provider.name} requires App Password`
          );
        }
      }, 100);

    } catch (error) {
      console.error('Error in applyProviderSettings:', error);
    }
  };

  // Auto-detect provider from email
  useEffect(() => {
    try {
      if (watchedEmail && watchedEmail.includes('@')) {
        const detectedProvider = detectEmailProvider(watchedEmail);
        if (detectedProvider && detectedProvider !== watchedProvider) {
          setValue('email_provider', detectedProvider);
          applyProviderSettings(detectedProvider, watchedEmail);
          setValue('smtp_from_email', watchedEmail);
        }
      }
    } catch (error) {
      console.error('Error in email provider detection:', error);
    }
  }, [watchedEmail, setValue, isRTL]);

  // Apply settings when provider is manually changed
  useEffect(() => {
    if (watchedProvider) {
      try {
        applyProviderSettings(watchedProvider, watchedEmail);
      } catch (error) {
        console.error('Error applying provider settings:', error);
        toast.error(isRTL ? 'خطأ في تطبيق إعدادات المزود' : 'Error applying provider settings');
      }
    }
  }, [watchedProvider, setValue, isRTL]);

  const tabs = [
    {
      id: 'general',
      name: isRTL ? 'عام' : 'General',
      icon: SettingsIcon
    },
    {
      id: 'email',
      name: isRTL ? 'البريد الإلكتروني' : 'Email',
      icon: Mail
    },
    {
      id: 'notifications',
      name: isRTL ? 'الإشعارات' : 'Notifications',
      icon: Bell
    },
    {
      id: 'appearance',
      name: isRTL ? 'المظهر' : 'Appearance',
      icon: Palette
    }
  ];

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <LoadingSpinner />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            {t('settings.title') || (isRTL ? 'الإعدادات' : 'Settings')}
          </h1>
          <p className="text-gray-600">
            {isRTL 
              ? 'إدارة إعدادات النظام والتفضيلات الشخصية'
              : 'Manage system settings and personal preferences'
            }
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* Sidebar */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-lg shadow-sm p-4">
              <nav className="space-y-2">
                {tabs.map((tab) => {
                  const Icon = tab.icon;
                  return (
                    <button
                      key={tab.id}
                      onClick={() => setActiveTab(tab.id)}
                      className={`w-full flex items-center space-x-3 rtl:space-x-reverse px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                        activeTab === tab.id
                          ? 'bg-primary-100 text-primary-700'
                          : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                      }`}
                    >
                      <Icon className="h-5 w-5" />
                      <span>{tab.name}</span>
                    </button>
                  );
                })}
              </nav>
            </div>
          </div>

          {/* Content */}
          <div className="lg:col-span-3">
            <div className="bg-white rounded-lg shadow-sm p-6">
              <form onSubmit={handleSubmit(saveSettings)} className="space-y-6">
                {/* General Tab */}
                {activeTab === 'general' && (
                  <div className="space-y-6">
                    <h2 className="text-xl font-semibold text-gray-900">
                      {isRTL ? 'الإعدادات العامة' : 'General Settings'}
                    </h2>
                    
                    {/* Language */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        {isRTL ? 'اللغة الافتراضية' : 'Default Language'}
                      </label>
                      <select
                        {...register('default_language')}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                      >
                        {getAvailableLanguages().map(lang => (
                          <option key={lang.code} value={lang.code}>
                            {lang.flag} {lang.label}
                          </option>
                        ))}
                      </select>
                    </div>

                    {/* Max Recipients */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        {isRTL ? 'الحد الأقصى للمستلمين' : 'Max Recipients per Campaign'}
                      </label>
                      <input
                        type="number"
                        {...register('max_recipients', { min: 1, max: 10000 })}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                      />
                    </div>
                  </div>
                )}

                {/* Email Tab */}
                {activeTab === 'email' && (
                  <div className="space-y-6">
                    <div className="flex items-center justify-between">
                      <h2 className="text-xl font-semibold text-gray-900">
                        {isRTL ? 'إعدادات البريد الإلكتروني' : 'Email Settings'}
                      </h2>

                      {emailTestResult && (
                        <div className={`flex items-center space-x-2 rtl:space-x-reverse px-3 py-1 rounded-md text-sm ${
                          emailTestResult.success
                            ? 'bg-green-100 text-green-800'
                            : 'bg-red-100 text-red-800'
                        }`}>
                          {emailTestResult.success ? (
                            <CheckCircle className="h-4 w-4" />
                          ) : (
                            <XCircle className="h-4 w-4" />
                          )}
                          <span>
                            {emailTestResult.success
                              ? (isRTL ? 'تم الاختبار بنجاح' : 'Test Successful')
                              : (isRTL ? 'فشل الاختبار' : 'Test Failed')
                            }
                          </span>
                        </div>
                      )}
                    </div>

                    <div className="space-y-4">
                      <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-md">
                        <div className="flex items-start space-x-3 rtl:space-x-reverse">
                          <AlertTriangle className="h-5 w-5 text-yellow-600 mt-0.5" />
                          <div>
                            <h3 className="font-medium text-yellow-800">
                              {isRTL ? 'ملاحظات مهمة' : 'Important Notes'}
                            </h3>
                            <ul className="text-sm text-yellow-700 mt-1 space-y-1">
                              <li>
                                <strong>Gmail/Outlook:</strong> {' '}
                                {isRTL
                                  ? 'يتطلب "كلمة مرور التطبيق" من إعدادات الأمان'
                                  : 'Requires "App Password" from security settings'
                                }
                              </li>
                              <li>
                                <strong>cPanel:</strong> {' '}
                                {isRTL
                                  ? 'استخدم كلمة مرور البريد الإلكتروني العادية. الخادم عادة: mail.yourdomain.com'
                                  : 'Use regular email password. Server usually: mail.yourdomain.com'
                                }
                              </li>
                            </ul>
                          </div>
                        </div>
                      </div>

                      {/* Provider-specific help */}
                      {watchedProvider && emailProviders[watchedProvider] && (
                        <div className="p-4 bg-blue-50 border border-blue-200 rounded-md">
                          <div className="flex items-start space-x-3 rtl:space-x-reverse">
                            <Mail className="h-5 w-5 text-blue-600 mt-0.5" />
                            <div>
                              <h3 className="font-medium text-blue-800">
                                {emailProviders[watchedProvider].name} {isRTL ? 'إعدادات' : 'Settings'}
                              </h3>
                              <div className="text-sm text-blue-700 mt-1 space-y-1">
                                {emailProviders[watchedProvider].requiresApiKey && (
                                  <>
                                    <p><strong>{isRTL ? 'نوع المصادقة:' : 'Authentication:'}</strong> API Key</p>
                                    <p><strong>{isRTL ? 'اسم المستخدم:' : 'Username:'}</strong> {emailProviders[watchedProvider].username || 'API Username'}</p>
                                    <p><strong>{isRTL ? 'كلمة المرور:' : 'Password:'}</strong> {isRTL ? 'API Key الخاص بك' : 'Your API Key'}</p>
                                  </>
                                )}
                                {emailProviders[watchedProvider].requiresAppPassword && (
                                  <>
                                    <p><strong>{isRTL ? 'نوع المصادقة:' : 'Authentication:'}</strong> {isRTL ? 'كلمة مرور التطبيق' : 'App Password'}</p>
                                    <p><strong>{isRTL ? 'ملاحظة:' : 'Note:'}</strong> {isRTL ? 'يجب تفعيل التحقق بخطوتين أولاً' : 'Must enable 2-factor authentication first'}</p>
                                  </>
                                )}
                                {emailProviders[watchedProvider].isCPanel && (
                                  <>
                                    <p><strong>{isRTL ? 'نوع المصادقة:' : 'Authentication:'}</strong> {isRTL ? 'كلمة مرور عادية' : 'Regular password'}</p>
                                    <p><strong>{isRTL ? 'الخادم المقترح:' : 'Suggested server:'}</strong> mail.yourdomain.com</p>
                                    <p><strong>{isRTL ? 'خيارات أخرى:' : 'Other options:'}</strong> server.yourdomain.com, smtp.yourdomain.com</p>
                                    <p><strong>{isRTL ? 'نصائح:' : 'Tips:'}</strong></p>
                                    <ul className="list-disc list-inside ml-4 space-y-1">
                                      <li>{isRTL ? 'جرب منفذ 587 أولاً بدون SSL' : 'Try port 587 first without SSL'}</li>
                                      <li>{isRTL ? 'إذا فشل، جرب منفذ 465 مع SSL' : 'If failed, try port 465 with SSL'}</li>
                                      <li>{isRTL ? 'تأكد من تفعيل SMTP في cPanel' : 'Ensure SMTP is enabled in cPanel'}</li>
                                      <li>{isRTL ? 'تحقق من حدود الإرسال اليومية' : 'Check daily sending limits'}</li>
                                    </ul>
                                  </>
                                )}
                                <p><strong>{isRTL ? 'المنفذ:' : 'Port:'}</strong> {emailProviders[watchedProvider].port}</p>
                                <p><strong>{isRTL ? 'التشفير:' : 'Encryption:'}</strong> {emailProviders[watchedProvider].secure ? 'SSL' : 'TLS/STARTTLS'}</p>
                              </div>
                            </div>
                          </div>
                        </div>
                      )}

                      {/* cPanel Troubleshooting */}
                      {watchedProvider && emailProviders[watchedProvider]?.isCPanel && (
                        <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-md">
                          <div className="flex items-start space-x-3 rtl:space-x-reverse">
                            <AlertTriangle className="h-5 w-5 text-yellow-600 mt-0.5" />
                            <div>
                              <h3 className="font-medium text-yellow-800">
                                {isRTL ? 'استكشاف أخطاء cPanel' : 'cPanel Troubleshooting'}
                              </h3>
                              <div className="text-sm text-yellow-700 mt-2">
                                <p className="font-medium mb-2">{isRTL ? 'إذا فشل الاختبار، جرب هذه الحلول:' : 'If test fails, try these solutions:'}</p>
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                  <div>
                                    <p className="font-medium">{isRTL ? 'مشاكل الاتصال:' : 'Connection Issues:'}</p>
                                    <ul className="list-disc list-inside space-y-1 text-xs">
                                      <li>{isRTL ? 'غير المنفذ إلى 465 + SSL' : 'Change port to 465 + SSL'}</li>
                                      <li>{isRTL ? 'جرب server.yourdomain.com' : 'Try server.yourdomain.com'}</li>
                                      <li>{isRTL ? 'تحقق من جدار الحماية' : 'Check firewall settings'}</li>
                                    </ul>
                                  </div>
                                  <div>
                                    <p className="font-medium">{isRTL ? 'مشاكل المصادقة:' : 'Authentication Issues:'}</p>
                                    <ul className="list-disc list-inside space-y-1 text-xs">
                                      <li>{isRTL ? 'تأكد من البريد الإلكتروني الكامل' : 'Use full email address'}</li>
                                      <li>{isRTL ? 'تحقق من كلمة المرور في cPanel' : 'Verify password in cPanel'}</li>
                                      <li>{isRTL ? 'تأكد من تفعيل الحساب' : 'Ensure account is active'}</li>
                                    </ul>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      )}
                    </div>

                    {/* Email Provider Selection */}
                    <div className="mb-6">
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        {isRTL ? 'مزود البريد الإلكتروني' : 'Email Provider'}
                      </label>
                      <select
                        {...register('email_provider')}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                      >
                        <option value="">{isRTL ? 'اختر مزود البريد الإلكتروني' : 'Select Email Provider'}</option>

                        <optgroup label={isRTL ? 'المزودون الشائعون' : 'Popular Providers'}>
                          {Object.entries(emailProviders)
                            .filter(([key, provider]) => provider.category === 'popular')
                            .map(([key, provider]) => (
                              <option key={key} value={key}>{provider.name}</option>
                            ))}
                        </optgroup>

                        <optgroup label={isRTL ? 'الخدمات المهنية' : 'Professional Services'}>
                          {Object.entries(emailProviders)
                            .filter(([key, provider]) => provider.category === 'professional')
                            .map(([key, provider]) => (
                              <option key={key} value={key}>{provider.name}</option>
                            ))}
                        </optgroup>

                        <optgroup label={isRTL ? 'خيارات مخصصة' : 'Custom Options'}>
                          {Object.entries(emailProviders)
                            .filter(([key, provider]) => provider.category === 'custom')
                            .map(([key, provider]) => (
                              <option key={key} value={key}>{provider.name}</option>
                            ))}
                        </optgroup>
                      </select>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      {/* SMTP Host */}
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          {isRTL ? 'خادم SMTP' : 'SMTP Host'}
                        </label>
                        <input
                          type="text"
                          {...register('smtp_host', { required: true })}
                          placeholder="smtp.gmail.com"
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                          readOnly={watchedProvider && emailProviders[watchedProvider] && !emailProviders[watchedProvider].isCPanel && watchedProvider !== 'other'}
                        />
                        {errors.smtp_host && (
                          <p className="text-red-500 text-sm mt-1">
                            {isRTL ? 'خادم SMTP مطلوب' : 'SMTP Host is required'}
                          </p>
                        )}
                      </div>

                      {/* SMTP Port */}
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          {isRTL ? 'منفذ SMTP' : 'SMTP Port'}
                        </label>
                        <select
                          {...register('smtp_port', { required: true })}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                        >
                          <option value="587">587 (TLS)</option>
                          <option value="465">465 (SSL)</option>
                          <option value="25">25 (Plain)</option>
                          <option value="2525">2525 (Alternative)</option>
                        </select>
                        {errors.smtp_port && (
                          <p className="text-red-500 text-sm mt-1">
                            {isRTL ? 'منفذ SMTP مطلوب' : 'SMTP Port is required'}
                          </p>
                        )}
                        <p className="text-xs text-gray-500 mt-1">
                          {isRTL
                            ? 'للـ cPanel: جرب 587 أولاً، ثم 465 إذا لم يعمل'
                            : 'For cPanel: Try 587 first, then 465 if it doesn\'t work'
                          }
                        </p>
                      </div>

                      {/* Username/Email */}
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          {isRTL ? 'البريد الإلكتروني' : 'Email Address'}
                        </label>
                        <input
                          type="email"
                          {...register('smtp_username', { required: true })}
                          placeholder="<EMAIL>"
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                        />
                        {errors.smtp_username && (
                          <p className="text-red-500 text-sm mt-1">
                            {isRTL ? 'البريد الإلكتروني مطلوب' : 'Email address is required'}
                          </p>
                        )}
                      </div>

                      {/* Password */}
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          {watchedProvider && emailProviders[watchedProvider]?.requiresApiKey
                            ? (isRTL ? 'API Key' : 'API Key')
                            : watchedProvider && emailProviders[watchedProvider]?.requiresAppPassword
                            ? (isRTL ? 'كلمة مرور التطبيق' : 'App Password')
                            : (isRTL ? 'كلمة المرور' : 'Password')
                          }
                        </label>
                        <div className="relative">
                          <input
                            type={showPassword ? 'text' : 'password'}
                            {...register('smtp_password', { required: true })}
                            placeholder={
                              watchedProvider && emailProviders[watchedProvider]?.requiresApiKey
                                ? (isRTL ? 'أدخل API Key' : 'Enter API Key')
                                : watchedProvider && emailProviders[watchedProvider]?.requiresAppPassword
                                ? (isRTL ? 'كلمة مرور التطبيق' : 'App Password')
                                : (isRTL ? 'كلمة المرور' : 'Password')
                            }
                            className="w-full px-3 py-2 pr-10 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                          />
                          <button
                            type="button"
                            onClick={() => setShowPassword(!showPassword)}
                            className="absolute inset-y-0 right-0 pr-3 flex items-center"
                          >
                            {showPassword ? (
                              <EyeOff className="h-4 w-4 text-gray-400" />
                            ) : (
                              <Eye className="h-4 w-4 text-gray-400" />
                            )}
                          </button>
                        </div>
                        {errors.smtp_password && (
                          <p className="text-red-500 text-sm mt-1">
                            {watchedProvider && emailProviders[watchedProvider]?.requiresApiKey
                              ? (isRTL ? 'API Key مطلوب' : 'API Key is required')
                              : (isRTL ? 'كلمة المرور مطلوبة' : 'Password is required')
                            }
                          </p>
                        )}
                      </div>

                      {/* From Name */}
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          {isRTL ? 'اسم المرسل' : 'From Name'}
                        </label>
                        <input
                          type="text"
                          {...register('smtp_from_name')}
                          placeholder={isRTL ? 'نظام التسويق الإلكتروني' : 'Email Marketing System'}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                        />
                      </div>

                      {/* From Email */}
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          {isRTL ? 'بريد المرسل' : 'From Email'}
                        </label>
                        <input
                          type="email"
                          {...register('smtp_from_email')}
                          placeholder="<EMAIL>"
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                        />
                      </div>
                    </div>

                    {/* SSL/TLS */}
                    <div className="flex items-center justify-between p-4 bg-gray-50 rounded-md">
                      <div>
                        <label className="text-sm font-medium text-gray-700">
                          {isRTL ? 'استخدام SSL/TLS' : 'Use SSL/TLS'}
                        </label>
                        <p className="text-xs text-gray-500">
                          {isRTL ? 'تشفير الاتصال (موصى به)' : 'Encrypt connection (recommended)'}
                        </p>
                      </div>
                      <input
                        type="checkbox"
                        {...register('smtp_secure')}
                        className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                      />
                    </div>

                    {/* Test Email Section */}
                    <div className="border-t border-gray-200 pt-6">
                      <h3 className="text-lg font-medium text-gray-900 mb-4">
                        {isRTL ? 'اختبار البريد الإلكتروني' : 'Test Email'}
                      </h3>

                      <div className="space-y-4">
                        <div className="flex space-x-4 rtl:space-x-reverse">
                          <div className="flex-1">
                            <input
                              type="email"
                              {...register('test_email')}
                              placeholder={isRTL ? 'أدخل بريد إلكتروني للاختبار' : 'Enter test email address'}
                              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                            />
                          </div>
                          <button
                            type="button"
                            onClick={testEmailConnection}
                            disabled={testingEmail}
                            className="flex items-center space-x-2 rtl:space-x-reverse px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50"
                          >
                            {testingEmail ? (
                              <LoadingSpinner size="sm" />
                            ) : (
                              <TestTube className="h-4 w-4" />
                            )}
                            <span>
                              {testingEmail
                                ? (isRTL ? 'جاري الاختبار...' : 'Testing...')
                                : (isRTL ? 'اختبار' : 'Test')
                              }
                            </span>
                          </button>
                        </div>

                        {/* cPanel Auto-Diagnosis */}
                        {watchedProvider && emailProviders[watchedProvider]?.isCPanel && (
                          <div className="flex justify-center space-x-4 rtl:space-x-reverse">
                            <button
                              type="button"
                              onClick={diagnoseCPanelSettings}
                              disabled={diagnosingCPanel}
                              className="flex items-center space-x-2 rtl:space-x-reverse px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 disabled:opacity-50"
                            >
                              {diagnosingCPanel ? (
                                <LoadingSpinner size="sm" />
                              ) : (
                                <CheckCircle className="h-4 w-4" />
                              )}
                              <span>
                                {diagnosingCPanel
                                  ? (isRTL ? 'جاري التشخيص...' : 'Diagnosing...')
                                  : (isRTL ? 'تشخيص تلقائي لـ cPanel' : 'Auto-Diagnose cPanel')
                                }
                              </span>
                            </button>

                            {/* Direct test with exact settings */}
                            <button
                              type="button"
                              onClick={async () => {
                                const formData = watch();
                                console.log('🧪 Direct test with exact settings:', {
                                  host: formData.smtp_host,
                                  port: formData.smtp_port,
                                  email: formData.smtp_username,
                                  secure: formData.smtp_secure
                                });

                                try {
                                  setTestingEmail(true);
                                  setEmailTestResult(null);

                                  const result = await apiHelpers.post('/email-config/test-direct', {
                                    host: formData.smtp_host,
                                    port: formData.smtp_port,
                                    email: formData.smtp_username,
                                    password: formData.smtp_password,
                                    secure: formData.smtp_secure
                                  });

                                  console.log('Direct test result:', result);

                                  if (result.success) {
                                    setEmailTestResult({
                                      success: true,
                                      message: `✅ ${result.data.message} (${result.data.responseTime})`
                                    });
                                    toast.success(
                                      isRTL
                                        ? `نجح الاتصال! وقت الاستجابة: ${result.data.responseTime}`
                                        : `Connection successful! Response time: ${result.data.responseTime}`
                                    );
                                  } else {
                                    setEmailTestResult({
                                      success: false,
                                      message: `❌ ${result.error} (Code: ${result.errorCode || 'Unknown'})`
                                    });
                                    toast.error(
                                      isRTL
                                        ? `فشل الاتصال: ${result.error}`
                                        : `Connection failed: ${result.error}`
                                    );
                                  }
                                } catch (error) {
                                  console.error('Direct test error:', error);
                                  setEmailTestResult({
                                    success: false,
                                    message: `❌ ${error.response?.data?.error || error.message}`
                                  });
                                  toast.error(
                                    isRTL
                                      ? `خطأ في الاختبار: ${error.response?.data?.error || error.message}`
                                      : `Test error: ${error.response?.data?.error || error.message}`
                                  );
                                } finally {
                                  setTestingEmail(false);
                                }
                              }}
                              disabled={testingEmail}
                              className="flex items-center space-x-2 rtl:space-x-reverse px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-purple-500 disabled:opacity-50"
                            >
                              {testingEmail ? (
                                <LoadingSpinner size="sm" />
                              ) : (
                                <TestTube className="h-4 w-4" />
                              )}
                              <span>
                                {testingEmail
                                  ? (isRTL ? 'جاري الاختبار...' : 'Testing...')
                                  : (isRTL ? 'اختبار مباشر' : 'Direct Test')
                                }
                              </span>
                            </button>
                          </div>
                        )}
                      </div>

                      {emailTestResult && !emailTestResult.success && (
                        <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-md">
                          <p className="text-sm text-red-800">
                            <strong>{isRTL ? 'خطأ:' : 'Error:'}</strong> {emailTestResult.message}
                          </p>
                        </div>
                      )}

                      {/* Advanced cPanel Diagnosis Results */}
                      {cPanelDiagnosis && (
                        <div className="mt-4 p-4 bg-gray-50 border border-gray-200 rounded-md">
                          <h4 className="font-medium text-gray-900 mb-3">
                            {isRTL ? 'نتائج التشخيص المتقدم' : 'Advanced Diagnosis Results'}
                          </h4>

                          {/* Summary */}
                          <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-md">
                            <div className="text-sm text-blue-800">
                              <p><strong>{isRTL ? 'الملخص:' : 'Summary:'}</strong></p>
                              <p>{isRTL ? 'تم اختبار' : 'Tested'} {cPanelDiagnosis.summary?.totalTests || 0} {isRTL ? 'إعدادات' : 'configurations'}</p>
                              <p className="text-green-700">✅ {cPanelDiagnosis.summary?.successful || 0} {isRTL ? 'نجح' : 'successful'}</p>
                              <p className="text-red-700">❌ {cPanelDiagnosis.summary?.failed || 0} {isRTL ? 'فشل' : 'failed'}</p>
                            </div>
                          </div>

                          {cPanelDiagnosis.recommendation ? (
                            <div className="p-3 bg-green-50 border border-green-200 rounded-md mb-3">
                              <div className="flex items-center space-x-2 rtl:space-x-reverse">
                                <CheckCircle className="h-5 w-5 text-green-600" />
                                <span className="font-medium text-green-800">
                                  {isRTL ? 'تم العثور على إعدادات تعمل!' : 'Found working settings!'}
                                </span>
                              </div>
                              <div className="mt-2 text-sm text-green-700">
                                <p><strong>{isRTL ? 'الإعدادات:' : 'Configuration:'}</strong> {cPanelDiagnosis.recommendation.config}</p>
                                <p><strong>{isRTL ? 'الخادم:' : 'Server:'}</strong> {cPanelDiagnosis.recommendation.settings.host}</p>
                                <p><strong>{isRTL ? 'المنفذ:' : 'Port:'}</strong> {cPanelDiagnosis.recommendation.settings.port}</p>
                                <p><strong>{isRTL ? 'SSL:' : 'SSL:'}</strong> {cPanelDiagnosis.recommendation.settings.secure ? (isRTL ? 'مفعل' : 'Enabled') : (isRTL ? 'معطل' : 'Disabled')}</p>
                                <p><strong>{isRTL ? 'وقت الاستجابة:' : 'Response Time:'}</strong> {cPanelDiagnosis.recommendation.responseTime}</p>
                              </div>
                            </div>
                          ) : (
                            <div className="p-3 bg-red-50 border border-red-200 rounded-md mb-3">
                              <div className="flex items-center space-x-2 rtl:space-x-reverse">
                                <XCircle className="h-5 w-5 text-red-600" />
                                <span className="font-medium text-red-800">
                                  {isRTL ? 'لم يتم العثور على إعدادات تعمل' : 'No working settings found'}
                                </span>
                              </div>

                              {/* Recommendations */}
                              {cPanelDiagnosis.summary?.recommendations && Array.isArray(cPanelDiagnosis.summary.recommendations) && cPanelDiagnosis.summary.recommendations.length > 0 && (
                                <div className="mt-3">
                                  <p className="font-medium text-red-800 mb-2">{isRTL ? 'التوصيات:' : 'Recommendations:'}</p>
                                  <ul className="space-y-1">
                                    {cPanelDiagnosis.summary.recommendations.map((rec, index) => (
                                      <li key={index} className="text-sm">
                                        • {isRTL ? (rec.message_ar || rec.message) : rec.message}
                                      </li>
                                    ))}
                                  </ul>
                                </div>
                              )}
                            </div>
                          )}

                          {/* Network Diagnostics */}
                          {cPanelDiagnosis.networkDiagnostics && (
                            <details className="mb-3">
                              <summary className="cursor-pointer text-sm font-medium text-gray-700">
                                {isRTL ? 'تشخيص الشبكة' : 'Network Diagnostics'}
                              </summary>
                              <div className="mt-2 p-3 bg-yellow-50 border border-yellow-200 rounded text-sm">
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                  <div>
                                    <p className="font-medium">{isRTL ? 'سجلات DNS:' : 'DNS Records:'}</p>
                                    <p>MX Records: {cPanelDiagnosis.networkDiagnostics.dns?.hasMX ? '✅' : '❌'}</p>
                                    {cPanelDiagnosis.networkDiagnostics.dns?.mx && (
                                      <ul className="text-xs mt-1">
                                        {cPanelDiagnosis.networkDiagnostics.dns.mx.map((mx, i) => (
                                          <li key={i}>• {mx.exchange} (priority: {mx.priority})</li>
                                        ))}
                                      </ul>
                                    )}
                                  </div>
                                  <div>
                                    <p className="font-medium">{isRTL ? 'حالة المنافذ:' : 'Port Status:'}</p>
                                    {Object.entries(cPanelDiagnosis.networkDiagnostics.ports || {}).map(([host, ports]) => (
                                      <div key={host} className="text-xs">
                                        <p className="font-medium">{host}:</p>
                                        <div className="ml-2">
                                          {Object.entries(ports).map(([port, status]) => (
                                            <span key={port} className={`mr-2 ${status === 'open' ? 'text-green-600' : 'text-red-600'}`}>
                                              {port}:{status === 'open' ? '✅' : '❌'}
                                            </span>
                                          ))}
                                        </div>
                                      </div>
                                    ))}
                                  </div>
                                </div>
                              </div>
                            </details>
                          )}

                          {/* Detailed Results */}
                          <details className="mt-3">
                            <summary className="cursor-pointer text-sm font-medium text-gray-700">
                              {isRTL ? 'النتائج المفصلة' : 'Detailed Results'}
                            </summary>
                            <div className="mt-2 space-y-2">
                              {(cPanelDiagnosis.results || []).map((result, index) => (
                                <div key={index} className={`p-3 rounded text-sm border ${
                                  result.status === 'success'
                                    ? 'bg-green-50 border-green-200 text-green-800'
                                    : 'bg-red-50 border-red-200 text-red-800'
                                }`}>
                                  <div className="flex items-center justify-between mb-2">
                                    <span className="font-medium">{result.config}</span>
                                    <div className="flex items-center space-x-2">
                                      <span className="text-xs">{result.responseTime}</span>
                                      <span className={result.status === 'success' ? 'text-green-600' : 'text-red-600'}>
                                        {result.status === 'success' ? '✅' : '❌'}
                                      </span>
                                    </div>
                                  </div>

                                  {result.status === 'failed' && (
                                    <div className="space-y-1">
                                      <p className="text-xs"><strong>{isRTL ? 'الخطأ:' : 'Error:'}</strong> {result.message}</p>
                                      {result.errorCode && (
                                        <p className="text-xs"><strong>{isRTL ? 'رمز الخطأ:' : 'Error Code:'}</strong> {result.errorCode}</p>
                                      )}
                                      {result.errorType && (
                                        <p className="text-xs"><strong>{isRTL ? 'نوع الخطأ:' : 'Error Type:'}</strong> {result.errorType}</p>
                                      )}
                                      {result.suggestion && (
                                        <p className="text-xs bg-yellow-100 p-2 rounded mt-2">
                                          <strong>{isRTL ? 'الاقتراح:' : 'Suggestion:'}</strong> {isRTL ? result.suggestion_ar : result.suggestion}
                                        </p>
                                      )}
                                    </div>
                                  )}

                                  {result.status === 'success' && (
                                    <div className="text-xs">
                                      <p><strong>{isRTL ? 'الخادم:' : 'Host:'}</strong> {result.details.host}</p>
                                      <p><strong>{isRTL ? 'المنفذ:' : 'Port:'}</strong> {result.details.port}</p>
                                      <p><strong>{isRTL ? 'SSL:' : 'SSL:'}</strong> {result.details.secure ? (isRTL ? 'مفعل' : 'Yes') : (isRTL ? 'معطل' : 'No')}</p>
                                    </div>
                                  )}
                                </div>
                              ))}
                            </div>
                          </details>
                        </div>
                      )}
                    </div>
                  </div>
                )}

                {/* Notifications Tab */}
                {activeTab === 'notifications' && (
                  <div className="space-y-6">
                    <h2 className="text-xl font-semibold text-gray-900">
                      {isRTL ? 'إعدادات الإشعارات' : 'Notification Settings'}
                    </h2>
                    
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <label className="text-sm font-medium text-gray-700">
                          {isRTL ? 'إشعارات البريد الإلكتروني' : 'Email Notifications'}
                        </label>
                        <input
                          type="checkbox"
                          {...register('email_notifications')}
                          className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                        />
                      </div>
                      
                      <div className="flex items-center justify-between">
                        <label className="text-sm font-medium text-gray-700">
                          {isRTL ? 'الإشعارات الصوتية' : 'Sound Notifications'}
                        </label>
                        <input
                          type="checkbox"
                          {...register('sound_notifications')}
                          className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                        />
                      </div>
                    </div>
                  </div>
                )}

                {/* Appearance Tab */}
                {activeTab === 'appearance' && (
                  <div className="space-y-6">
                    <h2 className="text-xl font-semibold text-gray-900">
                      {isRTL ? 'إعدادات المظهر' : 'Appearance Settings'}
                    </h2>
                    
                    {/* Theme */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        {isRTL ? 'المظهر' : 'Theme'}
                      </label>
                      <select
                        {...register('theme')}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                      >
                        {getAvailableThemes().map(themeOption => (
                          <option key={themeOption.value} value={themeOption.value}>
                            {themeOption.label}
                          </option>
                        ))}
                      </select>
                    </div>
                  </div>
                )}

                {/* Save Button */}
                <div className="flex justify-end pt-6 border-t border-gray-200">
                  <button
                    type="submit"
                    disabled={loading}
                    className="flex items-center space-x-2 rtl:space-x-reverse px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 disabled:opacity-50"
                  >
                    {loading ? (
                      <LoadingSpinner size="sm" />
                    ) : (
                      <Save className="h-4 w-4" />
                    )}
                    <span>{t('settings.save_settings') || (isRTL ? 'حفظ الإعدادات' : 'Save Settings')}</span>
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Settings;
