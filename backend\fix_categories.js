const { db } = require('./config/database');

// Fix client categories
const fixCategories = () => {
  return new Promise((resolve, reject) => {
    // First, check if key column exists
    db.all("PRAGMA table_info(client_categories)", [], (err, columns) => {
      if (err) {
        reject(err);
        return;
      }
      
      const hasKeyColumn = columns.some(col => col.name === 'key');
      
      if (!hasKeyColumn) {
        // Add key column
        db.run("ALTER TABLE client_categories ADD COLUMN key VARCHAR(50)", (err) => {
          if (err) {
            console.log('Key column might already exist:', err.message);
          }
          updateKeys();
        });
      } else {
        updateKeys();
      }
      
      function updateKeys() {
        const updates = [
          "UPDATE client_categories SET key = 'general' WHERE name_en = 'General' OR name_ar = 'عام'",
          "UPDATE client_categories SET key = 'premium' WHERE name_en = 'Premium' OR name_ar = 'مميز'", 
          "UPDATE client_categories SET key = 'vip' WHERE name_en = 'VIP' OR name_ar = 'كبار الشخصيات'",
          "UPDATE client_categories SET key = 'corporate' WHERE name_en = 'Corporate' OR name_ar = 'شركات'",
          "UPDATE client_categories SET key = 'individual' WHERE name_en = 'Individual' OR name_ar = 'أفراد'",
          "UPDATE client_categories SET key = 'owners' WHERE name_en = 'Owners' OR name_ar = 'الملاك'"
        ];
        
        let completed = 0;
        updates.forEach(sql => {
          db.run(sql, (err) => {
            if (err) {
              console.error('Error updating:', err);
            }
            completed++;
            if (completed === updates.length) {
              console.log('✅ Categories fixed successfully');
              
              // Verify the results
              db.all("SELECT key, name_en, name_ar FROM client_categories", [], (err, rows) => {
                if (!err) {
                  console.log('📂 Updated categories:', rows);
                }
                resolve();
              });
            }
          });
        });
      }
    });
  });
};

fixCategories().then(() => {
  process.exit(0);
}).catch(err => {
  console.error('Error:', err);
  process.exit(1);
});
