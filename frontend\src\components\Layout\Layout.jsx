import React from 'react';
import { Outlet } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { useTheme } from '../../contexts/ThemeContext';
import Sidebar from './Sidebar';
import Header from './Header';

const Layout = () => {
  const { i18n } = useTranslation();
  const { sidebarCollapsed } = useTheme();
  
  const isRTL = i18n.language === 'ar';

  return (
    <div className="min-h-screen bg-gray-50 flex">
      {/* Sidebar */}
      <Sidebar />
      
      {/* Main Content */}
      <div className={`
        flex-1 flex flex-col transition-all duration-300
        ${sidebarCollapsed 
          ? (isRTL ? 'mr-16' : 'ml-16') 
          : (isRTL ? 'mr-64' : 'ml-64')
        }
      `}>
        {/* Header */}
        <Header />
        
        {/* Page Content */}
        <main className="flex-1 overflow-auto">
          <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <Outlet />
          </div>
        </main>
      </div>
    </div>
  );
};

export default Layout;
