# نظام إدارة التسويق الإلكتروني | Email Marketing Management System

نظام متكامل لإدارة التسويق عبر البريد الإلكتروني مع دعم اللغات المتعددة (العربية والإنجليزية) ودعم RTL/LTR.

A comprehensive email marketing management system with multilingual support (Arabic and English) and RTL/LTR interface capabilities.

## المميزات / Features

### العربية
- واجهة متعددة اللغات مع دعم RTL/LTR
- إدارة العملاء مع رفع ملفات Excel
- إرسال رسائل يدوية ودورية
- تقارير وتحليلات متقدمة
- تخصيص الرسائل حسب اللغة
- **🚀 دعم تلقائي لجميع مزودي البريد الإلكتروني**

### English
- Multi-language interface with RTL/LTR support
- Client management with Excel file upload
- Manual and scheduled message sending
- Advanced reports and analytics
- Language-specific message customization
- **🚀 Automatic support for all email providers**

## ✨ المميزات المتقدمة | Advanced Features

### 📧 دعم البريد الإلكتروني الذكي | Smart Email Support
- **اكتشاف تلقائي للمزود** | Automatic provider detection
- **دعم Gmail, Outlook, Yahoo, iCloud وأكثر** | Support for Gmail, Outlook, Yahoo, iCloud and more
- **إعداد App Password تلقائي** | Automatic App Password setup
- **اختبار التكوين المدمج** | Built-in configuration testing
- **إرشادات مخصصة لكل مزود** | Provider-specific setup guides

## التقنيات المستخدمة / Technologies Used

- **Backend**: Express.js + SQLite
- **Frontend**: React + Vite + i18next
- **Database**: SQLite with structured schema
- **Styling**: Tailwind CSS with RTL support

## هيكل المشروع / Project Structure

```
marketing-email/
├── backend/          # Express.js server
├── frontend/         # React application
├── database/         # SQLite database files
└── docs/            # Documentation
```

## 🚀 التثبيت والتشغيل | Installation & Setup

### 1. تحميل المشروع | Download Project
```bash
git clone <repository-url>
cd "Marketing Email"
npm run install-all
```

### 2. إعداد البريد الإلكتروني | Email Configuration
```bash
# عدل ملف backend/.env | Edit backend/.env file
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
```

**المزودون المدعومون تلقائياً | Automatically Supported Providers:**
- Gmail, Outlook, Yahoo, iCloud, Zoho, AOL, ProtonMail

### 3. تشغيل النظام | Run System
```bash
npm run dev
```

## التشغيل اليدوي | Manual Running

### Backend
```bash
cd backend
npm install
npm start
```

### Frontend
```bash
cd frontend
npm install
npm run dev
```

## المساهمة / Contributing

يرحب بالمساهمات لتحسين التطبيق وإضافة مميزات جديدة.
Contributions are welcome to improve the application and add new features.
