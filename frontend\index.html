<!doctype html>
<html lang="ar" dir="rtl">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>نظام إدارة التسويق الإلكتروني | Email Marketing System</title>
    <meta name="description" content="نظام متكامل لإدارة التسويق عبر البريد الإلكتروني مع دعم اللغات المتعددة" />
    
    <!-- Preload fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- RTL/LTR support -->
    <style>
      /* Base styles for RTL/LTR support */
      html[dir="rtl"] {
        font-family: 'Tahoma', 'Arial', sans-serif;
      }
      
      html[dir="ltr"] {
        font-family: 'Inter', system-ui, sans-serif;
      }
      
      /* Loading spinner */
      .loading-spinner {
        border: 4px solid #f3f3f3;
        border-top: 4px solid #3498db;
        border-radius: 50%;
        width: 40px;
        height: 40px;
        animation: spin 2s linear infinite;
        margin: 20px auto;
      }
      
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
      
      /* Ensure proper text direction */
      .rtl-content {
        direction: rtl;
        text-align: right;
      }
      
      .ltr-content {
        direction: ltr;
        text-align: left;
      }
    </style>
  </head>
  <body class="bg-gray-50">
    <div id="root">
      <!-- Loading fallback -->
      <div class="min-h-screen flex items-center justify-center">
        <div class="text-center">
          <div class="loading-spinner"></div>
          <p class="mt-4 text-gray-600">جاري التحميل... Loading...</p>
        </div>
      </div>
    </div>
    <script type="module" src="/src/main.jsx"></script>
  </body>
</html>
