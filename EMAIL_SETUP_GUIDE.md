# دليل إعداد البريد الإلكتروني | Email Setup Guide

## 🎯 نظرة عامة | Overview

نظام التسويق الإلكتروني يدعم **جميع مزودي البريد الإلكتروني تلقائياً**! فقط أدخل بريدك الإلكتروني وكلمة المرور وسيقوم النظام بالباقي.

The Email Marketing System supports **all email providers automatically**! Just enter your email and password and the system will handle the rest.

## 🚀 الإعداد السريع | Quick Setup

### 1. أضف بريدك الإلكتروني | Add Your Email
```env
# في ملف backend/.env | In backend/.env file
SMTP_USER=<EMAIL>
SMTP_PASS=your-password-here
```

### 2. شغل النظام | Run System
```bash
npm run dev
```

### 3. اختبر الإعداد | Test Configuration
- اذهب إلى الإعدادات → البريد الإلكتروني | Go to Settings → Email
- اضغط "اختبار التكوين" | Click "Test Configuration"
- أرسل رسالة اختبار | Send test email

## 📧 المزودون المدعومون | Supported Providers

### ✅ Gmail
- **يتطلب**: App Password | **Requires**: App Password
- **الإعداد التلقائي**: ✅ | **Auto Setup**: ✅
- **الدليل**: [كيفية إنشاء App Password](#gmail-setup)

### ✅ Outlook/Hotmail
- **يتطلب**: App Password | **Requires**: App Password  
- **الإعداد التلقائي**: ✅ | **Auto Setup**: ✅
- **الدليل**: [كيفية إنشاء App Password](#outlook-setup)

### ✅ Yahoo Mail
- **يتطلب**: App Password | **Requires**: App Password
- **الإعداد التلقائي**: ✅ | **Auto Setup**: ✅
- **الدليل**: [كيفية إنشاء App Password](#yahoo-setup)

### ✅ iCloud Mail
- **يتطلب**: App Password | **Requires**: App Password
- **الإعداد التلقائي**: ✅ | **Auto Setup**: ✅
- **الدليل**: [كيفية إنشاء App Password](#icloud-setup)

### ✅ Zoho Mail
- **يتطلب**: كلمة المرور العادية | **Requires**: Regular Password
- **الإعداد التلقائي**: ✅ | **Auto Setup**: ✅

### ✅ AOL Mail
- **يتطلب**: App Password | **Requires**: App Password
- **الإعداد التلقائي**: ✅ | **Auto Setup**: ✅

### ✅ ProtonMail
- **يتطلب**: كلمة المرور العادية | **Requires**: Regular Password
- **الإعداد التلقائي**: ✅ | **Auto Setup**: ✅

### ✅ أي مزود آخر | Any Other Provider
- **الإعداد اليدوي**: متاح | **Manual Setup**: Available
- **SMTP مخصص**: مدعوم | **Custom SMTP**: Supported

## 🔧 أدلة الإعداد المفصلة | Detailed Setup Guides

### Gmail Setup

#### الخطوات بالعربية:
1. اذهب إلى: https://myaccount.google.com/security
2. فعل "التحقق بخطوتين" (2-Step Verification)
3. اختر "كلمات مرور التطبيقات" (App passwords)
4. اختر "تطبيق آخر" (Other app)
5. اكتب "Email Marketing System"
6. انسخ كلمة المرور المكونة من 16 رقم/حرف
7. استخدمها في SMTP_PASS

#### English Steps:
1. Go to: https://myaccount.google.com/security
2. Enable "2-Step Verification"
3. Select "App passwords"
4. Choose "Other app"
5. Type "Email Marketing System"
6. Copy the 16-character password
7. Use it in SMTP_PASS

### Outlook Setup

#### الخطوات بالعربية:
1. اذهب إلى: https://account.microsoft.com/security
2. فعل "التحقق بخطوتين" (Two-step verification)
3. اختر "كلمات مرور التطبيقات" (App passwords)
4. اضغط "إنشاء كلمة مرور جديدة" (Create new app password)
5. انسخ كلمة المرور
6. استخدمها في SMTP_PASS

#### English Steps:
1. Go to: https://account.microsoft.com/security
2. Enable "Two-step verification"
3. Select "App passwords"
4. Click "Create new app password"
5. Copy the password
6. Use it in SMTP_PASS

### Yahoo Setup

#### الخطوات بالعربية:
1. اذهب إلى: https://login.yahoo.com/account/security
2. فعل "التحقق بخطوتين"
3. اختر "كلمات مرور التطبيقات"
4. اختر "تطبيق آخر"
5. انسخ كلمة المرور
6. استخدمها في SMTP_PASS

#### English Steps:
1. Go to: https://login.yahoo.com/account/security
2. Enable "Two-step verification"
3. Select "App passwords"
4. Choose "Other app"
5. Copy the password
6. Use it in SMTP_PASS

### iCloud Setup

#### الخطوات بالعربية:
1. اذهب إلى: https://appleid.apple.com/account/manage
2. فعل "التحقق بخطوتين"
3. اختر "كلمات مرور التطبيقات"
4. اضغط "إنشاء كلمة مرور"
5. انسخ كلمة المرور
6. استخدمها في SMTP_PASS

#### English Steps:
1. Go to: https://appleid.apple.com/account/manage
2. Enable "Two-factor authentication"
3. Select "App-specific passwords"
4. Click "Generate password"
5. Copy the password
6. Use it in SMTP_PASS

## 🔧 الإعداد اليدوي | Manual Configuration

إذا لم يعمل الإعداد التلقائي، يمكنك استخدام الإعداد اليدوي:
If auto-setup doesn't work, you can use manual configuration:

```env
# إعداد SMTP يدوي | Manual SMTP Setup
SMTP_HOST=smtp.your-provider.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASS=your-password
```

### منافذ SMTP الشائعة | Common SMTP Ports:
- **587**: TLS (مستحسن | Recommended)
- **465**: SSL
- **25**: غير آمن | Unsecured (not recommended)

## 🧪 اختبار التكوين | Testing Configuration

### من واجهة النظام | From System Interface:
1. اذهب إلى الإعدادات | Go to Settings
2. اختر "البريد الإلكتروني" | Select "Email"
3. أدخل بياناتك | Enter your credentials
4. اضغط "اختبار التكوين" | Click "Test Configuration"
5. أرسل رسالة اختبار | Send test email

### من سطر الأوامر | From Command Line:
```bash
cd backend
npm run test-email
```

## ❗ استكشاف الأخطاء | Troubleshooting

### خطأ المصادقة | Authentication Error
- ✅ تأكد من استخدام App Password وليس كلمة المرور العادية
- ✅ Make sure to use App Password, not regular password
- ✅ تحقق من تفعيل التحقق بخطوتين
- ✅ Verify 2-factor authentication is enabled

### خطأ الاتصال | Connection Error
- ✅ تحقق من اتصال الإنترنت
- ✅ Check internet connection
- ✅ تأكد من عدم حجب منافذ SMTP
- ✅ Ensure SMTP ports are not blocked

### مزود غير مدعوم | Unsupported Provider
- ✅ استخدم الإعداد اليدوي
- ✅ Use manual configuration
- ✅ ابحث عن إعدادات SMTP للمزود
- ✅ Look up SMTP settings for your provider

## 📞 الدعم | Support

إذا واجهت مشاكل في الإعداد:
If you encounter setup issues:

- 📖 راجع هذا الدليل | Review this guide
- 🔧 جرب الإعداد اليدوي | Try manual configuration  
- 🐛 أبلغ عن المشكلة | Report the issue on GitHub
- 📧 راسلنا | Email us: <EMAIL>

---

**تم تطويره بـ ❤️ لدعم جميع مزودي البريد الإلكتروني**
**Developed with ❤️ to support all email providers**
