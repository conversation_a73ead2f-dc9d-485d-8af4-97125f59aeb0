const multer = require('multer');
const path = require('path');
const fs = require('fs');

// Ensure upload directory exists
const uploadDir = process.env.UPLOAD_PATH || './uploads';
if (!fs.existsSync(uploadDir)) {
  fs.mkdirSync(uploadDir, { recursive: true });
}

// Configure storage
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    cb(null, uploadDir);
  },
  filename: function (req, file, cb) {
    // Generate unique filename
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    const extension = path.extname(file.originalname);
    cb(null, file.fieldname + '-' + uniqueSuffix + extension);
  }
});

// File filter for Excel and CSV files
const fileFilter = (req, file, cb) => {
  console.log('📁 File upload attempt:', {
    originalname: file.originalname,
    mimetype: file.mimetype,
    size: file.size
  });

  const allowedMimes = [
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'application/vnd.ms-excel.sheet.macroEnabled.12',
    'text/csv',
    'application/csv',
    'text/plain' // Sometimes CSV files are detected as text/plain
  ];

  const allowedExtensions = ['.xls', '.xlsx', '.xlsm', '.csv'];
  const fileExtension = path.extname(file.originalname).toLowerCase();

  console.log('🔍 File validation:', {
    extension: fileExtension,
    mimeType: file.mimetype,
    mimeAllowed: allowedMimes.includes(file.mimetype),
    extensionAllowed: allowedExtensions.includes(fileExtension)
  });

  if (allowedMimes.includes(file.mimetype) || allowedExtensions.includes(fileExtension)) {
    console.log('✅ File accepted');
    cb(null, true);
  } else {
    console.log('❌ File rejected');
    cb(new Error('Only Excel (.xls, .xlsx, .xlsm) and CSV files are allowed'), false);
  }
};

// Configure multer
const upload = multer({
  storage: storage,
  limits: {
    fileSize: parseInt(process.env.MAX_FILE_SIZE) || 10 * 1024 * 1024, // 10MB default
    files: 1
  },
  fileFilter: fileFilter
});

// Error handling middleware
const handleUploadError = (err, req, res, next) => {
  if (err instanceof multer.MulterError) {
    if (err.code === 'LIMIT_FILE_SIZE') {
      return res.status(400).json({
        error: 'File too large',
        message_ar: 'الملف كبير جداً',
        maxSize: process.env.MAX_FILE_SIZE || '10MB'
      });
    } else if (err.code === 'LIMIT_FILE_COUNT') {
      return res.status(400).json({
        error: 'Too many files',
        message_ar: 'عدد كبير من الملفات'
      });
    } else if (err.code === 'LIMIT_UNEXPECTED_FILE') {
      return res.status(400).json({
        error: 'Unexpected field name',
        message_ar: 'اسم حقل غير متوقع'
      });
    }
  } else if (err) {
    return res.status(400).json({
      error: err.message,
      message_ar: 'خطأ في رفع الملف'
    });
  }
  
  next();
};

// Clean up uploaded file
const cleanupFile = (filePath) => {
  try {
    if (fs.existsSync(filePath)) {
      fs.unlinkSync(filePath);
    }
  } catch (error) {
    console.error('Error cleaning up file:', error);
  }
};

module.exports = {
  upload,
  handleUploadError,
  cleanupFile
};
