import React from 'react';
import { useTranslation } from 'react-i18next';
import { AlertTriangle, RefreshCw, Home } from 'lucide-react';

class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false, error: null, errorInfo: null };
  }

  static getDerivedStateFromError(error) {
    // Update state so the next render will show the fallback UI
    return { hasError: true };
  }

  componentDidCatch(error, errorInfo) {
    // Log error details
    console.error('Error Boundary caught an error:', error, errorInfo);
    
    this.setState({
      error: error,
      errorInfo: errorInfo
    });

    // You can also log the error to an error reporting service here
    // logErrorToService(error, errorInfo);
  }

  handleReload = () => {
    window.location.reload();
  };

  handleGoHome = () => {
    window.location.href = '/dashboard';
  };

  render() {
    if (this.state.hasError) {
      return <ErrorFallback 
        error={this.state.error}
        errorInfo={this.state.errorInfo}
        onReload={this.handleReload}
        onGoHome={this.handleGoHome}
      />;
    }

    return this.props.children;
  }
}

const ErrorFallback = ({ error, errorInfo, onReload, onGoHome }) => {
  const { t, i18n } = useTranslation();
  const isRTL = i18n.language === 'ar';

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center px-4">
      <div className="max-w-md w-full">
        <div className="bg-white rounded-lg shadow-lg p-8 text-center">
          {/* Error Icon */}
          <div className="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-red-100 mb-6">
            <AlertTriangle className="h-8 w-8 text-red-600" />
          </div>

          {/* Error Title */}
          <h1 className="text-2xl font-bold text-gray-900 mb-4">
            {isRTL ? 'حدث خطأ غير متوقع' : 'Something went wrong'}
          </h1>

          {/* Error Description */}
          <p className="text-gray-600 mb-6">
            {isRTL 
              ? 'نعتذر، حدث خطأ غير متوقع. يرجى المحاولة مرة أخرى أو العودة إلى الصفحة الرئيسية.'
              : 'We apologize for the inconvenience. An unexpected error occurred. Please try again or return to the home page.'
            }
          </p>

          {/* Error Details (Development only) */}
          {process.env.NODE_ENV === 'development' && error && (
            <details className="mb-6 text-left">
              <summary className="cursor-pointer text-sm font-medium text-gray-700 mb-2">
                {isRTL ? 'تفاصيل الخطأ (للمطورين)' : 'Error Details (Development)'}
              </summary>
              <div className="bg-gray-100 rounded p-3 text-xs font-mono text-gray-800 overflow-auto max-h-32">
                <div className="mb-2">
                  <strong>Error:</strong> {error.toString()}
                </div>
                {errorInfo && (
                  <div>
                    <strong>Stack Trace:</strong>
                    <pre className="whitespace-pre-wrap">{errorInfo.componentStack}</pre>
                  </div>
                )}
              </div>
            </details>
          )}

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-3 justify-center">
            <button
              onClick={onReload}
              className="btn btn-primary flex items-center justify-center gap-2"
            >
              <RefreshCw className="h-4 w-4" />
              {isRTL ? 'إعادة تحميل الصفحة' : 'Reload Page'}
            </button>
            
            <button
              onClick={onGoHome}
              className="btn btn-outline flex items-center justify-center gap-2"
            >
              <Home className="h-4 w-4" />
              {isRTL ? 'العودة للرئيسية' : 'Go to Home'}
            </button>
          </div>

          {/* Support Information */}
          <div className="mt-8 pt-6 border-t border-gray-200">
            <p className="text-sm text-gray-500">
              {isRTL 
                ? 'إذا استمر هذا الخطأ، يرجى الاتصال بالدعم الفني.'
                : 'If this error persists, please contact technical support.'
              }
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ErrorBoundary;
