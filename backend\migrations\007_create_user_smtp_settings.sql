-- Create user SMTP settings table
CREATE TABLE IF NOT EXISTS user_smtp_settings (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  user_id INTEGER NOT NULL,
  smtp_host VARCHAR(255) NOT NULL,
  smtp_port INTEGER NOT NULL DEFAULT 587,
  smtp_username VARCHAR(255) NOT NULL,
  smtp_password TEXT NOT NULL, -- Encrypted
  smtp_secure BOOLEAN DEFAULT false,
  from_email VARCHAR(255) NOT NULL,
  from_name VARCHAR(255) NOT NULL,
  is_active BOOLEAN DEFAULT true,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Create index for faster lookups
CREATE INDEX IF NOT EXISTS idx_user_smtp_user_id ON user_smtp_settings(user_id);
CREATE INDEX IF NOT EXISTS idx_user_smtp_active ON user_smtp_settings(user_id, is_active);
