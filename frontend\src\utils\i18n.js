import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import LanguageDetector from 'i18next-browser-languagedetector';

// Import translation files
import arTranslations from '../locales/ar.json';
import enTranslations from '../locales/en.json';

// Language resources
const resources = {
  ar: {
    translation: arTranslations
  },
  en: {
    translation: enTranslations
  }
};

// Language detector configuration
const detectionOptions = {
  // Order of language detection methods
  order: ['localStorage', 'navigator', 'htmlTag'],
  
  // Keys to look for in localStorage
  lookupLocalStorage: 'i18nextLng',
  
  // Cache user language
  caches: ['localStorage'],
  
  // Don't convert country code to language code
  convertDetectedLanguage: (lng) => {
    // Convert country codes to language codes
    if (lng.startsWith('ar')) return 'ar';
    if (lng.startsWith('en')) return 'en';
    return lng;
  }
};

// Initialize i18next
i18n
  .use(LanguageDetector)
  .use(initReactI18next)
  .init({
    resources,
    
    // Default language
    fallbackLng: 'ar',
    
    // Default namespace
    defaultNS: 'translation',
    
    // Language detection
    detection: detectionOptions,
    
    // Interpolation options
    interpolation: {
      escapeValue: false, // React already escapes values
    },
    
    // React options
    react: {
      useSuspense: false, // Disable suspense for SSR compatibility
    },
    
    // Debug mode (disable in production)
    debug: process.env.NODE_ENV === 'development',
    
    // Key separator
    keySeparator: '.',
    
    // Namespace separator
    nsSeparator: ':',
    
    // Return objects for nested keys
    returnObjects: true,
  });

// Language change handler
i18n.on('languageChanged', (lng) => {
  // Update document direction and language
  const isRTL = lng === 'ar';
  document.documentElement.dir = isRTL ? 'rtl' : 'ltr';
  document.documentElement.lang = lng;
  
  // Update body class for styling
  document.body.className = document.body.className
    .replace(/\b(rtl|ltr)\b/g, '')
    .trim();
  document.body.classList.add(isRTL ? 'rtl' : 'ltr');
  
  // Update font family
  const fontFamily = isRTL 
    ? "'Tahoma', 'Arial', sans-serif" 
    : "'Inter', system-ui, sans-serif";
  document.documentElement.style.setProperty('--font-family', fontFamily);
  
  // Store language preference
  localStorage.setItem('i18nextLng', lng);
  
  // Dispatch custom event for components that need to react to language changes
  window.dispatchEvent(new CustomEvent('languageChanged', { 
    detail: { language: lng, isRTL } 
  }));
});

// Helper functions
export const getCurrentLanguage = () => i18n.language;

export const isRTL = () => i18n.language === 'ar';

export const changeLanguage = (lng) => {
  return i18n.changeLanguage(lng);
};

export const getDirection = () => isRTL() ? 'rtl' : 'ltr';

export const getOppositeDirection = () => isRTL() ? 'ltr' : 'rtl';

export const formatNumber = (number, options = {}) => {
  const locale = i18n.language === 'ar' ? 'ar-SA' : 'en-US';
  return new Intl.NumberFormat(locale, options).format(number);
};

export const formatDate = (date, options = {}) => {
  const locale = i18n.language === 'ar' ? 'ar-SA' : 'en-US';
  const defaultOptions = {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    ...options
  };
  return new Intl.DateTimeFormat(locale, defaultOptions).format(new Date(date));
};

export const formatTime = (date, options = {}) => {
  const locale = i18n.language === 'ar' ? 'ar-SA' : 'en-US';
  const defaultOptions = {
    hour: '2-digit',
    minute: '2-digit',
    ...options
  };
  return new Intl.DateTimeFormat(locale, defaultOptions).format(new Date(date));
};

export const formatDateTime = (date, options = {}) => {
  const locale = i18n.language === 'ar' ? 'ar-SA' : 'en-US';
  const defaultOptions = {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
    ...options
  };
  return new Intl.DateTimeFormat(locale, defaultOptions).format(new Date(date));
};

export const formatCurrency = (amount, currency = 'SAR', options = {}) => {
  const locale = i18n.language === 'ar' ? 'ar-SA' : 'en-US';
  const defaultOptions = {
    style: 'currency',
    currency,
    ...options
  };
  return new Intl.NumberFormat(locale, defaultOptions).format(amount);
};

export const formatPercentage = (value, options = {}) => {
  const locale = i18n.language === 'ar' ? 'ar-SA' : 'en-US';
  const defaultOptions = {
    style: 'percent',
    minimumFractionDigits: 0,
    maximumFractionDigits: 2,
    ...options
  };
  return new Intl.NumberFormat(locale, defaultOptions).format(value / 100);
};

// Translation helper with fallback
export const t = (key, options = {}) => {
  return i18n.t(key, options);
};

// Get translation with interpolation
export const translate = (key, values = {}) => {
  return i18n.t(key, values);
};

// Check if translation exists
export const hasTranslation = (key) => {
  return i18n.exists(key);
};

// Get all available languages
export const getAvailableLanguages = () => {
  return Object.keys(resources);
};

// Get language display name
export const getLanguageDisplayName = (lng) => {
  const names = {
    ar: 'العربية',
    en: 'English'
  };
  return names[lng] || lng;
};

// CSS class helpers for RTL/LTR
export const getTextAlignClass = () => isRTL() ? 'text-right' : 'text-left';
export const getMarginStartClass = (size) => isRTL() ? `mr-${size}` : `ml-${size}`;
export const getMarginEndClass = (size) => isRTL() ? `ml-${size}` : `mr-${size}`;
export const getPaddingStartClass = (size) => isRTL() ? `pr-${size}` : `pl-${size}`;
export const getPaddingEndClass = (size) => isRTL() ? `pl-${size}` : `pr-${size}`;
export const getBorderStartClass = () => isRTL() ? 'border-r' : 'border-l';
export const getBorderEndClass = () => isRTL() ? 'border-l' : 'border-r';

export default i18n;
