const { db } = require('../config/database');
const crypto = require('crypto');

class UserSmtpSettings {
  static encrypt(text) {
    const algorithm = 'aes-256-cbc';
    const key = process.env.ENCRYPTION_KEY || 'default-encryption-key-32-chars!!';
    const iv = crypto.randomBytes(16);
    const cipher = crypto.createCipher(algorithm, key);
    let encrypted = cipher.update(text, 'utf8', 'hex');
    encrypted += cipher.final('hex');
    return iv.toString('hex') + ':' + encrypted;
  }

  static decrypt(text) {
    try {
      const algorithm = 'aes-256-cbc';
      const key = process.env.ENCRYPTION_KEY || 'default-encryption-key-32-chars!!';
      const textParts = text.split(':');
      const iv = Buffer.from(textParts.shift(), 'hex');
      const encryptedText = textParts.join(':');
      const decipher = crypto.createDecipher(algorithm, key);
      let decrypted = decipher.update(encryptedText, 'hex', 'utf8');
      decrypted += decipher.final('utf8');
      return decrypted;
    } catch (error) {
      return text; // Return original if decryption fails
    }
  }

  static async create(userId, smtpData) {
    return new Promise((resolve, reject) => {
      const encryptedPassword = this.encrypt(smtpData.smtp_password);
      
      const query = `
        INSERT INTO user_smtp_settings (
          user_id, smtp_host, smtp_port, smtp_username, smtp_password,
          smtp_secure, from_email, from_name, is_active
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
      `;
      
      db.run(query, [
        userId,
        smtpData.smtp_host,
        smtpData.smtp_port || 587,
        smtpData.smtp_username,
        encryptedPassword,
        smtpData.smtp_secure || false,
        smtpData.from_email,
        smtpData.from_name,
        smtpData.is_active !== false
      ], function(err) {
        if (err) {
          reject(err);
        } else {
          resolve({ id: this.lastID });
        }
      });
    });
  }

  static async getByUserId(userId) {
    return new Promise((resolve, reject) => {
      const query = `
        SELECT * FROM user_smtp_settings 
        WHERE user_id = ? AND is_active = true
        ORDER BY created_at DESC
        LIMIT 1
      `;
      
      db.get(query, [userId], (err, row) => {
        if (err) {
          reject(err);
        } else if (row) {
          // Decrypt password before returning
          row.smtp_password = this.decrypt(row.smtp_password);
          resolve(row);
        } else {
          resolve(null);
        }
      });
    });
  }

  static async update(userId, smtpData) {
    return new Promise((resolve, reject) => {
      const encryptedPassword = this.encrypt(smtpData.smtp_password);
      
      const query = `
        UPDATE user_smtp_settings SET
          smtp_host = ?, smtp_port = ?, smtp_username = ?, smtp_password = ?,
          smtp_secure = ?, from_email = ?, from_name = ?, updated_at = CURRENT_TIMESTAMP
        WHERE user_id = ? AND is_active = true
      `;
      
      db.run(query, [
        smtpData.smtp_host,
        smtpData.smtp_port || 587,
        smtpData.smtp_username,
        encryptedPassword,
        smtpData.smtp_secure || false,
        smtpData.from_email,
        smtpData.from_name,
        userId
      ], function(err) {
        if (err) {
          reject(err);
        } else {
          resolve({ changes: this.changes });
        }
      });
    });
  }

  static async delete(userId) {
    return new Promise((resolve, reject) => {
      const query = `UPDATE user_smtp_settings SET is_active = false WHERE user_id = ?`;
      
      db.run(query, [userId], function(err) {
        if (err) {
          reject(err);
        } else {
          resolve({ changes: this.changes });
        }
      });
    });
  }
}

module.exports = UserSmtpSettings;
