import React, { useState, useEffect, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { Settings, AlertCircle } from 'lucide-react';

const TemplateVariablesSimple = ({ template, variables = {}, onVariablesChange }) => {
  const { t, i18n } = useTranslation();
  const isRTL = i18n.language === 'ar';
  
  const [detectedVariables, setDetectedVariables] = useState([]);
  const [formData, setFormData] = useState({});

  // Known system variables that shouldn't be shown as custom
  const systemVariables = [
    'direction', 'language', 'subject', 'company_name', 'company_name_ar', 'company_name_en',
    'company_address', 'company_address_ar', 'company_address_en', 'title', 'title_ar', 'title_en',
    'greeting', 'greeting_ar', 'greeting_en', 'client_name', 'content', 'unsubscribe_url',
    'unsubscribe_text', 'unsubscribe_text_ar', 'unsubscribe_text_en', 'thank_you', 'thank_you_ar',
    'thank_you_en', 'best_regards', 'best_regards_ar', 'best_regards_en', 'tracking_pixel'
  ];

  // Extract variables from template content
  const extractVariables = useCallback((htmlContent) => {
    if (!htmlContent) return [];
    
    const variablePattern = /\{\{([^}]+)\}\}/g;
    const matches = htmlContent.match(variablePattern) || [];
    
    return matches
      .map(match => match.replace(/[{}]/g, '').trim())
      .filter((variable, index, array) => array.indexOf(variable) === index)
      .filter(variable => !systemVariables.includes(variable))
      .sort();
  }, []);

  // Update detected variables when template changes
  useEffect(() => {
    const newVariables = extractVariables(template?.html_content);
    setDetectedVariables(newVariables);
  }, [template?.html_content, extractVariables]);

  // Initialize form data from variables prop
  useEffect(() => {
    if (variables && typeof variables === 'object') {
      setFormData(prev => ({ ...prev, ...variables }));
    }
  }, [variables]);

  // Handle input change
  const handleInputChange = (variableName, value) => {
    setFormData(prev => {
      const updated = { ...prev, [variableName]: value };
      
      // Notify parent immediately
      if (onVariablesChange) {
        onVariablesChange(updated);
      }
      
      return updated;
    });
  };

  if (!template || detectedVariables.length === 0) {
    return (
      <div className="bg-white rounded-lg shadow-sm border p-6">
        <div className="flex items-center gap-2 mb-4">
          <Settings className="h-5 w-5 text-blue-600" />
          <h3 className="text-lg font-semibold text-gray-900">
            {isRTL ? 'متغيرات القالب المخصصة' : 'Custom Template Variables'}
          </h3>
        </div>
        
        <div className="text-center py-8">
          <AlertCircle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <p className="text-gray-500">
            {isRTL 
              ? 'لم يتم العثور على متغيرات مخصصة في هذا القالب'
              : 'No custom variables found in this template'
            }
          </p>
          <p className="text-sm text-gray-400 mt-2">
            {isRTL 
              ? 'استخدم {{variable_name}} في القالب لإنشاء متغيرات مخصصة'
              : 'Use {{variable_name}} in template to create custom variables'
            }
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-sm border p-6">
      <div className="flex items-center gap-2 mb-4">
        <Settings className="h-5 w-5 text-blue-600" />
        <h3 className="text-lg font-semibold text-gray-900">
          {isRTL ? 'متغيرات القالب المخصصة' : 'Custom Template Variables'}
        </h3>
      </div>

      <div className="space-y-4">
        <p className="text-sm text-gray-600">
          {isRTL 
            ? `تم العثور على ${detectedVariables.length} متغير مخصص`
            : `Found ${detectedVariables.length} custom variable(s)`
          }
        </p>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {detectedVariables.map((variable) => (
            <div key={variable} className="space-y-3 p-4 border rounded-lg bg-gray-50">
              <div className="flex items-center justify-between">
                <label className="block text-sm font-medium text-gray-700">
                  <code className="bg-gray-200 px-2 py-1 rounded text-xs">
                    {`{{${variable}}}`}
                  </code>
                </label>
              </div>
              
              <div className="space-y-2">
                {/* Arabic value */}
                <div>
                  <label className="block text-xs text-gray-500 mb-1">
                    {isRTL ? 'القيمة بالعربية' : 'Arabic Value'}
                  </label>
                  <input
                    type="text"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
                    placeholder={isRTL ? 'أدخل القيمة بالعربية' : 'Enter Arabic value'}
                    value={formData[`${variable}_ar`] || ''}
                    onChange={(e) => handleInputChange(`${variable}_ar`, e.target.value)}
                  />
                </div>
                
                {/* English value */}
                <div>
                  <label className="block text-xs text-gray-500 mb-1">
                    {isRTL ? 'القيمة بالإنجليزية' : 'English Value'}
                  </label>
                  <input
                    type="text"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
                    placeholder={isRTL ? 'أدخل القيمة بالإنجليزية' : 'Enter English value'}
                    value={formData[`${variable}_en`] || ''}
                    onChange={(e) => handleInputChange(`${variable}_en`, e.target.value)}
                  />
                </div>
                
                {/* Default value */}
                <div>
                  <label className="block text-xs text-gray-500 mb-1">
                    {isRTL ? 'القيمة الافتراضية' : 'Default Value'}
                  </label>
                  <input
                    type="text"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
                    placeholder={isRTL ? 'القيمة الافتراضية' : 'Default value'}
                    value={formData[variable] || ''}
                    onChange={(e) => handleInputChange(variable, e.target.value)}
                  />
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Instructions */}
      <div className="mt-6 p-4 bg-blue-50 rounded-lg">
        <h5 className="font-medium text-blue-900 mb-2">
          {isRTL ? 'تعليمات الاستخدام:' : 'Usage Instructions:'}
        </h5>
        <ul className="text-sm text-blue-800 space-y-1">
          <li>
            {isRTL 
              ? '• املأ القيم بكلا اللغتين لضمان التوافق'
              : '• Fill values in both languages for compatibility'
            }
          </li>
          <li>
            {isRTL 
              ? '• القيمة الافتراضية ستظهر حسب لغة العميل'
              : '• Default value will show based on client language'
            }
          </li>
          <li>
            {isRTL 
              ? '• يتم حفظ البيانات تلقائياً عند الكتابة'
              : '• Data is saved automatically as you type'
            }
          </li>
        </ul>
      </div>
    </div>
  );
};

export default TemplateVariablesSimple;
