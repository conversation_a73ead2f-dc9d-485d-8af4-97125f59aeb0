const express = require('express');
const router = express.Router();
const Interaction = require('../models/Interaction');
const { db } = require('../config/database');

// Get all interactions with pagination and filtering
router.get('/', async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const filters = {
      client_id: req.query.client_id,
      message_id: req.query.message_id,
      type: req.query.type,
      status: req.query.status,
      date_from: req.query.date_from,
      date_to: req.query.date_to
    };

    const result = await Interaction.getAll(page, limit, filters);
    
    res.json({
      success: true,
      data: result,
      message: 'Interactions retrieved successfully',
      message_ar: 'تم استرجاع التفاعلات بنجاح'
    });
  } catch (error) {
    console.error('Error fetching interactions:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch interactions',
      message_ar: 'فشل في استرجاع التفاعلات'
    });
  }
});

// Get engagement timeline data
router.get('/timeline', async (req, res) => {
  try {
    const {
      message_id,
      client_id,
      days = 7,
      interval = 'day',
      start_date
    } = req.query;

    console.log('📈 Getting engagement timeline:', {
      message_id,
      client_id,
      days,
      interval,
      start_date
    });

    let whereClause = 'WHERE 1=1';
    const params = [];

    if (message_id) {
      whereClause += ' AND message_id = ?';
      params.push(message_id);
    }

    if (client_id) {
      whereClause += ' AND client_id = ?';
      params.push(client_id);
    }

    // Add date filter
    if (start_date) {
      if (interval === 'day') {
        whereClause += ' AND created_at >= date(?) AND created_at < date(?, "+" || ? || " days")';
        params.push(start_date, start_date, days);
      } else if (interval === 'week') {
        const weeks = Math.ceil(days / 7);
        whereClause += ' AND created_at >= date(?) AND created_at < date(?, "+" || ? || " days")';
        params.push(start_date, start_date, weeks * 7);
      } else if (interval === 'month') {
        whereClause += ' AND created_at >= date(?) AND created_at < date(?, "+" || ? || " months")';
        params.push(start_date, start_date, days);
      }
    } else {
      // Default: last N days from now
      whereClause += ' AND created_at >= date("now", "-" || ? || " days")';
      params.push(days);
    }

    // Build SELECT and GROUP BY based on interval
    let dateExpression, groupBy;
    if (interval === 'day') {
      dateExpression = 'DATE(created_at)';
      groupBy = 'DATE(created_at)';
    } else if (interval === 'week') {
      // Group by week (start of week)
      dateExpression = 'DATE(created_at, "weekday 0", "-6 days")';
      groupBy = 'DATE(created_at, "weekday 0", "-6 days")';
    } else if (interval === 'month') {
      // Group by month
      dateExpression = 'DATE(created_at, "start of month")';
      groupBy = 'DATE(created_at, "start of month")';
    }

    const query = `
      SELECT
        ${dateExpression} as date,
        COUNT(CASE WHEN (type = 'open' OR opened_at IS NOT NULL) THEN 1 END) as opens,
        COUNT(CASE WHEN (type = 'click' OR clicked_at IS NOT NULL) THEN 1 END) as clicks,
        COUNT(CASE WHEN (type = 'email_sent' OR type = 'email' OR status = 'sent' OR status = 'delivered') THEN 1 END) as sent
      FROM interactions
      ${whereClause}
      GROUP BY ${groupBy}
      ORDER BY date ASC
    `;

    console.log('📊 Executing timeline query:', query);
    console.log('📊 Query parameters:', params);

    db.all(query, params, (err, rows) => {
      console.log('📊 Timeline query executed');
      console.log('📊 Timeline error:', err);
      console.log('📊 Timeline rows:', rows);
      if (err) {
        console.error('❌ Error getting timeline data:', err);
        return res.status(500).json({
          success: false,
          error: 'Failed to get timeline data',
          message_ar: 'فشل في جلب بيانات الجدول الزمني'
        });
      }

      console.log('📊 Timeline data result:', rows);
      console.log('📊 Timeline data length:', rows.length);

      res.json({
        success: true,
        data: rows,
        message: 'Timeline data retrieved successfully',
        message_ar: 'تم استرجاع بيانات الجدول الزمني بنجاح'
      });
    });

  } catch (error) {
    console.error('Error getting timeline data:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get timeline data',
      message_ar: 'فشل في جلب بيانات الجدول الزمني'
    });
  }
});

// Get interaction by ID
router.get('/:id', async (req, res) => {
  try {
    const interaction = await Interaction.getById(req.params.id);
    
    if (!interaction) {
      return res.status(404).json({
        success: false,
        error: 'Interaction not found',
        message_ar: 'التفاعل غير موجود'
      });
    }

    res.json({
      success: true,
      data: interaction,
      message: 'Interaction retrieved successfully',
      message_ar: 'تم استرجاع التفاعل بنجاح'
    });
  } catch (error) {
    console.error('Error fetching interaction:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch interaction',
      message_ar: 'فشل في استرجاع التفاعل'
    });
  }
});

// Create new interaction
router.post('/', async (req, res) => {
  try {
    const {
      client_id, message_id, type, status, sent_at, opened_at,
      clicked_at, bounced_at, error_message, metadata
    } = req.body;

    // Validate required fields
    if (!client_id || !message_id || !type) {
      return res.status(400).json({
        success: false,
        error: 'Client ID, Message ID, and Type are required',
        message_ar: 'معرف العميل ومعرف الرسالة والنوع مطلوبة'
      });
    }

    const interaction = await Interaction.create({
      client_id, message_id, type, status, sent_at, opened_at,
      clicked_at, bounced_at, error_message, metadata
    });

    res.status(201).json({
      success: true,
      data: interaction,
      message: 'Interaction created successfully',
      message_ar: 'تم إنشاء التفاعل بنجاح'
    });
  } catch (error) {
    console.error('Error creating interaction:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to create interaction',
      message_ar: 'فشل في إنشاء التفاعل'
    });
  }
});

// Update interaction
router.put('/:id', async (req, res) => {
  try {
    const interaction = await Interaction.getById(req.params.id);
    if (!interaction) {
      return res.status(404).json({
        success: false,
        error: 'Interaction not found',
        message_ar: 'التفاعل غير موجود'
      });
    }

    const {
      status, sent_at, opened_at, clicked_at, bounced_at, error_message, metadata
    } = req.body;

    const result = await Interaction.update(req.params.id, {
      status, sent_at, opened_at, clicked_at, bounced_at, error_message, metadata
    });

    res.json({
      success: true,
      data: result,
      message: 'Interaction updated successfully',
      message_ar: 'تم تحديث التفاعل بنجاح'
    });
  } catch (error) {
    console.error('Error updating interaction:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to update interaction',
      message_ar: 'فشل في تحديث التفاعل'
    });
  }
});

// Delete interaction
router.delete('/:id', async (req, res) => {
  try {
    const interaction = await Interaction.getById(req.params.id);
    if (!interaction) {
      return res.status(404).json({
        success: false,
        error: 'Interaction not found',
        message_ar: 'التفاعل غير موجود'
      });
    }

    const result = await Interaction.delete(req.params.id);

    res.json({
      success: true,
      data: result,
      message: 'Interaction deleted successfully',
      message_ar: 'تم حذف التفاعل بنجاح'
    });
  } catch (error) {
    console.error('Error deleting interaction:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to delete interaction',
      message_ar: 'فشل في حذف التفاعل'
    });
  }
});

// Track email open (pixel tracking)
router.get('/track/open/:client_id/:message_id', async (req, res) => {
  try {
    const { client_id, message_id } = req.params;
    
    // Record the open interaction
    await Interaction.recordOpened(
      parseInt(client_id),
      parseInt(message_id),
      JSON.stringify({
        user_agent: req.get('User-Agent'),
        ip: req.ip,
        timestamp: new Date().toISOString()
      })
    );

    // Return 1x1 transparent pixel
    const pixel = Buffer.from(
      'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==',
      'base64'
    );

    res.set({
      'Content-Type': 'image/png',
      'Content-Length': pixel.length,
      'Cache-Control': 'no-cache, no-store, must-revalidate',
      'Pragma': 'no-cache',
      'Expires': '0'
    });

    res.send(pixel);
  } catch (error) {
    console.error('Error tracking email open:', error);
    // Still return pixel even if tracking fails
    const pixel = Buffer.from(
      'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==',
      'base64'
    );
    res.set('Content-Type', 'image/png');
    res.send(pixel);
  }
});

// Track email click
router.get('/track/click/:client_id/:message_id', async (req, res) => {
  try {
    const { client_id, message_id } = req.params;
    const { url } = req.query;
    
    // Record the click interaction
    await Interaction.recordClicked(
      parseInt(client_id),
      parseInt(message_id),
      JSON.stringify({
        clicked_url: url,
        user_agent: req.get('User-Agent'),
        ip: req.ip,
        timestamp: new Date().toISOString()
      })
    );

    // Redirect to the original URL
    if (url) {
      res.redirect(decodeURIComponent(url));
    } else {
      res.status(400).json({
        success: false,
        error: 'URL parameter is required',
        message_ar: 'معامل الرابط مطلوب'
      });
    }
  } catch (error) {
    console.error('Error tracking email click:', error);
    // Redirect anyway if URL is provided
    if (req.query.url) {
      res.redirect(decodeURIComponent(req.query.url));
    } else {
      res.status(500).json({
        success: false,
        error: 'Failed to track click',
        message_ar: 'فشل في تتبع النقرة'
      });
    }
  }
});

// Get interaction statistics
router.get('/stats/overview', async (req, res) => {
  try {
    const filters = {
      date_from: req.query.date_from,
      date_to: req.query.date_to,
      message_id: req.query.message_id,
      client_id: req.query.client_id
    };

    console.log('📊 Getting interaction statistics with filters:', filters);

    const stats = await Interaction.getStatistics(filters);

    console.log('📈 Raw statistics:', stats);

    // Calculate rates
    const openRate = stats.sent > 0 ? ((stats.opened / stats.sent) * 100).toFixed(2) : 0;
    const clickRate = stats.sent > 0 ? ((stats.clicked / stats.sent) * 100).toFixed(2) : 0;
    const bounceRate = stats.sent > 0 ? ((stats.bounced / stats.sent) * 100).toFixed(2) : 0;

    const result = {
      ...stats,
      rates: {
        open_rate: parseFloat(openRate),
        click_rate: parseFloat(clickRate),
        bounce_rate: parseFloat(bounceRate)
      }
    };

    console.log('📊 Final statistics result:', result);

    res.json({
      success: true,
      data: result,
      message: 'Statistics retrieved successfully',
      message_ar: 'تم استرجاع الإحصائيات بنجاح'
    });
  } catch (error) {
    console.error('Error fetching statistics:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch statistics',
      message_ar: 'فشل في استرجاع الإحصائيات'
    });
  }
});

// Get interactions by client
router.get('/client/:client_id', async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const filters = {
      client_id: req.params.client_id,
      type: req.query.type,
      status: req.query.status,
      date_from: req.query.date_from,
      date_to: req.query.date_to
    };

    const result = await Interaction.getAll(page, limit, filters);
    
    res.json({
      success: true,
      data: result,
      message: 'Client interactions retrieved successfully',
      message_ar: 'تم استرجاع تفاعلات العميل بنجاح'
    });
  } catch (error) {
    console.error('Error fetching client interactions:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch client interactions',
      message_ar: 'فشل في استرجاع تفاعلات العميل'
    });
  }
});

// Get interactions by message
router.get('/message/:message_id', async (req, res) => {
  try {
    console.log('📧 Getting interactions for message:', req.params.message_id);
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const filters = {
      message_id: req.params.message_id,
      type: req.query.type,
      status: req.query.status,
      date_from: req.query.date_from,
      date_to: req.query.date_to
    };
    console.log('📧 Message interactions filters:', filters);

    const result = await Interaction.getAll(page, limit, filters);
    console.log('📧 Message interactions result:', result);
    
    res.json({
      success: true,
      data: result,
      message: 'Message interactions retrieved successfully',
      message_ar: 'تم استرجاع تفاعلات الرسالة بنجاح'
    });
  } catch (error) {
    console.error('Error fetching message interactions:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch message interactions',
      message_ar: 'فشل في استرجاع تفاعلات الرسالة'
    });
  }
});

module.exports = router;
