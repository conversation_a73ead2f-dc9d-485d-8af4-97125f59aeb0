// API Helper functions for making HTTP requests
const API_BASE_URL = 'http://localhost:5000/api';

// Request deduplication
const pendingRequests = new Map();

// Clean up old pending requests periodically
setInterval(() => {
  const now = Date.now();
  for (const [key, request] of pendingRequests.entries()) {
    // Remove requests older than 30 seconds
    if (request.timestamp && now - request.timestamp > 30000) {
      pendingRequests.delete(key);
    }
  }
}, 10000); // Clean every 10 seconds

const createRequestKey = (url, method, body) => {
  // Create a more specific key that includes timestamp for GET requests to avoid over-caching
  const bodyStr = body ? JSON.stringify(body) : '';
  const key = `${method}:${url}:${bodyStr}`;

  // For GET requests, add a short-lived cache key to prevent rapid duplicates
  if (method === 'GET') {
    const now = Date.now();
    const cacheWindow = Math.floor(now / 500); // 500ms window
    return `${key}:${cacheWindow}`;
  }

  return key;
};

// Get auth token from localStorage
const getAuthToken = () => {
  return localStorage.getItem('token');
};

// Create headers with auth token
const createHeaders = (additionalHeaders = {}) => {
  const token = getAuthToken();
  const headers = {
    'Content-Type': 'application/json',
    ...additionalHeaders
  };
  
  if (token) {
    headers.Authorization = `Bearer ${token}`;
  }
  
  return headers;
};

// Handle API response
const handleResponse = async (response) => {
  const contentType = response.headers.get('content-type');
  
  if (contentType && contentType.includes('application/json')) {
    const data = await response.json();
    
    if (!response.ok) {
      throw new Error(data.error || data.message || `HTTP error! status: ${response.status}`);
    }
    
    return data;
  } else {
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    
    return { success: true };
  }
};

// Generic request function
const makeRequest = async (url, options = {}) => {
  try {
    const fullUrl = url.startsWith('http') ? url : `${API_BASE_URL}${url}`;
    const method = options.method || 'GET';
    const requestKey = createRequestKey(fullUrl, method, options.body);

    // Check if this exact request is already pending
    if (pendingRequests.has(requestKey)) {
      const existingRequest = pendingRequests.get(requestKey);
      console.log('⚠️ Duplicate request detected, waiting for existing request:', requestKey);
      return await existingRequest.promise;
    }

    const config = {
      headers: createHeaders(options.headers),
      ...options
    };

    // Create and store the promise with timestamp
    const requestPromise = fetch(fullUrl, config).then(handleResponse);
    pendingRequests.set(requestKey, {
      promise: requestPromise,
      timestamp: Date.now()
    });

    try {
      const result = await requestPromise;
      return result;
    } finally {
      // Clean up the pending request
      pendingRequests.delete(requestKey);
    }
  } catch (error) {
    console.error('API Request Error:', error);
    throw error;
  }
};

// API Helper methods
const apiHelpers = {
  // GET request
  get: async (url, options = {}) => {
    return makeRequest(url, {
      method: 'GET',
      ...options
    });
  },

  // POST request
  post: async (url, data = null, options = {}) => {
    const config = {
      method: 'POST',
      ...options
    };
    
    if (data) {
      if (data instanceof FormData) {
        // Don't set Content-Type for FormData, let browser set it
        config.headers = createHeaders({ ...options.headers });
        delete config.headers['Content-Type'];
        config.body = data;
      } else {
        config.headers = createHeaders(options.headers);
        config.body = JSON.stringify(data);
      }
    }
    
    return makeRequest(url, config);
  },

  // PUT request
  put: async (url, data = null, options = {}) => {
    const config = {
      method: 'PUT',
      ...options
    };
    
    if (data) {
      if (data instanceof FormData) {
        config.headers = createHeaders({ ...options.headers });
        delete config.headers['Content-Type'];
        config.body = data;
      } else {
        config.headers = createHeaders(options.headers);
        config.body = JSON.stringify(data);
      }
    }
    
    return makeRequest(url, config);
  },

  // PATCH request
  patch: async (url, data = null, options = {}) => {
    const config = {
      method: 'PATCH',
      ...options
    };
    
    if (data) {
      if (data instanceof FormData) {
        config.headers = createHeaders({ ...options.headers });
        delete config.headers['Content-Type'];
        config.body = data;
      } else {
        config.headers = createHeaders(options.headers);
        config.body = JSON.stringify(data);
      }
    }
    
    return makeRequest(url, config);
  },

  // DELETE request
  delete: async (url, options = {}) => {
    return makeRequest(url, {
      method: 'DELETE',
      ...options
    });
  },

  // Upload file
  upload: async (url, file, additionalData = {}, options = {}) => {
    const formData = new FormData();
    formData.append('file', file);
    
    // Add additional data to FormData
    Object.keys(additionalData).forEach(key => {
      formData.append(key, additionalData[key]);
    });
    
    return apiHelpers.post(url, formData, options);
  },

  // Download file
  download: async (url, filename = null, options = {}) => {
    try {
      const fullUrl = url.startsWith('http') ? url : `${API_BASE_URL}${url}`;
      
      const response = await fetch(fullUrl, {
        method: 'GET',
        headers: createHeaders(options.headers),
        ...options
      });
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const blob = await response.blob();
      
      // Create download link
      const downloadUrl = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = downloadUrl;
      
      // Set filename
      if (filename) {
        link.download = filename;
      } else {
        // Try to get filename from response headers
        const contentDisposition = response.headers.get('content-disposition');
        if (contentDisposition) {
          const filenameMatch = contentDisposition.match(/filename="(.+)"/);
          if (filenameMatch) {
            link.download = filenameMatch[1];
          }
        }
      }
      
      // Trigger download
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      
      // Clean up
      window.URL.revokeObjectURL(downloadUrl);
      
      return { success: true };
    } catch (error) {
      console.error('Download Error:', error);
      throw error;
    }
  },

  // Set auth token
  setAuthToken: (token) => {
    if (token) {
      localStorage.setItem('token', token);
    } else {
      localStorage.removeItem('token');
    }
  },

  // Clear auth token
  clearAuthToken: () => {
    localStorage.removeItem('token');
  },

  // Check if user is authenticated
  isAuthenticated: () => {
    return !!getAuthToken();
  },

  // Get current user info from token (basic JWT decode)
  getCurrentUser: () => {
    const token = getAuthToken();
    if (!token) return null;
    
    try {
      const payload = JSON.parse(atob(token.split('.')[1]));
      return payload;
    } catch (error) {
      console.error('Error decoding token:', error);
      return null;
    }
  }
};

export default apiHelpers;
