import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { 
  Plus, 
  Edit, 
  Trash2, 
  Save, 
  X, 
  Users,
  Star,
  Crown,
  Building,
  User,
  Tag,
  Palette,
  ArrowUp,
  ArrowDown
} from 'lucide-react';
import { toast } from 'react-hot-toast';
import apiHelpers from '../../utils/apiHelpers';
import LoadingSpinner from '../../components/UI/LoadingSpinner';

const ClientCategories = () => {
  const { t, i18n } = useTranslation();
  const isRTL = i18n.language === 'ar';

  const [categories, setCategories] = useState([]);
  const [loading, setLoading] = useState(true);
  const [showAddForm, setShowAddForm] = useState(false);
  const [editingCategory, setEditingCategory] = useState(null);
  const [formData, setFormData] = useState({
    name: '',
    name_ar: '',
    description: '',
    description_ar: '',
    color: '#6b7280',
    icon: 'users',
    sort_order: 0
  });

  const iconOptions = [
    { value: 'users', label: isRTL ? 'مجموعة' : 'Users', icon: Users },
    { value: 'star', label: isRTL ? 'نجمة' : 'Star', icon: Star },
    { value: 'crown', label: isRTL ? 'تاج' : 'Crown', icon: Crown },
    { value: 'building', label: isRTL ? 'مبنى' : 'Building', icon: Building },
    { value: 'user', label: isRTL ? 'شخص' : 'User', icon: User },
    { value: 'tag', label: isRTL ? 'علامة' : 'Tag', icon: Tag }
  ];

  const colorOptions = [
    '#6b7280', '#3b82f6', '#8b5cf6', '#059669', '#dc2626', 
    '#f59e0b', '#ec4899', '#06b6d4', '#84cc16', '#f97316'
  ];

  useEffect(() => {
    fetchCategories();
  }, []);

  const fetchCategories = async () => {
    try {
      setLoading(true);
      const response = await apiHelpers.get('/client-categories');
      if (response.success) {
        setCategories(response.data);
      }
    } catch (error) {
      console.error('Error fetching categories:', error);
      toast.error(
        isRTL 
          ? 'فشل في تحميل الفئات' 
          : 'Failed to load categories'
      );
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!formData.name.trim()) {
      toast.error(
        isRTL 
          ? 'اسم الفئة مطلوب' 
          : 'Category name is required'
      );
      return;
    }

    try {
      const response = editingCategory
        ? await apiHelpers.put(`/client-categories/${editingCategory.id}`, formData)
        : await apiHelpers.post('/client-categories', formData);

      if (response.success) {
        toast.success(
          isRTL 
            ? (editingCategory ? 'تم تحديث الفئة بنجاح' : 'تم إنشاء الفئة بنجاح')
            : (editingCategory ? 'Category updated successfully' : 'Category created successfully')
        );
        
        resetForm();
        fetchCategories();
      }
    } catch (error) {
      console.error('Error saving category:', error);
      toast.error(
        isRTL 
          ? 'فشل في حفظ الفئة' 
          : 'Failed to save category'
      );
    }
  };

  const handleEdit = (category) => {
    setEditingCategory(category);
    setFormData({
      name: category.name || '',
      name_ar: category.name_ar || '',
      description: category.description || '',
      description_ar: category.description_ar || '',
      color: category.color || '#6b7280',
      icon: category.icon || 'users',
      sort_order: category.sort_order || 0
    });
    setShowAddForm(true);
  };

  const handleDelete = async (categoryId) => {
    if (!window.confirm(
      isRTL 
        ? 'هل أنت متأكد من حذف هذه الفئة؟' 
        : 'Are you sure you want to delete this category?'
    )) {
      return;
    }

    try {
      const response = await apiHelpers.delete(`/client-categories/${categoryId}`);
      if (response.success) {
        toast.success(
          isRTL 
            ? 'تم حذف الفئة بنجاح' 
            : 'Category deleted successfully'
        );
        fetchCategories();
      }
    } catch (error) {
      console.error('Error deleting category:', error);
      const errorMessage = error.response?.data?.error || error.message;
      toast.error(
        isRTL 
          ? `فشل في حذف الفئة: ${errorMessage}` 
          : `Failed to delete category: ${errorMessage}`
      );
    }
  };

  const resetForm = () => {
    setFormData({
      name: '',
      name_ar: '',
      description: '',
      description_ar: '',
      color: '#6b7280',
      icon: 'users',
      sort_order: 0
    });
    setEditingCategory(null);
    setShowAddForm(false);
  };

  const moveCategory = async (categoryId, direction) => {
    const currentIndex = categories.findIndex(cat => cat.id === categoryId);
    if (
      (direction === 'up' && currentIndex === 0) ||
      (direction === 'down' && currentIndex === categories.length - 1)
    ) {
      return;
    }

    const newCategories = [...categories];
    const targetIndex = direction === 'up' ? currentIndex - 1 : currentIndex + 1;
    
    // Swap categories
    [newCategories[currentIndex], newCategories[targetIndex]] = 
    [newCategories[targetIndex], newCategories[currentIndex]];

    // Update sort orders
    const reorderData = newCategories.map((cat, index) => ({
      id: cat.id,
      sort_order: index + 1
    }));

    try {
      const response = await apiHelpers.post('/client-categories/reorder', {
        categories: reorderData
      });

      if (response.success) {
        setCategories(newCategories);
        toast.success(
          isRTL 
            ? 'تم إعادة ترتيب الفئات بنجاح' 
            : 'Categories reordered successfully'
        );
      }
    } catch (error) {
      console.error('Error reordering categories:', error);
      toast.error(
        isRTL 
          ? 'فشل في إعادة ترتيب الفئات' 
          : 'Failed to reorder categories'
      );
    }
  };

  const getIconComponent = (iconName) => {
    const iconOption = iconOptions.find(opt => opt.value === iconName);
    return iconOption ? iconOption.icon : Users;
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 p-6">
        <div className="max-w-4xl mx-auto">
          <div className="flex items-center justify-center py-20">
            <LoadingSpinner size="lg" />
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 mb-2">
                {isRTL ? 'إدارة فئات العملاء' : 'Client Categories Management'}
              </h1>
              <p className="text-gray-600">
                {isRTL 
                  ? 'إدارة وتنظيم فئات العملاء المختلفة'
                  : 'Manage and organize different client categories'
                }
              </p>
            </div>
            <button
              onClick={() => setShowAddForm(true)}
              className="flex items-center space-x-2 rtl:space-x-reverse px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
            >
              <Plus className="h-4 w-4" />
              <span>{isRTL ? 'إضافة فئة' : 'Add Category'}</span>
            </button>
          </div>
        </div>

        {/* Add/Edit Form */}
        {showAddForm && (
          <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-medium text-gray-900">
                {editingCategory 
                  ? (isRTL ? 'تعديل الفئة' : 'Edit Category')
                  : (isRTL ? 'إضافة فئة جديدة' : 'Add New Category')
                }
              </h3>
              <button
                onClick={resetForm}
                className="text-gray-400 hover:text-gray-600"
              >
                <X className="h-5 w-5" />
              </button>
            </div>

            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* English Name */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    {isRTL ? 'الاسم (إنجليزي) *' : 'Name (English) *'}
                  </label>
                  <input
                    type="text"
                    value={formData.name}
                    onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder={isRTL ? 'أدخل الاسم بالإنجليزية' : 'Enter name in English'}
                    required
                  />
                </div>

                {/* Arabic Name */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    {isRTL ? 'الاسم (عربي)' : 'Name (Arabic)'}
                  </label>
                  <input
                    type="text"
                    value={formData.name_ar}
                    onChange={(e) => setFormData({ ...formData, name_ar: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder={isRTL ? 'أدخل الاسم بالعربية' : 'Enter name in Arabic'}
                  />
                </div>

                {/* English Description */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    {isRTL ? 'الوصف (إنجليزي)' : 'Description (English)'}
                  </label>
                  <textarea
                    value={formData.description}
                    onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                    rows={2}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder={isRTL ? 'أدخل الوصف بالإنجليزية' : 'Enter description in English'}
                  />
                </div>

                {/* Arabic Description */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    {isRTL ? 'الوصف (عربي)' : 'Description (Arabic)'}
                  </label>
                  <textarea
                    value={formData.description_ar}
                    onChange={(e) => setFormData({ ...formData, description_ar: e.target.value })}
                    rows={2}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder={isRTL ? 'أدخل الوصف بالعربية' : 'Enter description in Arabic'}
                  />
                </div>

                {/* Icon */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    {isRTL ? 'الأيقونة' : 'Icon'}
                  </label>
                  <select
                    value={formData.icon}
                    onChange={(e) => setFormData({ ...formData, icon: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    {iconOptions.map(option => (
                      <option key={option.value} value={option.value}>
                        {option.label}
                      </option>
                    ))}
                  </select>
                </div>

                {/* Color */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    {isRTL ? 'اللون' : 'Color'}
                  </label>
                  <div className="flex items-center space-x-2 rtl:space-x-reverse">
                    <input
                      type="color"
                      value={formData.color}
                      onChange={(e) => setFormData({ ...formData, color: e.target.value })}
                      className="w-12 h-10 border border-gray-300 rounded cursor-pointer"
                    />
                    <div className="flex flex-wrap gap-1">
                      {colorOptions.map(color => (
                        <button
                          key={color}
                          type="button"
                          onClick={() => setFormData({ ...formData, color })}
                          className={`w-6 h-6 rounded border-2 hover:scale-110 transition-transform ${
                            formData.color === color ? 'border-gray-800 ring-2 ring-blue-500' : 'border-gray-300'
                          }`}
                          style={{ backgroundColor: color, minWidth: '24px', minHeight: '24px' }}
                          title={color}
                        />
                      ))}
                    </div>
                  </div>
                </div>
              </div>

              {/* Submit Buttons */}
              <div className="flex items-center justify-end space-x-4 rtl:space-x-reverse pt-4 border-t border-gray-200">
                <button
                  type="button"
                  onClick={resetForm}
                  className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
                >
                  {isRTL ? 'إلغاء' : 'Cancel'}
                </button>
                <button
                  type="submit"
                  className="flex items-center space-x-2 rtl:space-x-reverse px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                >
                  <Save className="h-4 w-4" />
                  <span>
                    {editingCategory 
                      ? (isRTL ? 'تحديث' : 'Update')
                      : (isRTL ? 'حفظ' : 'Save')
                    }
                  </span>
                </button>
              </div>
            </form>
          </div>
        )}

        {/* Categories List */}
        <div className="bg-white rounded-lg shadow-sm overflow-hidden">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-medium text-gray-900">
              {isRTL ? 'قائمة الفئات' : 'Categories List'}
            </h3>
          </div>

          {categories.length === 0 ? (
            <div className="text-center py-12">
              <Tag className="h-12 w-12 mx-auto text-gray-300 mb-4" />
              <p className="text-gray-500">
                {isRTL ? 'لا توجد فئات بعد' : 'No categories yet'}
              </p>
            </div>
          ) : (
            <div className="divide-y divide-gray-200">
              {categories.map((category, index) => {
                const IconComponent = getIconComponent(category.icon);
                return (
                  <div key={category.id} className="px-6 py-4 flex items-center justify-between hover:bg-gray-50">
                    <div className="flex items-center space-x-4 rtl:space-x-reverse">
                      <div 
                        className="w-10 h-10 rounded-full flex items-center justify-center text-white"
                        style={{ backgroundColor: category.color }}
                      >
                        <IconComponent className="h-5 w-5" />
                      </div>
                      <div>
                        <h4 className="font-medium text-gray-900">
                          {isRTL ? (category.name_ar || category.name) : category.name}
                        </h4>
                        {(category.description || category.description_ar) && (
                          <p className="text-sm text-gray-500">
                            {isRTL ? (category.description_ar || category.description) : category.description}
                          </p>
                        )}
                      </div>
                    </div>

                    <div className="flex items-center space-x-2 rtl:space-x-reverse">
                      {/* Move buttons */}
                      <button
                        onClick={() => moveCategory(category.id, 'up')}
                        disabled={index === 0}
                        className="p-1 text-gray-400 hover:text-gray-600 disabled:opacity-50"
                        title={isRTL ? 'تحريك لأعلى' : 'Move up'}
                      >
                        <ArrowUp className="h-4 w-4" />
                      </button>
                      <button
                        onClick={() => moveCategory(category.id, 'down')}
                        disabled={index === categories.length - 1}
                        className="p-1 text-gray-400 hover:text-gray-600 disabled:opacity-50"
                        title={isRTL ? 'تحريك لأسفل' : 'Move down'}
                      >
                        <ArrowDown className="h-4 w-4" />
                      </button>

                      {/* Edit button */}
                      <button
                        onClick={() => handleEdit(category)}
                        className="p-1 text-blue-600 hover:text-blue-800"
                        title={isRTL ? 'تعديل' : 'Edit'}
                      >
                        <Edit className="h-4 w-4" />
                      </button>

                      {/* Delete button */}
                      <button
                        onClick={() => handleDelete(category.id)}
                        className="p-1 text-red-600 hover:text-red-800"
                        title={isRTL ? 'حذف' : 'Delete'}
                      >
                        <Trash2 className="h-4 w-4" />
                      </button>
                    </div>
                  </div>
                );
              })}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ClientCategories;
