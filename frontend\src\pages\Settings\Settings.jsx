import React, { useState, useEffect, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Settings as SettingsIcon,
  Globe,
  Shield,
  Database,
  Bell,
  Save,
  TestTube,
  CheckCircle,
  XCircle,
  Palette
} from 'lucide-react';
import { useForm } from 'react-hook-form';
import toast from 'react-hot-toast';
import LoadingSpinner from '../../components/UI/LoadingSpinner';
import { useTheme } from '../../contexts/ThemeContext';
import { apiHelpers, endpoints } from '../../services/api';

const Settings = () => {
  console.log('🔧 NEW Settings component loaded!');

  const { t, i18n } = useTranslation();
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('general');
  const [testingEmail, setTestingEmail] = useState(false);
  const [emailTestResult, setEmailTestResult] = useState(null);
  const hasLoadedRef = useRef(false);

  const {
    theme,
    fontSize,
    primaryColor,
    borderRadius,
    preferences,
    setTheme,
    setFontSize,
    setPrimaryColor,
    setBorderRadius,
    updatePreferences,
    getAvailableThemes,
    getAvailableFontSizes,
    getAvailableLanguages,
    setLanguage
  } = useTheme();

  const isRTL = i18n.language === 'ar';

  const { register, handleSubmit, watch, setValue, formState: { errors } } = useForm({
    defaultValues: {
      // General settings
      default_language: i18n.language,
      theme: theme,
      font_size: fontSize,
      items_per_page: 10,

      // Appearance settings
      primary_color: primaryColor,
      border_radius: borderRadius,

      // Notification settings
      email_notifications: preferences.notifications,
      sound_notifications: preferences.sounds,
      auto_save: preferences.autoSave,
      animations: preferences.animations
    }
  });

  const watchedValues = watch();

  useEffect(() => {
    loadSettings();
  }, []);

  const loadSettings = async () => {
    // Prevent multiple calls
    if (hasLoadedRef.current) return;
    hasLoadedRef.current = true;

    try {
      setLoading(true);

      // Try to load from API first
      try {
        const result = await apiHelpers.get(endpoints.settings.list);

        if (result.success) {
          // Set form values from API data
          Object.entries(result.data).forEach(([key, value]) => {
            setValue(key, value);
          });
          return;
        }
      } catch (apiError) {
        console.warn('API settings not available, using localStorage fallback');
      }

      // Fallback to localStorage
      const savedSettings = localStorage.getItem('app_settings');
      if (savedSettings) {
        const settings = JSON.parse(savedSettings);
        Object.keys(settings).forEach(key => {
          setValue(key, settings[key]);
        });
      }
    } catch (error) {
      console.error('Error loading settings:', error);
      toast.error(t('settings.error_loading_settings') || 'Error loading settings');
    } finally {
      setLoading(false);
    }
  };

  const saveSettings = async (data) => {
    try {
      setLoading(true);
      
      // Try to save to API first, fallback to localStorage
      try {
        const result = await apiHelpers.put(endpoints.settings.update, data);
        if (!result.success) {
          throw new Error('API save failed');
        }
      } catch (apiError) {
        console.warn('API save failed, using localStorage fallback');
        localStorage.setItem('app_settings', JSON.stringify(data));
      }
      
      // Apply theme changes
      if (data.theme !== theme) {
        setTheme(data.theme);
      }
      
      // Apply font size changes
      if (data.font_size !== fontSize) {
        setFontSize(data.font_size);
      }
      
      // Apply language changes
      if (data.default_language !== i18n.language) {
        setLanguage(data.default_language);
      }
      
      // Apply preference changes
      updatePreferences({
        notifications: data.email_notifications,
        sounds: data.sound_notifications,
        autoSave: data.auto_save,
        animations: data.animations
      });
      
      toast.success(t('settings.settings_saved'));
    } catch (error) {
      console.error('Error saving settings:', error);
      toast.error(t('settings.settings_error'));
    } finally {
      setLoading(false);
    }
  };

  const testEmailConnection = async () => {
    if (!watchedValues.test_email) {
      toast.error(t('settings.test_email_required'));
      return;
    }

    try {
      setTestingEmail(true);
      setEmailTestResult(null);

      const result = await apiHelpers.post(endpoints.messages.testEmail, {
        email: watchedValues.test_email
      });

      if (result.success) {
        setEmailTestResult({ success: true, message: t('settings.connection_successful') });
        toast.success(t('settings.connection_successful'));
      } else {
        setEmailTestResult({ success: false, message: result.error || t('settings.connection_failed') });
        toast.error(t('settings.connection_failed'));
      }
    } catch (error) {
      setEmailTestResult({ success: false, message: error.message });
      toast.error(t('settings.connection_failed'));
    } finally {
      setTestingEmail(false);
    }
  };

  const tabs = [
    {
      id: 'general',
      title: t('settings.general_settings'),
      icon: SettingsIcon,
      description: isRTL ? 'الإعدادات العامة للنظام' : 'General system preferences'
    },
    {
      id: 'notifications',
      title: t('settings.notification_settings'),
      icon: Bell,
      description: isRTL ? 'إعدادات الإشعارات' : 'Notification preferences'
    },
    {
      id: 'appearance',
      title: t('settings.appearance_settings'),
      icon: Palette,
      description: isRTL ? 'إعدادات المظهر والألوان' : 'Theme and color settings'
    },
    {
      id: 'language',
      title: t('settings.language_settings'),
      icon: Globe,
      description: isRTL ? 'إعدادات اللغة' : 'Language settings'
    }
  ];

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div>
        <h1 className="text-3xl font-bold text-gray-900">
          {t('settings.title')}
        </h1>
        <p className="mt-2 text-gray-600">
          {isRTL ? 'إدارة إعدادات النظام والتفضيلات' : 'Manage system settings and preferences'}
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Sidebar */}
        <div className="lg:col-span-1">
          <nav className="space-y-1">
            {tabs.map((tab) => {
              const Icon = tab.icon;
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`
                    w-full flex items-center space-x-3 rtl:space-x-reverse px-3 py-2 text-sm font-medium rounded-md transition-colors
                    ${activeTab === tab.id
                      ? 'bg-primary-100 text-primary-700 border-r-2 border-primary-600'
                      : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                    }
                  `}
                >
                  <Icon className="h-5 w-5 flex-shrink-0" />
                  <div className="text-left rtl:text-right">
                    <div className="font-medium">{tab.title}</div>
                    <div className="text-xs text-gray-500 mt-1">{tab.description}</div>
                  </div>
                </button>
              );
            })}
          </nav>
        </div>

        {/* Content */}
        <div className="lg:col-span-3">
          <form onSubmit={handleSubmit(saveSettings)} className="space-y-6">
            {/* General Settings */}
            {activeTab === 'general' && (
              <div className="bg-white rounded-lg shadow-sm p-6">
                <div className="mb-6">
                  <h3 className="text-lg font-medium text-gray-900">
                    {isRTL ? 'الإعدادات العامة' : 'General Settings'}
                  </h3>
                  <p className="text-sm text-gray-600 mt-1">
                    {isRTL ? 'إعدادات عامة للنظام' : 'General system preferences'}
                  </p>
                </div>
                <div className="space-y-4">
                  <div className="text-center py-8 text-gray-500">
                    <SettingsIcon className="h-12 w-12 mx-auto mb-4 opacity-50" />
                    <p>{isRTL ? 'سيتم إضافة الإعدادات العامة لاحقاً' : 'General settings will be added later'}</p>
                  </div>
                </div>
              </div>
            )}

            {/* Notifications Settings */}
            {activeTab === 'notifications' && (
              <div className="bg-white rounded-lg shadow-sm p-6">
                <div className="mb-6">
                  <h3 className="text-lg font-medium text-gray-900">
                    {t('settings.notification_settings')}
                  </h3>
                  <p className="text-sm text-gray-600 mt-1">
                    {isRTL ? 'إدارة الإشعارات والتنبيهات' : 'Manage notifications and alerts'}
                  </p>
                </div>
                <div className="space-y-6">
                  {/* Email Notifications */}
                  <div className="flex items-center justify-between">
                    <div>
                      <label className="text-sm font-medium text-gray-700">
                        {t('settings.email_notifications')}
                      </label>
                      <p className="text-sm text-gray-500">
                        {isRTL ? 'تلقي إشعارات عبر البريد الإلكتروني' : 'Receive notifications via email'}
                      </p>
                    </div>
                    <input
                      type="checkbox"
                      {...register('email_notifications')}
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                  </div>

                  {/* Sound Notifications */}
                  <div className="flex items-center justify-between">
                    <div>
                      <label className="text-sm font-medium text-gray-700">
                        {t('settings.sound_notifications')}
                      </label>
                      <p className="text-sm text-gray-500">
                        {isRTL ? 'تشغيل أصوات للإشعارات' : 'Play sounds for notifications'}
                      </p>
                    </div>
                    <input
                      type="checkbox"
                      {...register('sound_notifications')}
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                  </div>

                  {/* Auto Save */}
                  <div className="flex items-center justify-between">
                    <div>
                      <label className="text-sm font-medium text-gray-700">
                        {t('settings.auto_save')}
                      </label>
                      <p className="text-sm text-gray-500">
                        {isRTL ? 'حفظ التغييرات تلقائياً' : 'Automatically save changes'}
                      </p>
                    </div>
                    <input
                      type="checkbox"
                      {...register('auto_save')}
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                  </div>

                  {/* Animations */}
                  <div className="flex items-center justify-between">
                    <div>
                      <label className="text-sm font-medium text-gray-700">
                        {t('settings.animations')}
                      </label>
                      <p className="text-sm text-gray-500">
                        {isRTL ? 'تفعيل الرسوم المتحركة' : 'Enable animations'}
                      </p>
                    </div>
                    <input
                      type="checkbox"
                      {...register('animations')}
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                  </div>
                </div>
              </div>
            )}

            {/* Appearance Settings */}
            {activeTab === 'appearance' && (
              <div className="bg-white rounded-lg shadow-sm p-6 appearance-settings">
                <div className="mb-6">
                  <h3 className="text-lg font-medium text-gray-900">
                    {t('settings.appearance_settings')}
                  </h3>
                  <p className="text-sm text-gray-600 mt-1">
                    {isRTL ? 'تخصيص مظهر التطبيق والألوان' : 'Customize app appearance and colors'}
                  </p>
                </div>
                <div className="space-y-6">
                  {/* Theme Selection */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-3">
                      {t('settings.theme')}
                    </label>
                    <div className="grid grid-cols-3 gap-3">
                      {[
                        { value: 'light', label: t('settings.light_theme') },
                        { value: 'dark', label: t('settings.dark_theme') },
                        { value: 'auto', label: t('settings.auto_theme') }
                      ].map((themeOption) => (
                        <button
                          key={themeOption.value}
                          type="button"
                          onClick={() => {
                            setValue('theme', themeOption.value);
                            setTheme(themeOption.value);
                          }}
                          className={`p-3 rounded-dynamic border-2 transition-colors theme-transition ${
                            watchedValues.theme === themeOption.value
                              ? 'border-primary bg-blue-50'
                              : 'border-gray-200 hover:border-gray-300'
                          }`}
                        >
                          <div className="text-center">
                            <div className={`w-8 h-8 mx-auto mb-2 rounded-dynamic ${
                              themeOption.value === 'light' ? 'theme-preview-light bg-white border border-gray-300' :
                              themeOption.value === 'dark' ? 'theme-preview-dark bg-gray-800' :
                              'theme-preview-auto bg-gradient-to-br from-blue-500 to-purple-600'
                            }`}></div>
                            <span className="text-sm font-medium">{themeOption.label}</span>
                          </div>
                        </button>
                      ))}
                    </div>
                  </div>

                  {/* Primary Color Selection */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-3">
                      {t('settings.primary_color')}
                    </label>
                    <div className="grid grid-cols-6 gap-3">
                      {[
                        { name: 'Blue', value: 'blue', color: 'bg-blue-500' },
                        { name: 'Green', value: 'green', color: 'bg-green-500' },
                        { name: 'Purple', value: 'purple', color: 'bg-purple-500' },
                        { name: 'Red', value: 'red', color: 'bg-red-500' },
                        { name: 'Orange', value: 'orange', color: 'bg-orange-500' },
                        { name: 'Pink', value: 'pink', color: 'bg-pink-500' },
                        { name: 'Indigo', value: 'indigo', color: 'bg-indigo-500' },
                        { name: 'Teal', value: 'teal', color: 'bg-teal-500' },
                        { name: 'Cyan', value: 'cyan', color: 'bg-cyan-500' },
                        { name: 'Emerald', value: 'emerald', color: 'bg-emerald-500' },
                        { name: 'Lime', value: 'lime', color: 'bg-lime-500' },
                        { name: 'Amber', value: 'amber', color: 'bg-amber-500' }
                      ].map((color) => (
                        <button
                          key={color.value}
                          type="button"
                          onClick={() => {
                            setValue('primary_color', color.value);
                            setPrimaryColor(color.value);
                          }}
                          className={`w-12 h-12 rounded-dynamic ${color.color} border-2 transition-all theme-transition ${
                            watchedValues.primary_color === color.value
                              ? 'border-gray-800 scale-110'
                              : 'border-gray-200 hover:scale-105'
                          }`}
                          title={color.name}
                        ></button>
                      ))}
                    </div>
                  </div>

                  {/* Font Size */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-3">
                      {t('settings.font_size')}
                    </label>
                    <div className="grid grid-cols-3 gap-3">
                      {[
                        { value: 'small', label: t('settings.small_font') },
                        { value: 'medium', label: t('settings.medium_font') },
                        { value: 'large', label: t('settings.large_font') }
                      ].map((sizeOption) => (
                        <button
                          key={sizeOption.value}
                          type="button"
                          onClick={() => {
                            setValue('font_size', sizeOption.value);
                            setFontSize(sizeOption.value);
                          }}
                          className={`p-3 rounded-dynamic border-2 transition-colors theme-transition ${
                            watchedValues.font_size === sizeOption.value
                              ? 'border-primary bg-blue-50'
                              : 'border-gray-200 hover:border-gray-300'
                          }`}
                        >
                          <div className="text-center">
                            <span className={`block mb-1 ${
                              sizeOption.value === 'small' ? 'text-sm' :
                              sizeOption.value === 'large' ? 'text-lg' : 'text-base'
                            }`}>Aa</span>
                            <span className="text-xs text-gray-600">{sizeOption.label}</span>
                          </div>
                        </button>
                      ))}
                    </div>
                  </div>

                  {/* Border Radius */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-3">
                      {t('settings.border_radius')}
                    </label>
                    <div className="grid grid-cols-4 gap-3">
                      {[
                        { name: t('settings.square_corners'), value: 'none', class: 'rounded-none' },
                        { name: t('settings.small_corners'), value: 'sm', class: 'rounded-sm' },
                        { name: t('settings.medium_corners'), value: 'md', class: 'rounded-md' },
                        { name: t('settings.large_corners'), value: 'lg', class: 'rounded-lg' }
                      ].map((radius) => (
                        <button
                          key={radius.value}
                          type="button"
                          onClick={() => {
                            setValue('border_radius', radius.value);
                            setBorderRadius(radius.value);
                          }}
                          className={`p-3 border-2 transition-colors theme-transition ${radius.class} ${
                            watchedValues.border_radius === radius.value
                              ? 'border-primary bg-blue-50'
                              : 'border-gray-200 hover:border-gray-300'
                          }`}
                        >
                          <div className="text-center">
                            <div className={`w-6 h-6 mx-auto mb-1 bg-gray-300 ${radius.class}`}></div>
                            <span className="text-xs">{radius.name}</span>
                          </div>
                        </button>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Language Settings */}
            {activeTab === 'language' && (
              <div className="bg-white rounded-lg shadow-sm p-6">
                <div className="mb-6">
                  <h3 className="text-lg font-medium text-gray-900">
                    {isRTL ? 'إعدادات اللغة' : 'Language Settings'}
                  </h3>
                  <p className="text-sm text-gray-600 mt-1">
                    {isRTL ? 'اختيار لغة التطبيق' : 'Choose application language'}
                  </p>
                </div>
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-3">
                      {isRTL ? 'اللغة' : 'Language'}
                    </label>
                    <div className="grid grid-cols-2 gap-3">
                      {getAvailableLanguages().map((lang) => (
                        <button
                          key={lang.value}
                          type="button"
                          onClick={() => {
                            setValue('default_language', lang.value);
                            setLanguage(lang.value);
                          }}
                          className={`p-4 rounded-lg border-2 transition-colors ${
                            watchedValues.default_language === lang.value
                              ? 'border-blue-500 bg-blue-50'
                              : 'border-gray-200 hover:border-gray-300'
                          }`}
                        >
                          <div className="text-center">
                            <span className="text-2xl mb-2 block">{lang.flag}</span>
                            <span className="text-sm font-medium">{lang.label}</span>
                          </div>
                        </button>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Legacy SMTP Settings (for manual configuration) */}
            {activeTab === 'email' && false && (
              <div className="card">
                <div className="card-header">
                  <h3 className="text-lg font-medium text-gray-900">
                    {t('settings.manual_smtp_configuration')}
                  </h3>
                  <p className="text-sm text-gray-600 mt-1">
                    {isRTL ? 'تكوين SMTP يدوي للإعدادات المتقدمة' : 'Manual SMTP configuration for advanced settings'}
                  </p>
                </div>
                <div className="card-body space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        {t('settings.smtp_host')}
                      </label>
                      <input
                        type="text"
                        className="input"
                        placeholder="smtp.gmail.com"
                        {...register('smtp_host', { required: t('validation.required_field') })}
                      />
                      {errors.smtp_host && (
                        <p className="mt-1 text-sm text-red-600">{errors.smtp_host.message}</p>
                      )}
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        {t('settings.smtp_port')}
                      </label>
                      <input
                        type="number"
                        className="input"
                        placeholder="587"
                        {...register('smtp_port', { required: t('validation.required_field') })}
                      />
                      {errors.smtp_port && (
                        <p className="mt-1 text-sm text-red-600">{errors.smtp_port.message}</p>
                      )}
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      {t('settings.smtp_username')}
                    </label>
                    <input
                      type="email"
                      className="input"
                      placeholder="<EMAIL>"
                      {...register('smtp_username', { required: t('validation.required_field') })}
                    />
                    {errors.smtp_username && (
                      <p className="mt-1 text-sm text-red-600">{errors.smtp_username.message}</p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      {t('settings.smtp_password')}
                    </label>
                    <input
                      type="password"
                      className="input"
                      placeholder="••••••••"
                      {...register('smtp_password', { required: t('validation.required_field') })}
                    />
                    {errors.smtp_password && (
                      <p className="mt-1 text-sm text-red-600">{errors.smtp_password.message}</p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      {t('settings.smtp_encryption')}
                    </label>
                    <select className="input" {...register('smtp_encryption')}>
                      <option value="tls">TLS</option>
                      <option value="ssl">SSL</option>
                      <option value="none">{isRTL ? 'بدون تشفير' : 'None'}</option>
                    </select>
                  </div>

                  {/* Test Email */}
                  <div className="border-t pt-4">
                    <h4 className="text-md font-medium text-gray-900 mb-3">
                      {t('settings.test_connection')}
                    </h4>
                    <div className="flex space-x-3 rtl:space-x-reverse">
                      <div className="flex-1">
                        <input
                          type="email"
                          className="input"
                          placeholder={t('settings.test_email_address')}
                          {...register('test_email')}
                        />
                      </div>
                      <button
                        type="button"
                        onClick={testEmailConnection}
                        disabled={testingEmail}
                        className="btn btn-outline flex items-center space-x-2 rtl:space-x-reverse"
                      >
                        {testingEmail ? (
                          <LoadingSpinner size="small" />
                        ) : (
                          <TestTube className="h-4 w-4" />
                        )}
                        <span>{t('settings.test_connection')}</span>
                      </button>
                    </div>

                    {emailTestResult && (
                      <div className={`mt-3 p-3 rounded-md flex items-center space-x-2 rtl:space-x-reverse ${
                        emailTestResult.success ? 'bg-green-50 text-green-800' : 'bg-red-50 text-red-800'
                      }`}>
                        {emailTestResult.success ? (
                          <CheckCircle className="h-5 w-5" />
                        ) : (
                          <XCircle className="h-5 w-5" />
                        )}
                        <span className="text-sm">{emailTestResult.message}</span>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            )}

            {/* General Settings */}
            {activeTab === 'general' && (
              <div className="card">
                <div className="card-header">
                  <h3 className="text-lg font-medium text-gray-900">
                    {t('settings.general_settings')}
                  </h3>
                </div>
                <div className="card-body space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      {t('settings.items_per_page')}
                    </label>
                    <select className="input" {...register('items_per_page')}>
                      <option value={10}>10</option>
                      <option value={25}>25</option>
                      <option value={50}>50</option>
                      <option value={100}>100</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      {t('settings.timezone')}
                    </label>
                    <select className="input" {...register('timezone')}>
                      <option value="Asia/Riyadh">{isRTL ? 'الرياض (GMT+3)' : 'Riyadh (GMT+3)'}</option>
                      <option value="UTC">{isRTL ? 'التوقيت العالمي (UTC)' : 'UTC'}</option>
                      <option value="America/New_York">{isRTL ? 'نيويورك (GMT-5)' : 'New York (GMT-5)'}</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      {t('settings.date_format')}
                    </label>
                    <select className="input" {...register('date_format')}>
                      <option value="DD/MM/YYYY">DD/MM/YYYY</option>
                      <option value="MM/DD/YYYY">MM/DD/YYYY</option>
                      <option value="YYYY-MM-DD">YYYY-MM-DD</option>
                    </select>
                  </div>
                </div>
              </div>
            )}

            {/* Notification Settings */}
            {activeTab === 'notifications' && (
              <div className="card">
                <div className="card-header">
                  <h3 className="text-lg font-medium text-gray-900">
                    {t('settings.notification_settings')}
                  </h3>
                </div>
                <div className="card-body space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <h4 className="text-sm font-medium text-gray-900">
                        {isRTL ? 'إشعارات البريد الإلكتروني' : 'Email Notifications'}
                      </h4>
                      <p className="text-sm text-gray-500">
                        {isRTL ? 'تلقي إشعارات عبر البريد الإلكتروني' : 'Receive email notifications'}
                      </p>
                    </div>
                    <input
                      type="checkbox"
                      className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                      {...register('email_notifications')}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <h4 className="text-sm font-medium text-gray-900">
                        {isRTL ? 'الأصوات' : 'Sound Notifications'}
                      </h4>
                      <p className="text-sm text-gray-500">
                        {isRTL ? 'تشغيل الأصوات للإشعارات' : 'Play sounds for notifications'}
                      </p>
                    </div>
                    <input
                      type="checkbox"
                      className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                      {...register('sound_notifications')}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <h4 className="text-sm font-medium text-gray-900">
                        {t('settings.auto_save')}
                      </h4>
                      <p className="text-sm text-gray-500">
                        {isRTL ? 'حفظ تلقائي للتغييرات' : 'Automatically save changes'}
                      </p>
                    </div>
                    <input
                      type="checkbox"
                      className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                      {...register('auto_save')}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <h4 className="text-sm font-medium text-gray-900">
                        {isRTL ? 'الرسوم المتحركة' : 'Animations'}
                      </h4>
                      <p className="text-sm text-gray-500">
                        {isRTL ? 'تفعيل الرسوم المتحركة' : 'Enable interface animations'}
                      </p>
                    </div>
                    <input
                      type="checkbox"
                      className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                      {...register('animations')}
                    />
                  </div>
                </div>
              </div>
            )}

            {/* Language Settings */}
            {activeTab === 'language' && (
              <div className="card">
                <div className="card-header">
                  <h3 className="text-lg font-medium text-gray-900">
                    {t('settings.language_settings')}
                  </h3>
                </div>
                <div className="card-body space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      {t('settings.default_language')}
                    </label>
                    <select className="input" {...register('default_language')}>
                      {getAvailableLanguages().map((lang) => (
                        <option key={lang.value} value={lang.value}>
                          {lang.flag} {lang.label}
                        </option>
                      ))}
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      {isRTL ? 'المظهر' : 'Theme'}
                    </label>
                    <select className="input" {...register('theme')}>
                      {getAvailableThemes().map((theme) => (
                        <option key={theme.value} value={theme.value}>
                          {isRTL ? theme.label_ar : theme.label}
                        </option>
                      ))}
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      {isRTL ? 'حجم الخط' : 'Font Size'}
                    </label>
                    <select className="input" {...register('font_size')}>
                      {getAvailableFontSizes().map((size) => (
                        <option key={size.value} value={size.value}>
                          {isRTL ? size.label_ar : size.label}
                        </option>
                      ))}
                    </select>
                  </div>
                </div>
              </div>
            )}

            {/* Save Button */}
            <div className="flex justify-end">
              <button
                type="submit"
                disabled={loading}
                className="btn btn-primary flex items-center space-x-2 rtl:space-x-reverse"
              >
                {loading ? (
                  <LoadingSpinner size="small" />
                ) : (
                  <Save className="h-4 w-4" />
                )}
                <span>{t('settings.save_settings')}</span>
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default Settings;
