# دليل البدء السريع | Quick Start Guide

## التثبيت السريع | Quick Installation

### 1. تحميل المشروع | Download Project
```bash
git clone <repository-url>
cd "Marketing Email"
```

### 2. تثبيت جميع التبعيات | Install All Dependencies
```bash
npm run install-all
```

### 3. إعداد البريد الإلكتروني | Setup Email Configuration
```bash
# عدل ملف .env في مجلد backend | Edit .env file in backend folder
# أضف بريدك الإلكتروني وكلمة المرور | Add your email and password

SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
```

**🎯 النظام يدعم جميع مزودي البريد تلقائياً:**
**🎯 System supports all email providers automatically:**

- **Gmail**: يتطلب App Password | Requires App Password
- **Outlook/Hotmail**: يتطلب App Password | Requires App Password
- **Yahoo**: يتطلب App Password | Requires App Password
- **iCloud**: يتطلب App Password | Requires App Password
- **Zoho**: كلمة المرور العادية | Regular password
- **وأكثر...** | **And more...**

### 4. تشغيل النظام | Run System

#### الطريقة السهلة (Windows) | Easy Way (Windows):
```bash
# تشغيل ملف batch | Run batch file
start-dev.bat

# أو PowerShell | Or PowerShell
.\start-dev.ps1
```

#### الطريقة اليدوية | Manual Way:
```bash
# Terminal 1 - Backend
cd backend
npm install
npm run dev

# Terminal 2 - Frontend (في terminal منفصل | In separate terminal)
cd frontend
npm install
npm run dev
```

#### باستخدام concurrently | Using concurrently:
```bash
npm install concurrently
npm run dev-both
```

## الوصول للنظام | Access System

- **Frontend**: http://localhost:3000
- **Backend API**: http://localhost:5000

## بيانات الدخول | Login Credentials

```
Username: admin
Password: admin123
```

## 🚀 إعداد البريد الإلكتروني السريع | Quick Email Setup

### ✨ إعداد تلقائي (مستحسن) | Auto Setup (Recommended)
```env
# فقط أضف بريدك وكلمة المرور - النظام سيكتشف الباقي تلقائياً
# Just add your email and password - system will auto-detect the rest

SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
```

### 📧 كيفية الحصول على App Password | How to Get App Password

#### Gmail:
1. اذهب إلى: https://myaccount.google.com/security
2. فعل "2-Step Verification"
3. اختر "App passwords"
4. أنشئ كلمة مرور جديدة للتطبيق

#### Outlook:
1. اذهب إلى: https://account.microsoft.com/security
2. فعل "Two-step verification"
3. اختر "App passwords"
4. أنشئ كلمة مرور جديدة للتطبيق

### 🔧 إعداد يدوي (للحالات الخاصة) | Manual Setup (Special Cases)
```env
SMTP_HOST=smtp.your-provider.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-password
```

## الأوامر المفيدة | Useful Commands

```bash
# تثبيت التبعيات | Install dependencies
npm run install-all

# تشغيل التطوير | Run development
npm run dev

# بناء للإنتاج | Build for production
npm run build

# تشغيل الإنتاج | Run production
npm start

# تنظيف الملفات | Clean files
npm run clean

# إعداد قاعدة البيانات | Setup database
npm run setup-db
```

## استكشاف الأخطاء السريع | Quick Troubleshooting

### خطأ في المنفذ | Port Error
```bash
# تغيير منفذ Backend | Change Backend port
# في ملف backend/.env | In backend/.env file
PORT=5001
```

### خطأ في قاعدة البيانات | Database Error
```bash
cd backend
npm run init-db
```

### خطأ في التبعيات | Dependencies Error
```bash
npm run clean
npm run install-all
```

## الخطوات التالية | Next Steps

1. **إضافة عملاء** | Add Clients
   - انتقل لقسم العملاء | Go to Clients section
   - أضف عملاء جدد أو استورد من Excel | Add new clients or import from Excel

2. **إنشاء رسائل** | Create Messages
   - انتقل لقسم الرسائل | Go to Messages section
   - أنشئ رسالة جديدة | Create new message

3. **إرسال حملة** | Send Campaign
   - اختر الرسالة والعملاء | Select message and clients
   - أرسل أو جدول الإرسال | Send or schedule

4. **مراجعة التقارير** | Review Reports
   - انتقل لقسم التقارير | Go to Reports section
   - اعرض الإحصائيات | View statistics

## الدعم | Support

للحصول على المساعدة:
For help:

- 📖 اقرأ الـ README الكامل | Read full README
- 🐛 أبلغ عن مشكلة | Report issue on GitHub
- 📧 راسلنا | Email us: <EMAIL>
