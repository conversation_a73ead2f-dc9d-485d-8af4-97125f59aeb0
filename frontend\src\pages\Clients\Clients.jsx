import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import {
  Plus,
  Search,
  Filter,
  Download,
  Upload,
  MoreVertical,
  Edit,
  Trash2,
  Eye,
  Mail,
  Users
} from 'lucide-react';
import { useForm } from 'react-hook-form';
import toast from 'react-hot-toast';
import LoadingSpinner from '../../components/UI/LoadingSpinner';
import apiHelpers from '../../utils/apiHelpers';

const Clients = () => {
  const { t, i18n } = useTranslation();
  const navigate = useNavigate();
  const [clients, setClients] = useState([]);
  const [loading, setLoading] = useState(true);
  const [totalPages, setTotalPages] = useState(1);
  const [currentPage, setCurrentPage] = useState(1);
  const [showFilters, setShowFilters] = useState(false);
  const [showImportModal, setShowImportModal] = useState(false);
  const [selectedClients, setSelectedClients] = useState([]);
  const [actionMenuOpen, setActionMenuOpen] = useState(null);

  const isRTL = i18n.language === 'ar';

  const { register, watch, reset } = useForm({
    defaultValues: {
      search: '',
      category: '',
      language: '',
      status: ''
    }
  });

  const filters = watch();

  // Load clients when page changes
  useEffect(() => {
    console.log('📋 Loading clients - Page:', currentPage);
    loadClients();
  }, [currentPage]);

  // Load clients when filters change (with debounce)
  useEffect(() => {
    const hasFilters = filters.search || filters.category || filters.language || filters.status;

    if (hasFilters) {
      const timeoutId = setTimeout(() => {
        console.log('📋 Loading clients with filters');
        setCurrentPage(1); // This will trigger the first useEffect
      }, 300);
      return () => clearTimeout(timeoutId);
    }
  }, [filters.search, filters.category, filters.language, filters.status]);

  const loadClients = async () => {
    try {
      if (loading && clients.length > 0) {
        console.log('⚠️ Skipping duplicate request - already loading clients');
        return;
      }
      setLoading(true);
      console.log('🔄 Loading clients from API...');
      const params = new URLSearchParams({
        page: currentPage,
        limit: 10,
        search: filters.search || '',
        status: filters.status || '',
        interest_level: '',
        sort_by: 'created_at',
        sort_order: 'DESC'
      });

      const result = await apiHelpers.get(`/clients?${params}`);

      if (result.success) {
        setClients(result.data.clients || []);
        setTotalPages(result.data.pagination?.total_pages || 1);
        console.log(`✅ Loaded ${result.data.clients?.length || 0} clients`);
      }
    } catch (error) {
      console.error('Error loading clients:', error);
      toast.error(isRTL ? 'فشل في تحميل العملاء' : 'Failed to load clients');
    } finally {
      setLoading(false);
    }
  };

  const createSampleData = async () => {
    try {
      const response = await apiHelpers.post('/clients/seed-sample-data');
      if (response.success) {
        toast.success(
          isRTL
            ? response.message_ar || 'تم إنشاء البيانات التجريبية بنجاح'
            : response.message || 'Sample data created successfully'
        );
        loadClients(); // Refresh the list
      }
    } catch (error) {
      console.error('Error creating sample data:', error);
      toast.error(
        isRTL
          ? 'فشل في إنشاء البيانات التجريبية'
          : 'Failed to create sample data'
      );
    }
  };

  const exportClients = async () => {
    try {
      const response = await apiHelpers.get('/clients/export');
      if (response.success) {
        // Create CSV content
        const csvContent = convertToCSV(response.data.clients);
        downloadCSV(csvContent, 'clients-export.csv');
        toast.success(
          isRTL
            ? 'تم تصدير العملاء بنجاح'
            : 'Clients exported successfully'
        );
      }
    } catch (error) {
      console.error('Error exporting clients:', error);
      toast.error(
        isRTL
          ? 'فشل في تصدير العملاء'
          : 'Failed to export clients'
      );
    }
  };

  const convertToCSV = (data) => {
    const headers = ['Name', 'Email', 'Phone', 'Company', 'Status', 'Interest Level', 'Created At'];
    const csvRows = [headers.join(',')];

    data.forEach(client => {
      const row = [
        `"${client.name || ''}"`,
        `"${client.email || ''}"`,
        `"${client.phone || ''}"`,
        `"${client.company || ''}"`,
        `"${client.status || ''}"`,
        `"${client.interest_level || ''}"`,
        `"${client.created_at || ''}"`
      ];
      csvRows.push(row.join(','));
    });

    return csvRows.join('\n');
  };

  const downloadCSV = (csvContent, filename) => {
    // Add BOM for proper UTF-8 encoding
    const BOM = '\uFEFF';
    const blob = new Blob([BOM + csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', filename);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  };

  const handleImportClients = async (event) => {
    const file = event.target.files[0];
    if (!file) return;

    const validTypes = [
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // .xlsx
      'application/vnd.ms-excel', // .xls
      'text/csv' // .csv
    ];

    const validExtensions = ['.xlsx', '.xls', '.csv'];
    const fileExtension = file.name.toLowerCase().substring(file.name.lastIndexOf('.'));

    if (!validTypes.includes(file.type) && !validExtensions.includes(fileExtension)) {
      toast.error(
        isRTL
          ? 'يرجى اختيار ملف Excel أو CSV صحيح'
          : 'Please select a valid Excel or CSV file'
      );
      return;
    }

    try {
      const formData = new FormData();
      formData.append('excel', file);

      const response = await apiHelpers.post('/clients/import', formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });

      if (response.success) {
        toast.success(
          isRTL
            ? `تم استيراد ${response.data.inserted} عميل بنجاح`
            : `Successfully imported ${response.data.inserted} clients`
        );
        loadClients(); // Refresh the list
      }
    } catch (error) {
      console.error('Error importing clients:', error);
      toast.error(
        isRTL
          ? 'فشل في استيراد العملاء'
          : 'Failed to import clients'
      );
    }

    event.target.value = ''; // Reset file input
  };

  const handleDeleteClient = async (clientId) => {
    if (!confirm(isRTL ? 'هل أنت متأكد من حذف هذا العميل؟' : 'Are you sure you want to delete this client?')) return;

    try {
      const result = await apiHelpers.delete(`/clients/${clientId}`);
      
      if (result.success) {
        toast.success(isRTL ? 'تم حذف العميل بنجاح' : 'Client deleted successfully');
        loadClients();
      }
    } catch (error) {
      toast.error(isRTL ? 'فشل في حذف العميل' : 'Failed to delete client');
    }
  };

  const handleBulkDelete = async () => {
    if (selectedClients.length === 0) return;
    if (!confirm(t('clients.delete_confirm'))) return;

    try {
      await Promise.all(
        selectedClients.map(id => apiHelpers.delete(endpoints.clients.delete(id)))
      );
      
      toast.success(t('messages_ui.success_delete'));
      setSelectedClients([]);
      loadClients();
    } catch (error) {
      toast.error(t('messages_ui.error_delete'));
    }
  };

  const handleExportClients = async () => {
    try {
      // Get all clients for export
      const response = await apiHelpers.get('/clients?limit=1000');
      if (response.success) {
        const csvContent = convertToCSV(response.data.clients);
        downloadCSV(csvContent, 'clients-export.csv');
        toast.success(
          isRTL
            ? 'تم تصدير العملاء بنجاح'
            : 'Clients exported successfully'
        );
      }
    } catch (error) {
      console.error('Error exporting clients:', error);
      toast.error(
        isRTL
          ? 'فشل في تصدير العملاء'
          : 'Failed to export clients'
      );
    }
  };

  const handleSelectAll = (checked) => {
    if (checked) {
      setSelectedClients(clients.map(client => client.id));
    } else {
      setSelectedClients([]);
    }
  };

  const handleSelectClient = (clientId, checked) => {
    if (checked) {
      setSelectedClients([...selectedClients, clientId]);
    } else {
      setSelectedClients(selectedClients.filter(id => id !== clientId));
    }
  };

  const getStatusBadge = (status) => {
    const statusConfig = {
      active: {
        class: 'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',
        text: isRTL ? 'نشط' : 'Active'
      },
      inactive: {
        class: 'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200',
        text: isRTL ? 'غير نشط' : 'Inactive'
      },
      pending: {
        class: 'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200',
        text: isRTL ? 'معلق' : 'Pending'
      },
      blocked: {
        class: 'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200',
        text: isRTL ? 'محظور' : 'Blocked'
      }
    };

    const config = statusConfig[status] || statusConfig.active;
    return <span className={config.class}>{config.text}</span>;
  };

  const getCategoryBadge = (category) => {
    const categoryConfig = {
      general: {
        class: 'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200',
        text: isRTL ? 'عام' : 'General'
      },
      premium: {
        class: 'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200',
        text: isRTL ? 'مميز' : 'Premium'
      },
      vip: {
        class: 'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200',
        text: isRTL ? 'كبار الشخصيات' : 'VIP'
      },
      corporate: {
        class: 'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-indigo-100 text-indigo-800 dark:bg-indigo-900 dark:text-indigo-200',
        text: isRTL ? 'شركات' : 'Corporate'
      },
      individual: {
        class: 'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-teal-100 text-teal-800 dark:bg-teal-900 dark:text-teal-200',
        text: isRTL ? 'أفراد' : 'Individual'
      }
    };

    const config = categoryConfig[category] || categoryConfig.general;
    return <span className={config.class}>{config.text}</span>;
  };

  if (loading && clients.length === 0) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="large" text={t('common.loading')} />
      </div>
    );
  }

  return (
    <div className="flex-1 overflow-hidden">
      <div className="h-full flex flex-col">
        {/* Page Header - Fixed width container */}
        <div className="flex-shrink-0 px-6 py-4 bg-white border-b border-gray-200">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
            <div className="flex-shrink-0">
              <h1 className="text-3xl font-bold text-gray-900">
                {t('clients.title')}
              </h1>
              <p className="mt-2 text-gray-600">
                {t('clients.client_list')}
              </p>
            </div>
            <div className="flex flex-wrap items-center gap-3 flex-shrink-0">
          <input
            type="file"
            accept=".csv,.xlsx,.xls"
            onChange={handleImportClients}
            style={{ display: 'none' }}
            id="import-clients-input"
          />
          <button
            onClick={() => document.getElementById('import-clients-input').click()}
            className="btn btn-outline flex items-center space-x-2 rtl:space-x-reverse"
          >
            <Upload className="h-4 w-4" />
            <span>{isRTL ? 'استيراد العملاء' : 'Import Clients'}</span>
          </button>
          <button
            onClick={handleExportClients}
            className="btn btn-outline flex items-center space-x-2 rtl:space-x-reverse"
          >
            <Download className="h-4 w-4" />
            <span>{isRTL ? 'تصدير العملاء' : 'Export Clients'}</span>
          </button>
          {clients.length === 0 && (
            <button
              onClick={createSampleData}
              className="btn btn-success flex items-center space-x-2 rtl:space-x-reverse mr-3 rtl:ml-3 rtl:mr-0"
            >
              <Users className="h-4 w-4" />
              <span>{isRTL ? 'إنشاء بيانات تجريبية' : 'Create Sample Data'}</span>
            </button>
          )}
          <button
            onClick={() => navigate('/clients/new')}
            className="btn btn-primary flex items-center space-x-2 rtl:space-x-reverse"
          >
            <Plus className="h-4 w-4" />
            <span>{isRTL ? 'إضافة عميل' : 'Add Client'}</span>
          </button>
        </div>
      </div>

      {/* Search and Filters */}
      <div className="card">
        <div className="card-body">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
            {/* Search */}
            <div className="flex-1 max-w-lg">
              <div className="relative">
                <div className={`absolute inset-y-0 ${isRTL ? 'right-0 pr-3' : 'left-0 pl-3'} flex items-center pointer-events-none`}>
                  <Search className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  type="text"
                  placeholder={t('clients.search_clients')}
                  className={`input ${isRTL ? 'pr-10' : 'pl-10'}`}
                  {...register('search')}
                />
              </div>
            </div>

            {/* Filter Toggle */}
            <div className="flex items-center space-x-3 rtl:space-x-reverse">
              <button
                onClick={() => setShowFilters(!showFilters)}
                className="btn btn-outline flex items-center space-x-2 rtl:space-x-reverse"
              >
                <Filter className="h-4 w-4" />
                <span>{t('common.filter')}</span>
              </button>

              {selectedClients.length > 0 && (
                <button
                  onClick={handleBulkDelete}
                  className="btn btn-danger flex items-center space-x-2 rtl:space-x-reverse"
                >
                  <Trash2 className="h-4 w-4" />
                  <span>{t('clients.bulk_delete')} ({selectedClients.length})</span>
                </button>
              )}
            </div>
          </div>

          {/* Filters */}
          {showFilters && (
            <div className="mt-4 pt-4 border-t border-gray-200">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    {t('clients.filter_by_category')}
                  </label>
                  <select className="input" {...register('category')}>
                    <option value="">{t('common.all')}</option>
                    <option value="general">{t('clients.general')}</option>
                    <option value="premium">{t('clients.premium')}</option>
                    <option value="vip">{t('clients.vip')}</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    {t('clients.filter_by_language')}
                  </label>
                  <select className="input" {...register('language')}>
                    <option value="">{t('common.all')}</option>
                    <option value="ar">العربية</option>
                    <option value="en">English</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    {t('clients.filter_by_status')}
                  </label>
                  <select className="input" {...register('status')}>
                    <option value="">{t('common.all')}</option>
                    <option value="active">{t('clients.active')}</option>
                    <option value="inactive">{t('clients.inactive')}</option>
                    <option value="blocked">{t('clients.blocked')}</option>
                  </select>
                </div>
              </div>

              <div className="mt-4 flex justify-end">
                <button
                  onClick={() => reset()}
                  className="btn btn-outline"
                >
                  {t('common.clear_filters')}
                </button>
              </div>
            </div>
          )}
        </div>
      </div>

        {/* Main Content Area */}
        <div className="flex-1 overflow-hidden">
          <div className="h-full px-6 py-4 overflow-y-auto">
          {/* Clients Table */}
          <div className="bg-white rounded-lg shadow-sm overflow-hidden mb-8">
            <div className="overflow-x-auto" style={{ maxWidth: '100vw' }}>
              <table className="w-full divide-y divide-gray-200" style={{ minWidth: '900px', borderCollapse: 'collapse', tableLayout: 'fixed' }}>
                <thead className="bg-gray-50">
              <tr>
                <th className="px-3 py-3 text-left rtl:text-right text-xs font-medium text-gray-500 uppercase tracking-wider" style={{ width: '50px' }}>
                  <input
                    type="checkbox"
                    checked={selectedClients.length === clients.length && clients.length > 0}
                    onChange={(e) => handleSelectAll(e.target.checked)}
                    className="rounded border-gray-300"
                  />
                </th>
                <th className="px-3 py-3 text-left rtl:text-right text-xs font-medium text-gray-500 uppercase tracking-wider" style={{ width: '180px' }}>{t('clients.client_name')}</th>
                <th className="px-3 py-3 text-left rtl:text-right text-xs font-medium text-gray-500 uppercase tracking-wider" style={{ width: '220px' }}>{t('clients.client_email')}</th>
                <th className="px-3 py-3 text-left rtl:text-right text-xs font-medium text-gray-500 uppercase tracking-wider" style={{ width: '160px' }}>{t('clients.client_company')}</th>
                <th className="px-3 py-3 text-left rtl:text-right text-xs font-medium text-gray-500 uppercase tracking-wider" style={{ width: '120px' }}>{t('clients.client_category')}</th>
                <th className="px-3 py-3 text-left rtl:text-right text-xs font-medium text-gray-500 uppercase tracking-wider" style={{ width: '100px' }}>{t('clients.client_language')}</th>
                <th className="px-3 py-3 text-left rtl:text-right text-xs font-medium text-gray-500 uppercase tracking-wider" style={{ width: '100px' }}>{t('clients.client_status')}</th>
                <th className="px-3 py-3 text-left rtl:text-right text-xs font-medium text-gray-500 uppercase tracking-wider" style={{ width: '120px' }}>{t('clients.client_created')}</th>
                <th className="px-3 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider" style={{ width: '80px' }}>{t('common.actions')}</th>
              </tr>
            </thead>
            <tbody className="table-body">
              {clients.map((client) => (
                <tr
                  key={client.id}
                  className="table-row-hover cursor-pointer"
                  onClick={() => navigate(`/clients/${client.id}`)}
                >
                  <td className="px-3 py-4" onClick={(e) => e.stopPropagation()}>
                    <input
                      type="checkbox"
                      checked={selectedClients.includes(client.id)}
                      onChange={(e) => handleSelectClient(client.id, e.target.checked)}
                      className="rounded border-gray-300"
                    />
                  </td>
                  <td className="table-cell-name px-3 py-4">
                    <div className="flex items-center space-x-3 rtl:space-x-reverse">
                      <div className="h-8 w-8 bg-primary-100 rounded-full flex items-center justify-center">
                        <span className="text-sm font-medium text-primary-700">
                          {client.name?.charAt(0)?.toUpperCase()}
                        </span>
                      </div>
                      <span className="font-medium text-gray-900 truncate">{client.name}</span>
                    </div>
                  </td>
                  <td className="table-cell-email px-3 py-4">
                    <div className="flex items-center space-x-2 rtl:space-x-reverse">
                      <Mail className="h-4 w-4 text-gray-400 flex-shrink-0" />
                      <span className="text-gray-900 truncate">{client.email}</span>
                    </div>
                  </td>
                  <td className="table-cell-company px-3 py-4 text-gray-900 truncate">{client.company || '-'}</td>
                  <td className="table-cell-category px-3 py-4">{getCategoryBadge(client.category)}</td>
                  <td className="table-cell-status px-3 py-4">
                    <span className="text-sm text-gray-600">
                      {client.language === 'ar' ? 'العربية' : 'English'}
                    </span>
                  </td>
                  <td className="table-cell-status px-3 py-4">{getStatusBadge(client.status)}</td>
                  <td className="table-cell-status px-3 py-4 text-sm text-gray-500">
                    {new Date(client.created_at).toLocaleDateString('en-US', {
                      year: 'numeric',
                      month: 'short',
                      day: 'numeric',
                      calendar: 'gregory'
                    })}
                  </td>
                  <td className="table-cell-actions px-3 py-4" onClick={(e) => e.stopPropagation()}>
                    <div className="relative">
                      <button
                        onClick={() => setActionMenuOpen(actionMenuOpen === client.id ? null : client.id)}
                        className="p-1 text-gray-400 hover:text-gray-600"
                      >
                        <MoreVertical className="h-4 w-4" />
                      </button>

                      {actionMenuOpen === client.id && (
                        <div className={`
                          absolute ${isRTL ? 'left-0' : 'right-0'} mt-2 w-48 bg-white rounded-md shadow-lg ring-1 ring-black ring-opacity-5 z-10
                        `}>
                          <div className="py-1">
                            <button
                              onClick={() => {
                                navigate(`/clients/${client.id}`);
                                setActionMenuOpen(null);
                              }}
                              className="flex items-center space-x-2 rtl:space-x-reverse w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                            >
                              <Eye className="h-4 w-4" />
                              <span>{t('common.view')}</span>
                            </button>
                            <button
                              onClick={() => {
                                navigate(`/clients/${client.id}/edit`);
                                setActionMenuOpen(null);
                              }}
                              className="flex items-center space-x-2 rtl:space-x-reverse w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                            >
                              <Edit className="h-4 w-4" />
                              <span>{t('common.edit')}</span>
                            </button>
                            <button
                              onClick={() => {
                                handleDeleteClient(client.id);
                                setActionMenuOpen(null);
                              }}
                              className="flex items-center space-x-2 rtl:space-x-reverse w-full px-4 py-2 text-sm text-red-600 hover:bg-red-50"
                            >
                              <Trash2 className="h-4 w-4" />
                              <span>{t('common.delete')}</span>
                            </button>
                          </div>
                        </div>
                      )}
                    </div>
                  </td>
                </tr>
              ))}
              </tbody>
              </table>
            </div>

            {clients.length === 0 && !loading && (
              <div className="text-center py-12">
                <Users className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-medium text-gray-900">
                  {t('messages_ui.no_data')}
                </h3>
                <p className="mt-1 text-sm text-gray-500">
                  {isRTL ? 'لا يوجد عملاء حالياً' : 'No clients found'}
                </p>
                <div className="mt-6">
                  <button
                    onClick={() => navigate('/clients/new')}
                    className="btn btn-primary"
                  >
                    {t('clients.add_client')}
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="card-footer">
            <div className="flex items-center justify-between">
              <div className="text-sm text-gray-700">
                {isRTL
                  ? `عرض ${clients.length} من ${totalPages * 10} عميل`
                  : `Showing ${clients.length} of ${totalPages * 10} clients`
                }
              </div>
              <div className="flex items-center space-x-2 rtl:space-x-reverse">
                <button
                  onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                  disabled={currentPage === 1}
                  className="btn btn-outline btn-sm"
                >
                  {t('common.previous')}
                </button>
                <span className="text-sm text-gray-700">
                  {currentPage} {t('common.of')} {totalPages}
                </span>
                <button
                  onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                  disabled={currentPage === totalPages}
                  className="btn btn-outline btn-sm"
                >
                  {t('common.next')}
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Click outside to close action menu */}
        {actionMenuOpen && (
          <div
            className="fixed inset-0 z-5"
            onClick={() => setActionMenuOpen(null)}
          />
        )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Clients;
