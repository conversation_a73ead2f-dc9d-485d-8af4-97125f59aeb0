const express = require('express');
const router = express.Router();
const { auth } = require('../middleware/auth');
const { db } = require('../config/database');
const Message = require('../models/Message');
const Client = require('../models/Client');
const Interaction = require('../models/Interaction');
const emailService = require('../utils/emailService');
const nodemailer = require('nodemailer');
const crypto = require('crypto');

// Direct email sending function with template support
async function sendEmailsDirectly(recipients, subject, content, smtpSettings, message = null) {
  const transporter = nodemailer.createTransport({
    host: smtpSettings.smtp_host,
    port: parseInt(smtpSettings.smtp_port) || 587,
    secure: parseInt(smtpSettings.smtp_port) === 465,
    auth: {
      user: smtpSettings.smtp_username,
      pass: smtpSettings.smtp_password
    }
  });

  const results = [];

  console.log(`📧 Starting to send emails to ${recipients.length} recipients`);

  // Get template if message has template_id
  let template = null;
  if (message && message.template_id) {
    try {
      template = await new Promise((resolve, reject) => {
        db.get(
          'SELECT * FROM email_templates WHERE id = ?',
          [message.template_id],
          (err, row) => {
            if (err) reject(err);
            else resolve(row);
          }
        );
      });
      console.log(`📧 Using template: ${template?.name || 'Default'}`);
    } catch (error) {
      console.error('Error loading template:', error);
    }
  }

  for (const recipient of recipients) {
    try {
      console.log(`📤 Sending email to: ${recipient.email}`);

      // Generate unique tracking ID for this specific send
      const trackingId = crypto.randomBytes(32).toString('hex');
      const messageCode = `MSG_${message?.id || 'NEW'}_${recipient.id}_${Date.now()}`;

      console.log(`🔑 Generated tracking ID: ${trackingId.substring(0, 8)}...`);
      console.log(`📋 Generated message code: ${messageCode}`);

      let emailContent = content;
      let emailSubject = subject;

      // Apply template if available
      if (template) {
        const templateVariables = {
          direction: recipient.language === 'ar' ? 'rtl' : 'ltr',
          language: recipient.language || 'en',
          subject: emailSubject,
          company_name: 'شركتك',
          title: emailSubject,
          greeting: recipient.language === 'ar' ? 'مرحباً' : 'Hello',
          client_name: recipient.name,
          content: emailContent,
          company_address: 'عنوان الشركة',
          unsubscribe_url: `http://localhost:5000/api/tracking/unsubscribe/${recipient.unsubscribe_token || 'invalid'}`,
          unsubscribe_text: recipient.language === 'ar' ? 'إلغاء الاشتراك' : 'Unsubscribe',
          tracking_pixel: `http://localhost:5000/api/tracking/pixel/${trackingId}`
        };

        emailContent = template.html_content;
        Object.keys(templateVariables).forEach(key => {
          const regex = new RegExp(`{{${key}}}`, 'g');
          emailContent = emailContent.replace(regex, templateVariables[key] || '');
        });
      }

      // Add tracking pixel to content if not in template
      if (!emailContent.includes('tracking/pixel/')) {
        emailContent += `<img src="http://localhost:5000/api/tracking/pixel/${trackingId}" width="1" height="1" style="display:none;" />`;
      }

      // Add click tracking to all links
      emailContent = emailContent.replace(/<a\s+([^>]*href\s*=\s*["']([^"']+)["'][^>]*)>/gi, (match, attributes, url) => {
        if (url.startsWith('mailto:') || url.startsWith('#') || url.includes('/api/tracking/')) {
          return match; // Don't track these links
        }
        const encodedUrl = encodeURIComponent(url);
        const trackingUrl = `http://localhost:5000/api/tracking/click/${trackingId}/link?url=${encodedUrl}`;
        return `<a ${attributes.replace(/href\s*=\s*["'][^"']+["']/i, `href="${trackingUrl}"`)}>`;
      });

      const mailOptions = {
        from: smtpSettings.smtp_username,
        to: recipient.email,
        subject: emailSubject,
        html: emailContent
      };

      const result = await transporter.sendMail(mailOptions);

      // Store tracking data in database
      await new Promise((resolve, reject) => {
        db.run(
          `INSERT INTO interactions (
            client_id, message_id, type, status, sent_at, created_at, metadata
          ) VALUES (?, ?, 'email', 'sent', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, ?)`,
          [
            recipient.id,
            message?.id || null,
            JSON.stringify({
              tracking_id: trackingId,
              message_code: messageCode,
              email_message_id: result.messageId,
              recipient_email: recipient.email
            })
          ],
          function(err) {
            if (err) {
              console.error(`❌ Error storing tracking data:`, err);
              reject(err);
            } else {
              console.log(`✅ Tracking data stored with interaction ID: ${this.lastID}`);
              resolve();
            }
          }
        );
      });

      results.push({
        success: true,
        clientId: recipient.id,
        email: recipient.email,
        messageId: result.messageId,
        trackingId: trackingId
      });

      console.log(`✅ Email sent successfully to ${recipient.email} - Message ID: ${result.messageId}`);
    } catch (error) {
      results.push({
        success: false,
        clientId: recipient.id,
        email: recipient.email,
        error: error.message
      });

      console.error(`❌ Failed to send email to ${recipient.email}:`, error.message);
    }
  }

  console.log(`📊 Email sending completed. Success: ${results.filter(r => r.success).length}, Failed: ${results.filter(r => !r.success).length}`);

  return results;
}

// Get all messages with pagination and filtering
router.get('/', auth, async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const filters = {
      type: req.query.type,
      category: req.query.category,
      language: req.query.language,
      status: req.query.status,
      search: req.query.search
    };

    const result = await Message.getAll(page, limit, filters);
    
    res.json({
      success: true,
      data: result,
      message: 'Messages retrieved successfully',
      message_ar: 'تم استرجاع الرسائل بنجاح'
    });
  } catch (error) {
    console.error('Error fetching messages:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch messages',
      message_ar: 'فشل في استرجاع الرسائل'
    });
  }
});

// Get message by ID
router.get('/:id', auth, async (req, res) => {
  try {
    console.log('🔍 Fetching message with ID:', req.params.id);
    const message = await Message.getById(req.params.id);

    if (!message) {
      console.log('❌ Message not found with ID:', req.params.id);
      return res.status(404).json({
        success: false,
        error: 'Message not found',
        message_ar: 'الرسالة غير موجودة'
      });
    }

    console.log('✅ Message found:', { id: message.id, title: message.title });

    res.json({
      success: true,
      data: message,
      message: 'Message retrieved successfully',
      message_ar: 'تم استرجاع الرسالة بنجاح'
    });
  } catch (error) {
    console.error('Error fetching message:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch message',
      message_ar: 'فشل في استرجاع الرسالة'
    });
  }
});

// Create new message
router.post('/', auth, async (req, res) => {
  try {
    const {
      title, title_ar, title_en, content, content_ar, content_en,
      type, category, language, status, scheduled_at, created_by,
      target_clients, total_recipients, subject, subject_ar, template_id
    } = req.body;

    console.log('📝 Creating message with data:', {
      title,
      target_clients: target_clients ? target_clients.length : 0,
      total_recipients,
      status
    });

    // Validate required fields
    if (!title || !content) {
      return res.status(400).json({
        success: false,
        error: 'Title and content are required',
        message_ar: 'العنوان والمحتوى مطلوبان'
      });
    }

    const message = await Message.create({
      title, title_ar, title_en, content, content_ar, content_en,
      type, category, language, status, scheduled_at, created_by,
      subject, subject_ar, template_id, total_recipients
    });

    // Save target clients if provided
    if (target_clients && Array.isArray(target_clients) && target_clients.length > 0) {
      console.log(`💾 Saving ${target_clients.length} target clients for message ${message.id}`);

      // Save target clients to a separate table or update message record
      await new Promise((resolve, reject) => {
        db.run(
          `UPDATE messages SET target_clients = ? WHERE id = ?`,
          [JSON.stringify(target_clients), message.id],
          function(err) {
            if (err) {
              console.error('❌ Error saving target clients:', err);
              reject(err);
            } else {
              console.log('✅ Target clients saved successfully');
              resolve();
            }
          }
        );
      });
    }

    res.status(201).json({
      success: true,
      data: message,
      message: 'Message created successfully',
      message_ar: 'تم إنشاء الرسالة بنجاح'
    });
  } catch (error) {
    console.error('Error creating message:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to create message',
      message_ar: 'فشل في إنشاء الرسالة'
    });
  }
});

// Update message
router.put('/:id', auth, async (req, res) => {
  try {
    const message = await Message.getById(req.params.id);
    if (!message) {
      return res.status(404).json({
        success: false,
        error: 'Message not found',
        message_ar: 'الرسالة غير موجودة'
      });
    }

    const {
      title, title_ar, title_en, content, content_ar, content_en,
      type, category, language, status, scheduled_at,
      target_clients, total_recipients, subject, subject_ar, template_id
    } = req.body;

    console.log('📝 Updating message with data:', {
      id: req.params.id,
      title,
      target_clients: target_clients ? target_clients.length : 0,
      total_recipients,
      status
    });

    const updateData = {
      title, title_ar, title_en, content, content_ar, content_en,
      type, category, language, status, scheduled_at,
      subject, subject_ar, template_id, total_recipients
    };

    // Handle target_clients separately if provided
    if (target_clients !== undefined) {
      updateData.target_clients = JSON.stringify(target_clients);
    }

    const result = await Message.update(req.params.id, updateData);

    res.json({
      success: true,
      data: result,
      message: 'Message updated successfully',
      message_ar: 'تم تحديث الرسالة بنجاح'
    });
  } catch (error) {
    console.error('Error updating message:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to update message',
      message_ar: 'فشل في تحديث الرسالة'
    });
  }
});

// Delete message
router.delete('/:id', auth, async (req, res) => {
  try {
    const message = await Message.getById(req.params.id);
    if (!message) {
      return res.status(404).json({
        success: false,
        error: 'Message not found',
        message_ar: 'الرسالة غير موجودة'
      });
    }

    const result = await Message.delete(req.params.id);

    res.json({
      success: true,
      data: result,
      message: 'Message deleted successfully',
      message_ar: 'تم حذف الرسالة بنجاح'
    });
  } catch (error) {
    console.error('Error deleting message:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to delete message',
      message_ar: 'فشل في حذف الرسالة'
    });
  }
});

// Send message to specific clients
router.post('/:id/send', auth, async (req, res) => {
  try {
    const message = await Message.getById(req.params.id);
    if (!message) {
      return res.status(404).json({
        success: false,
        error: 'Message not found',
        message_ar: 'الرسالة غير موجودة'
      });
    }

    const { client_ids, filters } = req.body;
    let recipients = [];

    // Get recipients based on client_ids or filters
    if (client_ids && client_ids.length > 0) {
      // Send to specific clients
      for (const clientId of client_ids) {
        const client = await Client.getById(clientId);
        if (client && client.status === 'active') {
          recipients.push(client);
        }
      }
    } else if (filters) {
      // Send to clients matching filters
      const result = await Client.getAll(1, 1000, filters);
      recipients = result.clients.filter(client => client.status === 'active');
    } else {
      return res.status(400).json({
        success: false,
        error: 'Either client_ids or filters must be provided',
        message_ar: 'يجب توفير معرفات العملاء أو المرشحات'
      });
    }

    if (recipients.length === 0) {
      return res.status(400).json({
        success: false,
        error: 'No active recipients found',
        message_ar: 'لم يتم العثور على مستقبلين نشطين'
      });
    }

    // Get email configuration from settings table (same as test send)
    const smtpSettings = await new Promise((resolve, reject) => {
      db.all(
        'SELECT key, value FROM settings WHERE key LIKE "smtp_%"',
        [],
        (err, rows) => {
          if (err) reject(err);
          else {
            const settings = {};
            rows.forEach(row => {
              settings[row.key] = row.value;
            });
            resolve(settings);
          }
        }
      );
    });

    console.log('📧 SMTP settings loaded:', Object.keys(smtpSettings));

    if (!smtpSettings.smtp_host || !smtpSettings.smtp_username || !smtpSettings.smtp_password) {
      return res.status(400).json({
        success: false,
        error: 'SMTP configuration incomplete. Please configure SMTP settings first.',
        error_ar: 'إعدادات SMTP غير مكتملة. يرجى تكوين إعدادات SMTP أولاً.'
      });
    }

    // Prepare email content
    const subject = message.language === 'ar' ?
      (message.title_ar || message.title) :
      (message.title_en || message.title);

    const content = message.language === 'ar' ?
      (message.content_ar || message.content) :
      (message.content_en || message.content);

    // Send emails using direct SMTP with template support
    const sendResults = await sendEmailsDirectly(recipients, subject, content, smtpSettings, message);

    // Calculate results
    const successfulSends = sendResults.filter(r => r.success).length;
    const failedSends = sendResults.filter(r => !r.success).length;
    const errors = sendResults.filter(r => !r.success).map(r => r.error);

    console.log(`📊 Send Results - Success: ${successfulSends}, Failed: ${failedSends}`);

    // Record basic interactions (simplified)
    for (const result of sendResults) {
      try {
        if (result.success) {
          // Record successful send using basic interaction
          await new Promise((resolve, reject) => {
            db.run(
              `INSERT INTO interactions (client_id, message_id, type, status, sent_at, created_at)
               VALUES (?, ?, 'email', 'sent', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)`,
              [result.clientId, message.id],
              function(err) {
                if (err) {
                  console.error(`Error recording interaction for client ${result.clientId}:`, err);
                  reject(err);
                } else {
                  console.log(`✅ Recorded interaction for client ${result.clientId}`);
                  resolve();
                }
              }
            );
          });
        }
      } catch (error) {
        console.error('Error recording interaction:', error);
      }
    }

    // Update message status
    await Message.updateStatus(message.id, 'sent');

    const successCount = sendResults.filter(r => r.success).length;
    const failureCount = sendResults.filter(r => !r.success).length;

    res.json({
      success: true,
      data: {
        total_recipients: recipients.length,
        successful_sends: successCount,
        failed_sends: failureCount,
        send_results: sendResults
      },
      message: `Message sent to ${successCount} recipients`,
      message_ar: `تم إرسال الرسالة إلى ${successCount} مستقبل`
    });

  } catch (error) {
    console.error('Error sending message:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to send message',
      message_ar: 'فشل في إرسال الرسالة',
      details: error.message
    });
  }
});

// Test message sending for specific message
router.post('/:id/test', auth, async (req, res) => {
  try {
    const { id } = req.params;
    const { testEmail, clientIds } = req.body;

    console.log(`🧪 Testing message ${id} to email: ${testEmail}`);
    console.log(`📋 Selected clients: ${clientIds ? clientIds.length : 0}`);

    // Get message details
    const message = await new Promise((resolve, reject) => {
      db.get(
        'SELECT * FROM messages WHERE id = ?',
        [id],
        (err, row) => {
          if (err) {
            console.error('❌ Error fetching message:', err);
            reject(err);
          } else {
            console.log('✅ Message found:', row ? row.title : 'Not found');
            resolve(row);
          }
        }
      );
    });

    if (!message) {
      return res.status(404).json({
        success: false,
        error: 'Message not found',
        error_ar: 'الرسالة غير موجودة'
      });
    }

    // Get SMTP settings
    const smtpSettings = await new Promise((resolve, reject) => {
      db.all(
        'SELECT key, value FROM settings WHERE key LIKE "smtp_%"',
        [],
        (err, rows) => {
          if (err) {
            console.error('❌ Error fetching SMTP settings:', err);
            reject(err);
          } else {
            const settings = {};
            rows.forEach(row => {
              settings[row.key] = row.value;
            });
            console.log('✅ SMTP settings loaded:', Object.keys(settings));
            resolve(settings);
          }
        }
      );
    });

    // Check SMTP configuration
    if (!smtpSettings.smtp_host || !smtpSettings.smtp_username || !smtpSettings.smtp_password) {
      console.error('❌ SMTP configuration incomplete');
      return res.status(400).json({
        success: false,
        error: 'SMTP configuration incomplete. Please configure SMTP settings first.',
        error_ar: 'إعدادات SMTP غير مكتملة. يرجى تكوين إعدادات SMTP أولاً.'
      });
    }

    // Setup nodemailer
    const nodemailer = require('nodemailer');
    const transporter = nodemailer.createTransport({
      host: smtpSettings.smtp_host,
      port: parseInt(smtpSettings.smtp_port) || 587,
      secure: parseInt(smtpSettings.smtp_port) === 465,
      auth: {
        user: smtpSettings.smtp_username,
        pass: smtpSettings.smtp_password
      }
    });

    // Prepare test email content
    const testSubject = `[TEST] ${message.subject || message.subject_ar || 'Test Message'}`;
    const testContent = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
        <div style="background: #f0f9ff; padding: 15px; border-radius: 8px; margin-bottom: 20px;">
          <h3 style="color: #0369a1; margin: 0;">🧪 رسالة اختبار / Test Message</h3>
          <p style="margin: 5px 0 0 0; color: #0369a1;">هذه رسالة اختبار من نظام التسويق الإلكتروني</p>
        </div>

        <h2>${message.title || message.title_ar || 'Test Message'}</h2>

        <div style="border: 1px solid #e5e7eb; padding: 15px; border-radius: 8px;">
          ${message.content || message.content_ar || 'Test content'}
        </div>

        <div style="margin-top: 20px; padding: 15px; background: #f9fafb; border-radius: 8px; font-size: 12px; color: #6b7280;">
          <p><strong>Message ID:</strong> ${message.id}</p>
          <p><strong>Created:</strong> ${message.created_at}</p>
          <p><strong>Test sent at:</strong> ${new Date().toLocaleString()}</p>
        </div>
      </div>
    `;

    // Send test email
    console.log('📧 Sending test email...');
    const result = await transporter.sendMail({
      from: smtpSettings.smtp_username,
      to: testEmail,
      subject: testSubject,
      html: testContent
    });

    console.log('✅ Test email sent successfully:', result.messageId);

    res.json({
      success: true,
      data: {
        messageId: result.messageId,
        testEmail: testEmail,
        message: 'Test email sent successfully',
        message_ar: 'تم إرسال البريد التجريبي بنجاح'
      }
    });

  } catch (error) {
    console.error('❌ Error sending test message:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to send test message: ' + error.message,
      error_ar: 'فشل في إرسال الرسالة التجريبية: ' + error.message
    });
  }
});

// Test email configuration
router.post('/test-email', auth, async (req, res) => {
  try {
    const { email } = req.body;

    if (!email) {
      return res.status(400).json({
        success: false,
        error: 'Email address is required',
        message_ar: 'عنوان البريد الإلكتروني مطلوب'
      });
    }

    const testResult = await emailService.testConnection();
    
    if (testResult.success) {
      // Send test email
      const testContent = emailService.generateEmailTemplate(
        '<h3>Test Email</h3><p>This is a test email from the Email Marketing System.</p><p>هذه رسالة تجريبية من نظام التسويق الإلكتروني.</p>',
        'ar'
      );

      await emailService.sendEmail(email, 'Test Email - رسالة تجريبية', testContent);

      res.json({
        success: true,
        message: 'Test email sent successfully',
        message_ar: 'تم إرسال الرسالة التجريبية بنجاح'
      });
    } else {
      res.status(500).json({
        success: false,
        error: 'Email configuration test failed',
        message_ar: 'فشل في اختبار تكوين البريد الإلكتروني'
      });
    }
  } catch (error) {
    console.error('Error testing email:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to test email configuration',
      message_ar: 'فشل في اختبار تكوين البريد الإلكتروني',
      details: error.message
    });
  }
});

// Get message statistics
router.get('/stats/overview', auth, async (req, res) => {
  try {
    const stats = await Message.getStatistics();
    
    res.json({
      success: true,
      data: stats,
      message: 'Statistics retrieved successfully',
      message_ar: 'تم استرجاع الإحصائيات بنجاح'
    });
  } catch (error) {
    console.error('Error fetching statistics:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch statistics',
      message_ar: 'فشل في استرجاع الإحصائيات'
    });
  }
});

// Send message endpoint
router.post('/send', auth, async (req, res) => {
  try {
    const {
      title,
      title_ar,
      subject,
      subject_ar,
      content,
      content_ar,
      template_id,
      target_clients,
      variables = {}
    } = req.body;

    // First create the message record
    const messageResult = await new Promise((resolve, reject) => {
      db.run(
        `INSERT INTO messages (
          title, title_ar, content, content_ar, subject, subject_ar,
          template_id, status, total_recipients, created_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, 'sending', ?, CURRENT_TIMESTAMP)`,
        [title, title_ar, content, content_ar, subject, subject_ar, template_id, target_clients.length],
        function(err) {
          if (err) reject(err);
          else resolve({ id: this.lastID });
        }
      );
    });

    const messageId = messageResult.id;

    // Get email configuration from settings table
    const smtpSettings = await new Promise((resolve, reject) => {
      db.all(
        'SELECT key, value FROM settings WHERE key LIKE "smtp_%"',
        [],
        (err, rows) => {
          if (err) reject(err);
          else {
            const settings = {};
            rows.forEach(row => {
              settings[row.key] = row.value;
            });
            resolve(settings);
          }
        }
      );
    });

    if (!smtpSettings.smtp_host || !smtpSettings.smtp_username || !smtpSettings.smtp_password) {
      return res.status(400).json({
        success: false,
        error: 'SMTP configuration incomplete. Please configure SMTP settings first.',
        error_ar: 'إعدادات SMTP غير مكتملة. يرجى تكوين إعدادات SMTP أولاً.'
      });
    }

    // Get template if specified
    let template = null;
    if (template_id) {
      template = await new Promise((resolve, reject) => {
        db.get(
          'SELECT * FROM email_templates WHERE id = ?',
          [template_id],
          (err, row) => {
            if (err) reject(err);
            else resolve(row);
          }
        );
      });
    }

    // Get target clients
    const clients = await new Promise((resolve, reject) => {
      const placeholders = target_clients.map(() => '?').join(',');
      db.all(
        `SELECT * FROM clients WHERE id IN (${placeholders}) AND unsubscribed = 0`,
        target_clients,
        (err, rows) => {
          if (err) reject(err);
          else resolve(rows);
        }
      );
    });

    // Setup nodemailer
    const nodemailer = require('nodemailer');
    const crypto = require('crypto');

    const transporter = nodemailer.createTransport({
      host: smtpSettings.smtp_host,
      port: parseInt(smtpSettings.smtp_port) || 587,
      secure: parseInt(smtpSettings.smtp_port) === 465,
      auth: {
        user: smtpSettings.smtp_username,
        pass: smtpSettings.smtp_password
      }
    });

    let successCount = 0;
    let failureCount = 0;

    console.log(`📧 Starting email sending process for ${clients.length} clients`);

    // Send emails to each client
    for (const client of clients) {
      try {
        console.log(`📤 Processing client: ${client.email} (ID: ${client.id})`);

        console.log(`📤 Processing client: ${client.email} (ID: ${client.id})`);

        // Create simple interaction record (same as /:id/send)
        await new Promise((resolve, reject) => {
          db.run(
            `INSERT INTO interactions (client_id, message_id, type, status, sent_at, created_at)
             VALUES (?, ?, 'email', 'sent', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)`,
            [client.id, messageId],
            function(err) {
              if (err) {
                console.error(`❌ Error creating interaction record for client ${client.id}:`, err);
                reject(err);
              } else {
                console.log(`✅ Interaction record created with ID: ${this.lastID}`);
                resolve();
              }
            }
          );
        });

        // Prepare email content
        let emailContent = content;
        let emailSubject = subject;

        // Use client's language preference
        if (client.language === 'ar' && content_ar) {
          emailContent = content_ar;
        }
        if (client.language === 'ar' && subject_ar) {
          emailSubject = subject_ar;
        }

        // If template is used, replace variables
        if (template) {
          const templateVariables = {
            direction: client.language === 'ar' ? 'rtl' : 'ltr',
            language: client.language || 'en',
            subject: emailSubject,
            company_name: 'شركتك', // You can make this configurable
            title: client.language === 'ar' ? (title_ar || title) : title,
            greeting: client.language === 'ar' ? 'مرحباً' : 'Hello',
            client_name: client.name,
            content: emailContent,
            company_address: 'عنوان الشركة', // You can make this configurable
            unsubscribe_url: `${req.protocol}://${req.get('host')}/api/tracking/unsubscribe/${client.unsubscribe_token}`,
            unsubscribe_text: client.language === 'ar' ? 'إلغاء الاشتراك' : 'Unsubscribe',
            tracking_pixel: `${req.protocol}://${req.get('host')}/api/tracking/pixel/simple`,
            ...variables
          };

          emailContent = template.html_content;
          Object.keys(templateVariables).forEach(key => {
            const regex = new RegExp(`{{${key}}}`, 'g');
            emailContent = emailContent.replace(regex, templateVariables[key] || '');
          });

          // Add tracking to links
          emailContent = emailContent.replace(/<a\s+([^>]*href\s*=\s*["']([^"']+)["'][^>]*)>/gi, (match, attributes, url) => {
            if (url.startsWith('mailto:') || url.startsWith('#') || url.includes('/api/tracking/')) {
              return match;
            }
            const trackingUrl = `${req.protocol}://${req.get('host')}/api/tracking/click/${trackingId}/1?url=${encodeURIComponent(url)}`;
            return `<a ${attributes.replace(/href\s*=\s*["'][^"']+["']/i, `href="${trackingUrl}"`)}>`;
          });
        }

        // Send email
        console.log(`📧 Preparing email for ${client.email}`);
        const mailOptions = {
          from: `"${smtpSettings.sender_name || 'Marketing System'}" <${smtpSettings.sender_email || smtpSettings.smtp_username}>`,
          to: client.email,
          subject: emailSubject,
          html: emailContent,
          headers: {
            'X-Message-ID': messageId.toString(),
            'X-Client-ID': client.id.toString()
          }
        };

        console.log(`📤 Sending email to ${client.email} with subject: ${emailSubject}`);
        const emailResult = await transporter.sendMail(mailOptions);
        console.log(`✅ Email sent successfully to ${client.email}, Message ID: ${emailResult.messageId}`);

        successCount++;

        // Update interaction with success details
        await new Promise((resolve, reject) => {
          db.run(
            `UPDATE interactions SET
              status = 'delivered',
              email_message_id = ?,
              delivered_at = CURRENT_TIMESTAMP
             WHERE id = ?`,
            [emailResult.messageId, interactionResult.id],
            function(err) {
              if (err) reject(err);
              else resolve();
            }
          );
        });

        // Update client stats
        await new Promise((resolve, reject) => {
          db.run(
            'UPDATE clients SET total_emails_sent = total_emails_sent + 1, last_activity = CURRENT_TIMESTAMP WHERE id = ?',
            [client.id],
            function(err) {
              if (err) reject(err);
              else resolve();
            }
          );
        });

        console.log(`✅ Email sent successfully to ${client.email} - Message ID: ${messageId}`);

      } catch (error) {
        console.error(`Error sending email to ${client.email}:`, error);
        failureCount++;

        // Update interaction with error
        await new Promise((resolve, reject) => {
          db.run(
            'UPDATE interactions SET status = ?, error_message = ? WHERE client_id = ? AND message_id = ?',
            ['failed', error.message, client.id, messageId],
            function(err) {
              if (err) reject(err);
              else resolve();
            }
          );
        });
      }
    }

    // Update message status
    await new Promise((resolve, reject) => {
      db.run(
        `UPDATE messages SET
          status = 'sent',
          total_sent = ?,
          sent_at = CURRENT_TIMESTAMP
         WHERE id = ?`,
        [successCount, messageId],
        function(err) {
          if (err) reject(err);
          else resolve();
        }
      );
    });

    console.log(`📊 FINAL RESULTS:`);
    console.log(`✅ Total recipients: ${clients.length}`);
    console.log(`✅ Successful sends: ${successCount}`);
    console.log(`❌ Failed sends: ${failureCount}`);
    console.log(`📧 Message ID: ${messageId}`);

    res.json({
      success: true,
      data: {
        message_id: messageId,
        total_recipients: clients.length,
        successful_sends: successCount,
        failed_sends: failureCount,
        message: `Message sent successfully to ${successCount} recipients`,
        message_ar: `تم إرسال الرسالة بنجاح إلى ${successCount} مستلم`
      }
    });

  } catch (error) {
    console.error('Error sending message:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to send message',
      error_ar: 'فشل في إرسال الرسالة',
      details: error.message
    });
  }
});

module.exports = router;
