const express = require('express');
const router = express.Router();
const fs = require('fs');
const Client = require('../models/Client');
const ExcelParser = require('../utils/excelParser');
const { upload, handleUploadError, cleanupFile } = require('../middleware/upload');

// Get all clients with pagination and filtering
router.get('/', async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const filters = {
      category: req.query.category,
      language: req.query.language,
      status: req.query.status,
      search: req.query.search
    };



    const result = await Client.getAll(page, limit, filters);
    
    res.json({
      success: true,
      data: result,
      message: 'Clients retrieved successfully',
      message_ar: 'تم استرجاع العملاء بنجاح'
    });
  } catch (error) {
    console.error('Error fetching clients:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch clients',
      message_ar: 'فشل في استرجاع العملاء'
    });
  }
});

// Get client by ID
router.get('/:id', async (req, res) => {
  try {
    const client = await Client.getById(req.params.id);
    
    if (!client) {
      return res.status(404).json({
        success: false,
        error: 'Client not found',
        message_ar: 'العميل غير موجود'
      });
    }

    res.json({
      success: true,
      data: client,
      message: 'Client retrieved successfully',
      message_ar: 'تم استرجاع العميل بنجاح'
    });
  } catch (error) {
    console.error('Error fetching client:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch client',
      message_ar: 'فشل في استرجاع العميل'
    });
  }
});

// Create new client
router.post('/', async (req, res) => {
  try {
    const { name, email, phone, company, category, language, status, tags, notes } = req.body;

    // Validate required fields
    if (!name || !email) {
      return res.status(400).json({
        success: false,
        error: 'Name and email are required',
        message_ar: 'الاسم والبريد الإلكتروني مطلوبان'
      });
    }

    // Check if email already exists
    const existingClient = await Client.getByEmail(email);
    if (existingClient) {
      return res.status(400).json({
        success: false,
        error: 'Email already exists',
        message_ar: 'البريد الإلكتروني موجود مسبقاً'
      });
    }

    const client = await Client.create({
      name, email, phone, company, category, language, status, tags, notes
    });

    res.status(201).json({
      success: true,
      data: client,
      message: 'Client created successfully',
      message_ar: 'تم إنشاء العميل بنجاح'
    });
  } catch (error) {
    console.error('Error creating client:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to create client',
      message_ar: 'فشل في إنشاء العميل'
    });
  }
});

// Update client
router.put('/:id', async (req, res) => {
  try {
    const { name, email, phone, company, category, language, status, tags, notes } = req.body;

    // Check if client exists
    const existingClient = await Client.getById(req.params.id);
    if (!existingClient) {
      return res.status(404).json({
        success: false,
        error: 'Client not found',
        message_ar: 'العميل غير موجود'
      });
    }

    // Check if email is being changed and if new email already exists
    if (email && email !== existingClient.email) {
      const emailExists = await Client.getByEmail(email);
      if (emailExists) {
        return res.status(400).json({
          success: false,
          error: 'Email already exists',
          message_ar: 'البريد الإلكتروني موجود مسبقاً'
        });
      }
    }

    const result = await Client.update(req.params.id, {
      name, email, phone, company, category, language, status, tags, notes
    });

    res.json({
      success: true,
      data: result,
      message: 'Client updated successfully',
      message_ar: 'تم تحديث العميل بنجاح'
    });
  } catch (error) {
    console.error('Error updating client:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to update client',
      message_ar: 'فشل في تحديث العميل'
    });
  }
});

// Delete client
router.delete('/:id', async (req, res) => {
  try {
    const client = await Client.getById(req.params.id);
    if (!client) {
      return res.status(404).json({
        success: false,
        error: 'Client not found',
        message_ar: 'العميل غير موجود'
      });
    }

    const result = await Client.delete(req.params.id);

    res.json({
      success: true,
      data: result,
      message: 'Client deleted successfully',
      message_ar: 'تم حذف العميل بنجاح'
    });
  } catch (error) {
    console.error('Error deleting client:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to delete client',
      message_ar: 'فشل في حذف العميل'
    });
  }
});

// Upload Excel file and import clients
router.post('/import', upload.single('excel'), handleUploadError, async (req, res) => {
  let filePath = null;
  
  try {
    if (!req.file) {
      return res.status(400).json({
        success: false,
        error: 'No file uploaded',
        message_ar: 'لم يتم رفع أي ملف'
      });
    }

    filePath = req.file.path;
    
    // Read and parse Excel/CSV file
    console.log('📁 Processing uploaded file:', req.file.originalname);
    const fileBuffer = fs.readFileSync(filePath);
    const parseResult = ExcelParser.parseClientsFile(fileBuffer, req.file.originalname);

    // Import valid clients
    let importResult = { inserted: 0, total: 0, errors: [] };
    
    if (parseResult.clients.length > 0) {
      importResult = await Client.bulkInsert(parseResult.clients);
    }

    // Combine parsing and import errors
    const allErrors = [...parseResult.errors, ...importResult.errors];

    res.json({
      success: true,
      data: {
        total: parseResult.total,
        valid: parseResult.valid,
        inserted: importResult.inserted,
        errors: allErrors,
        summary: {
          total_rows: parseResult.total,
          valid_rows: parseResult.valid,
          inserted_rows: importResult.inserted,
          error_rows: allErrors.length
        }
      },
      message: `Imported ${importResult.inserted} clients successfully`,
      message_ar: `تم استيراد ${importResult.inserted} عميل بنجاح`
    });

  } catch (error) {
    console.error('Error importing clients:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to import clients',
      message_ar: 'فشل في استيراد العملاء',
      details: error.message
    });
  } finally {
    // Clean up uploaded file
    if (filePath) {
      cleanupFile(filePath);
    }
  }
});

// Download Excel template
router.get('/template/download', (req, res) => {
  try {
    const template = ExcelParser.generateTemplate();
    
    res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    res.setHeader('Content-Disposition', 'attachment; filename="clients_template.xlsx"');
    
    res.send(template);
  } catch (error) {
    console.error('Error generating template:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to generate template',
      message_ar: 'فشل في إنشاء القالب'
    });
  }
});

// Get client statistics
router.get('/stats/overview', async (req, res) => {
  try {
    const stats = await Client.getStatistics();
    
    res.json({
      success: true,
      data: stats,
      message: 'Statistics retrieved successfully',
      message_ar: 'تم استرجاع الإحصائيات بنجاح'
    });
  } catch (error) {
    console.error('Error fetching statistics:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch statistics',
      message_ar: 'فشل في استرجاع الإحصائيات'
    });
  }
});

// Bulk delete clients
router.delete('/bulk', async (req, res) => {
  try {
    const { clientIds } = req.body;

    console.log('🗑️ Bulk delete request received for clients:', clientIds);

    if (!clientIds || !Array.isArray(clientIds) || clientIds.length === 0) {
      return res.status(400).json({
        success: false,
        error: 'Client IDs array is required',
        error_ar: 'مطلوب مصفوفة معرفات العملاء'
      });
    }

    console.log(`🗑️ Attempting to delete ${clientIds.length} clients`);

    // Delete clients one by one to ensure proper cleanup
    let deletedCount = 0;
    const errors = [];

    for (const clientId of clientIds) {
      try {
        console.log(`🗑️ Deleting client ID: ${clientId}`);

        // Check if client exists
        const client = await Client.getById(clientId);
        if (!client) {
          console.log(`⚠️ Client ${clientId} not found, skipping`);
          continue;
        }

        // Delete the client
        await Client.delete(clientId);
        deletedCount++;
        console.log(`✅ Successfully deleted client ID: ${clientId}`);

      } catch (error) {
        console.error(`❌ Error deleting client ${clientId}:`, error);
        errors.push({ clientId, error: error.message });
      }
    }

    console.log(`📊 Bulk delete results: ${deletedCount} deleted, ${errors.length} errors`);

    res.json({
      success: true,
      data: {
        deleted_count: deletedCount,
        total_requested: clientIds.length,
        errors: errors
      },
      message: `Successfully deleted ${deletedCount} clients`,
      message_ar: `تم حذف ${deletedCount} عميل بنجاح`
    });

  } catch (error) {
    console.error('❌ Error in bulk delete:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to delete clients',
      error_ar: 'فشل في حذف العملاء',
      details: error.message
    });
  }
});

module.exports = router;
