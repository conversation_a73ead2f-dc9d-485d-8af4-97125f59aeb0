import React, { Suspense, useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate, useLocation } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { Toaster } from 'react-hot-toast';

// Styles
import './styles/theme.css';

// Context Providers
import { AuthProvider, useAuth } from './contexts/AuthContext';
import { ThemeProvider } from './contexts/ThemeContext';

// Components
import Layout from './components/Layout/Layout';
import LoadingSpinner from './components/UI/LoadingSpinner';
import ErrorBoundary from './components/UI/ErrorBoundary';

// Pages (lazy loaded for better performance)
const Dashboard = React.lazy(() => import('./pages/Dashboard/Dashboard'));
const Login = React.lazy(() => import('./pages/Auth/Login'));
const Clients = React.lazy(() => import('./pages/Clients/Clients'));
const AddClient = React.lazy(() => import('./pages/Clients/AddClient'));
const ClientDetails = React.lazy(() => import('./pages/Clients/ClientDetails'));
const ClientCategories = React.lazy(() => import('./pages/ClientCategories/ClientCategories'));
const EmailTemplates = React.lazy(() => import('./pages/EmailTemplates/EmailTemplates'));
const Messages = React.lazy(() => import('./pages/Messages/Messages'));
const CreateMessage = React.lazy(() => import('./pages/Messages/CreateMessage'));
const MessageDetails = React.lazy(() => import('./pages/Messages/MessageDetails'));
const EditMessage = React.lazy(() => import('./pages/Messages/EditMessage'));
const SendMessage = React.lazy(() => import('./pages/Messages/SendMessage'));
const Reports = React.lazy(() => import('./pages/Reports/Reports'));
const ComprehensiveReports = React.lazy(() => import('./pages/Reports/ComprehensiveReports'));
const Settings = React.lazy(() => import('./pages/Settings/Settings'));
const EmailTest = React.lazy(() => import('./pages/EmailTest/EmailTest'));
const Profile = React.lazy(() => import('./pages/Profile/Profile'));
const NotFound = React.lazy(() => import('./pages/NotFound/NotFound'));

// Protected Route Component
const ProtectedRoute = ({ children }) => {
  const { isAuthenticated, loading } = useAuth();
  const location = useLocation();

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <LoadingSpinner />
      </div>
    );
  }

  if (!isAuthenticated) {
    // Save the attempted location for redirecting after login
    const redirectTo = location.pathname + location.search;
    return <Navigate to={`/login?redirect=${encodeURIComponent(redirectTo)}`} replace />;
  }

  return children;
};

// Public Route Component (redirect to dashboard if already logged in)
const PublicRoute = ({ children }) => {
  const { isAuthenticated, loading } = useAuth();

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <LoadingSpinner />
      </div>
    );
  }

  return !isAuthenticated ? children : <Navigate to="/dashboard" replace />;
};

function App() {
  const { i18n } = useTranslation();

  useEffect(() => {
    // Set initial direction and language
    const currentLang = i18n.language;
    const isRTL = currentLang === 'ar';
    
    document.documentElement.dir = isRTL ? 'rtl' : 'ltr';
    document.documentElement.lang = currentLang;
    document.body.classList.add(isRTL ? 'rtl' : 'ltr');
    
    // Set CSS custom property for font family
    const fontFamily = isRTL 
      ? "'Tahoma', 'Arial', sans-serif" 
      : "'Inter', system-ui, sans-serif";
    document.documentElement.style.setProperty('--font-family', fontFamily);
  }, [i18n.language]);

  return (
    <ErrorBoundary>
      <ThemeProvider>
        <AuthProvider>
          <Router>
            <div className="App min-h-screen bg-gray-50">
              <Suspense fallback={<LoadingSpinner />}>
                <Routes>
                  {/* Public Routes */}
                  <Route 
                    path="/login" 
                    element={
                      <PublicRoute>
                        <Login />
                      </PublicRoute>
                    } 
                  />
                  
                  {/* Protected Routes */}
                  <Route 
                    path="/" 
                    element={
                      <ProtectedRoute>
                        <Layout />
                      </ProtectedRoute>
                    }
                  >
                    {/* Dashboard */}
                    <Route index element={<Navigate to="/dashboard" replace />} />
                    <Route path="dashboard" element={<Dashboard />} />
                    
                    {/* Clients */}
                    <Route path="clients" element={<Clients />} />
                    <Route path="clients/new" element={<AddClient />} />
                    <Route path="clients/:id" element={<ClientDetails />} />
                    <Route path="client-categories" element={<ClientCategories />} />

                    {/* Email Templates */}
                    <Route path="email-templates" element={<EmailTemplates />} />

                    {/* Messages */}
                    <Route path="messages" element={<Messages />} />
                    <Route path="messages/new" element={<CreateMessage />} />
                    <Route path="messages/:id" element={<MessageDetails />} />
                    <Route path="messages/:id/edit" element={<EditMessage />} />
                    <Route path="messages/:id/send" element={<SendMessage />} />
                    
                    {/* Reports */}
                    <Route path="reports" element={<Reports />} />
                    <Route path="reports/comprehensive" element={<ComprehensiveReports />} />
                    
                    {/* Settings */}
                    <Route path="settings" element={<Settings />} />

                    {/* Email Test */}
                    <Route path="email-test" element={<EmailTest />} />

                    {/* Profile */}
                    <Route path="profile" element={<Profile />} />
                  </Route>
                  
                  {/* 404 Page */}
                  <Route path="*" element={<NotFound />} />
                </Routes>
              </Suspense>
              
              {/* Toast Notifications */}
              <Toaster
                position="top-center"
                reverseOrder={false}
                gutter={8}
                containerClassName=""
                containerStyle={{}}
                toastOptions={{
                  // Default options for all toasts
                  className: '',
                  duration: 4000,
                  style: {
                    background: '#363636',
                    color: '#fff',
                    direction: i18n.language === 'ar' ? 'rtl' : 'ltr',
                    fontFamily: i18n.language === 'ar' 
                      ? "'Tahoma', 'Arial', sans-serif" 
                      : "'Inter', system-ui, sans-serif"
                  },
                  
                  // Success toast options
                  success: {
                    duration: 3000,
                    style: {
                      background: '#10b981',
                    },
                    iconTheme: {
                      primary: '#fff',
                      secondary: '#10b981',
                    },
                  },
                  
                  // Error toast options
                  error: {
                    duration: 5000,
                    style: {
                      background: '#ef4444',
                    },
                    iconTheme: {
                      primary: '#fff',
                      secondary: '#ef4444',
                    },
                  },
                  
                  // Loading toast options
                  loading: {
                    duration: Infinity,
                    style: {
                      background: '#3b82f6',
                    },
                  },
                }}
              />
            </div>
          </Router>
        </AuthProvider>
      </ThemeProvider>
    </ErrorBoundary>
  );
}

export default App;
