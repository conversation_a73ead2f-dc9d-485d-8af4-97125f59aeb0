const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const rateLimit = require('express-rate-limit');
const path = require('path');
require('dotenv').config();

const app = express();
const PORT = process.env.PORT || 5000;

// Security middleware
app.use(helmet());

// Request logging middleware
app.use('/api/', (req, res, next) => {
  const timestamp = new Date().toISOString();
  const ip = req.ip || req.connection.remoteAddress;
  console.log(`[${timestamp}] ${req.method} ${req.originalUrl} - IP: ${ip}`);
  next();
});

// Rate limiting with better monitoring
const limiter = rateLimit({
  windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS) || 15 * 60 * 1000, // 15 minutes
  max: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS) || 500, // Increased for development
  message: {
    error: 'Too many requests from this IP, please try again later.',
    message_ar: 'عدد كبير جداً من الطلبات من هذا العنوان، يرجى المحاولة لاحقاً.'
  },
  standardHeaders: true,
  legacyHeaders: false,
  // Removed deprecated onLimitReached - using handler instead
  handler: (req, res) => {
    const ip = req.ip || req.connection.remoteAddress;
    console.warn(`⚠️  Rate limit exceeded for IP: ${ip} - ${req.method} ${req.originalUrl}`);
    console.warn(`⚠️  تم تجاوز حد الطلبات للعنوان: ${ip}`);

    res.status(429).json({
      error: 'Too many requests from this IP, please try again later.',
      message_ar: 'عدد كبير جداً من الطلبات من هذا العنوان، يرجى المحاولة لاحقاً.',
      retryAfter: Math.round(parseInt(process.env.RATE_LIMIT_WINDOW_MS) / 1000) || 900
    });
  },
  skip: (req) => {
    // Only skip in development AND for specific endpoints
    if (process.env.NODE_ENV === 'development') {
      // Allow unlimited requests for auth and health checks in development
      return req.originalUrl.includes('/auth/') || req.originalUrl.includes('/health');
    }
    return false;
  }
});

app.use('/api/', limiter);

// CORS configuration
const corsOptions = {
  origin: process.env.FRONTEND_URL || 'http://localhost:3000',
  credentials: true,
  optionsSuccessStatus: 200
};
app.use(cors(corsOptions));

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Static files
app.use('/uploads', express.static(path.join(__dirname, 'uploads')));

// API Routes
app.use('/api/clients', require('./routes/clients'));
app.use('/api/customers', require('./routes/customers'));
app.use('/api/client-categories', require('./routes/clientCategories'));
app.use('/api/email-templates', require('./routes/emailTemplates'));
app.use('/api/tracking', require('./routes/tracking'));
app.use('/api/messages', require('./routes/messages'));
app.use('/api/interactions', require('./routes/interactions'));
app.use('/api/reports', require('./routes/reports'));
app.use('/api/auth', require('./routes/auth').router);
app.use('/api/email-config', require('./routes/emailConfig'));
app.use('/api/settings', require('./routes/settings'));
app.use('/api/simple-email', require('./routes/simpleEmail'));

// Health check endpoint
app.get('/api/health', (req, res) => {
  res.json({
    status: 'OK',
    message: 'Email Marketing API is running',
    message_ar: 'واجهة برمجة التطبيقات لنظام التسويق الإلكتروني تعمل بشكل طبيعي',
    timestamp: new Date().toISOString(),
    version: '1.0.0'
  });
});

// Debug endpoint for monitoring requests (development only)
app.get('/api/debug/requests', (req, res) => {
  if (process.env.NODE_ENV !== 'development') {
    return res.status(404).json({ error: 'Not found' });
  }

  res.json({
    success: true,
    data: {
      message: 'Request monitoring active',
      message_ar: 'مراقبة الطلبات نشطة',
      timestamp: new Date().toISOString(),
      rateLimit: {
        windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS) || 15 * 60 * 1000,
        max: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS) || 100
      }
    }
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    error: 'Route not found',
    message_ar: 'المسار غير موجود'
  });
});

// Error handling middleware
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({
    error: 'Something went wrong!',
    message_ar: 'حدث خطأ ما!',
    ...(process.env.NODE_ENV === 'development' && { stack: err.stack })
  });
});

// Initialize database and start server
const { initializeDatabase } = require('./config/database');
const { generateUnsubscribeTokens } = require('./utils/generateUnsubscribeTokens');

async function startServer() {
  try {
    await initializeDatabase();
    console.log('Database initialized successfully');
    console.log('قاعدة البيانات تم تهيئتها بنجاح');

    // Generate unsubscribe tokens for existing clients
    try {
      await generateUnsubscribeTokens();
    } catch (error) {
      console.log('⚠️  Skipping unsubscribe token generation:', error.message);
    }
    
    const server = app.listen(PORT, () => {
      console.log(`Server is running on port ${PORT}`);
      console.log(`الخادم يعمل على المنفذ ${PORT}`);
      console.log(`Health check: http://localhost:${PORT}/api/health`);
    });

    // Handle server errors
    server.on('error', (err) => {
      if (err.code === 'EADDRINUSE') {
        console.error(`Port ${PORT} is already in use. Trying port ${PORT + 1}...`);
        console.error(`المنفذ ${PORT} مستخدم بالفعل. جاري المحاولة على المنفذ ${PORT + 1}...`);

        // Try next port
        const nextPort = PORT + 1;
        const newServer = app.listen(nextPort, () => {
          console.log(`Server running on port ${nextPort}`);
          console.log(`الخادم يعمل على المنفذ ${nextPort}`);
          console.log(`API available at: http://localhost:${nextPort}/api`);
        });

        newServer.on('error', (err) => {
          console.error('Failed to start server on alternative port:', err);
          console.error('فشل في تشغيل الخادم على منفذ بديل:', err);
          process.exit(1);
        });
      } else {
        console.error('Server error:', err);
        console.error('خطأ في الخادم:', err);
        process.exit(1);
      }
    });
  } catch (error) {
    console.error('Failed to start server:', error);
    console.error('فشل في تشغيل الخادم:', error);
    process.exit(1);
  }
}

startServer();

module.exports = app;
