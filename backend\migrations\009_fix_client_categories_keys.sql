-- Fix client_categories table to have proper keys
UPDATE client_categories SET key = 'general' WHERE name = 'General' OR name_en = 'General';
UPDATE client_categories SET key = 'premium' WHERE name = 'Premium' OR name_en = 'Premium';
UPDATE client_categories SET key = 'vip' WHERE name = 'VIP' OR name_en = 'VIP';
UPDATE client_categories SET key = 'corporate' WHERE name = 'Corporate' OR name_en = 'Corporate';
UPDATE client_categories SET key = 'individual' WHERE name = 'Individual' OR name_en = 'Individual';

-- Add key column if it doesn't exist
ALTER TABLE client_categories ADD COLUMN key VARCHAR(50);

-- Update keys based on names
UPDATE client_categories SET key = 'general' WHERE (name_en = 'General' OR name_ar = 'عام') AND key IS NULL;
UPDATE client_categories SET key = 'premium' WHERE (name_en = 'Premium' OR name_ar = 'مميز') AND key IS NULL;
UPDATE client_categories SET key = 'vip' WHERE (name_en = 'VIP' OR name_ar = 'كبار الشخصيات') AND key IS NULL;
UPDATE client_categories SET key = 'corporate' WHERE (name_en = 'Corporate' OR name_ar = 'شركات') AND key IS NULL;
UPDATE client_categories SET key = 'individual' WHERE (name_en = 'Individual' OR name_ar = 'أفراد') AND key IS NULL;
UPDATE client_categories SET key = 'owners' WHERE (name_en = 'Owners' OR name_ar = 'الملاك') AND key IS NULL;
