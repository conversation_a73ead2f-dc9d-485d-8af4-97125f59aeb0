const express = require('express');
const router = express.Router();
const { auth } = require('../middleware/auth');
const { db } = require('../config/database');

// Get all customers with pagination and filtering
router.get('/', auth, async (req, res) => {
  try {
    const { 
      page = 1, 
      limit = 10, 
      search = '', 
      status = '', 
      interest_level = '',
      sort_by = 'created_at',
      sort_order = 'DESC'
    } = req.query;

    const offset = (page - 1) * limit;
    
    // Build WHERE clause
    let whereClause = 'WHERE 1=1';
    const params = [];
    
    if (search) {
      whereClause += ' AND (name LIKE ? OR email LIKE ? OR company LIKE ?)';
      params.push(`%${search}%`, `%${search}%`, `%${search}%`);
    }
    
    if (status) {
      whereClause += ' AND status = ?';
      params.push(status);
    }
    
    if (interest_level) {
      whereClause += ' AND interest_level = ?';
      params.push(interest_level);
    }

    // Get total count
    const countQuery = `SELECT COUNT(*) as total FROM clients ${whereClause}`;
    const countResult = await new Promise((resolve, reject) => {
      db.get(countQuery, params, (err, row) => {
        if (err) reject(err);
        else resolve(row);
      });
    });

    // Get customers
    const query = `
      SELECT 
        id, name, email, phone, company, category, language, status, 
        interest_level, tags, notes, source, last_activity,
        total_emails_sent, total_emails_opened, total_clicks, engagement_score,
        created_at, updated_at
      FROM clients 
      ${whereClause}
      ORDER BY ${sort_by} ${sort_order}
      LIMIT ? OFFSET ?
    `;
    
    params.push(parseInt(limit), offset);
    
    const customers = await new Promise((resolve, reject) => {
      db.all(query, params, (err, rows) => {
        if (err) reject(err);
        else resolve(rows);
      });
    });

    // Calculate engagement levels for each customer
    const customersWithEngagement = customers.map(customer => {
      let engagementLevel = 'low';
      if (customer.engagement_score >= 80) engagementLevel = 'high';
      else if (customer.engagement_score >= 50) engagementLevel = 'medium';
      
      return {
        ...customer,
        engagement_level: engagementLevel,
        open_rate: customer.total_emails_sent > 0 
          ? Math.round((customer.total_emails_opened / customer.total_emails_sent) * 100) 
          : 0,
        click_rate: customer.total_emails_sent > 0 
          ? Math.round((customer.total_clicks / customer.total_emails_sent) * 100) 
          : 0
      };
    });

    res.json({
      success: true,
      data: {
        customers: customersWithEngagement,
        pagination: {
          current_page: parseInt(page),
          total_pages: Math.ceil(countResult.total / limit),
          total_items: countResult.total,
          items_per_page: parseInt(limit)
        }
      }
    });

  } catch (error) {
    console.error('Error fetching customers:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch customers',
      error_ar: 'فشل في استرجاع العملاء'
    });
  }
});

// Get single customer with detailed analytics
router.get('/:id', auth, async (req, res) => {
  try {
    const { id } = req.params;

    // Get customer details
    const customer = await new Promise((resolve, reject) => {
      db.get(
        'SELECT * FROM clients WHERE id = ?',
        [id],
        (err, row) => {
          if (err) reject(err);
          else resolve(row);
        }
      );
    });

    if (!customer) {
      return res.status(404).json({
        success: false,
        error: 'Customer not found',
        error_ar: 'العميل غير موجود'
      });
    }

    // Get email logs for this customer
    const emailLogs = await new Promise((resolve, reject) => {
      db.all(
        `SELECT 
          el.*, 
          ec.name as campaign_name 
         FROM email_logs el 
         LEFT JOIN email_campaigns ec ON el.campaign_id = ec.id 
         WHERE el.client_id = ? 
         ORDER BY el.sent_at DESC 
         LIMIT 50`,
        [id],
        (err, rows) => {
          if (err) reject(err);
          else resolve(rows);
        }
      );
    });

    // Get recent activities
    const activities = await new Promise((resolve, reject) => {
      db.all(
        'SELECT * FROM client_activities WHERE client_id = ? ORDER BY created_at DESC LIMIT 20',
        [id],
        (err, rows) => {
          if (err) reject(err);
          else resolve(rows);
        }
      );
    });

    // Calculate detailed analytics
    const analytics = {
      total_emails: emailLogs.length,
      total_opens: emailLogs.reduce((sum, log) => sum + log.open_count, 0),
      total_clicks: emailLogs.reduce((sum, log) => sum + log.click_count, 0),
      unique_opens: emailLogs.filter(log => log.open_count > 0).length,
      unique_clicks: emailLogs.filter(log => log.click_count > 0).length,
      last_open: emailLogs.find(log => log.opened_at)?.opened_at,
      last_click: emailLogs.find(log => log.clicked_at)?.clicked_at,
      engagement_trend: calculateEngagementTrend(emailLogs)
    };

    res.json({
      success: true,
      data: {
        customer,
        email_logs: emailLogs,
        activities,
        analytics
      }
    });

  } catch (error) {
    console.error('Error fetching customer details:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch customer details',
      error_ar: 'فشل في استرجاع تفاصيل العميل'
    });
  }
});

// Helper function to calculate engagement trend
function calculateEngagementTrend(emailLogs) {
  const last30Days = emailLogs.filter(log => {
    const logDate = new Date(log.sent_at);
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
    return logDate >= thirtyDaysAgo;
  });

  const totalSent = last30Days.length;
  const totalOpened = last30Days.filter(log => log.open_count > 0).length;
  const totalClicked = last30Days.filter(log => log.click_count > 0).length;

  return {
    period: '30_days',
    emails_sent: totalSent,
    open_rate: totalSent > 0 ? Math.round((totalOpened / totalSent) * 100) : 0,
    click_rate: totalSent > 0 ? Math.round((totalClicked / totalSent) * 100) : 0
  };
}

// Create new customer
router.post('/', auth, async (req, res) => {
  try {
    const {
      name,
      email,
      phone,
      company,
      category = 'general',
      language = 'ar',
      status = 'active',
      interest_level = 'unknown',
      tags,
      notes,
      source
    } = req.body;

    // Validate required fields
    if (!name || !email) {
      return res.status(400).json({
        success: false,
        error: 'Name and email are required',
        error_ar: 'الاسم والبريد الإلكتروني مطلوبان'
      });
    }

    // Check if email already exists
    const existingCustomer = await new Promise((resolve, reject) => {
      db.get(
        'SELECT id FROM clients WHERE email = ?',
        [email],
        (err, row) => {
          if (err) reject(err);
          else resolve(row);
        }
      );
    });

    if (existingCustomer) {
      return res.status(400).json({
        success: false,
        error: 'Email already exists',
        error_ar: 'البريد الإلكتروني موجود بالفعل'
      });
    }

    // Insert new customer
    const result = await new Promise((resolve, reject) => {
      db.run(
        `INSERT INTO clients (
          name, email, phone, company, category, language, status, 
          interest_level, tags, notes, source
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        [name, email, phone, company, category, language, status, interest_level, tags, notes, source],
        function(err) {
          if (err) reject(err);
          else resolve({ id: this.lastID });
        }
      );
    });

    // Log activity
    await logCustomerActivity(result.id, 'customer_created', {
      created_by: 'admin',
      source: source || 'manual'
    });

    res.status(201).json({
      success: true,
      data: {
        id: result.id,
        message: 'Customer created successfully',
        message_ar: 'تم إنشاء العميل بنجاح'
      }
    });

  } catch (error) {
    console.error('Error creating customer:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to create customer',
      error_ar: 'فشل في إنشاء العميل'
    });
  }
});

// Helper function to log customer activity
async function logCustomerActivity(clientId, activityType, activityData, ipAddress = null, userAgent = null) {
  try {
    await new Promise((resolve, reject) => {
      db.run(
        'INSERT INTO client_activities (client_id, activity_type, activity_data, ip_address, user_agent) VALUES (?, ?, ?, ?, ?)',
        [clientId, activityType, JSON.stringify(activityData), ipAddress, userAgent],
        function(err) {
          if (err) reject(err);
          else resolve();
        }
      );
    });
  } catch (error) {
    console.error('Error logging customer activity:', error);
  }
}

// Update customer
router.put('/:id', auth, async (req, res) => {
  try {
    const { id } = req.params;
    const {
      name,
      email,
      phone,
      company,
      category,
      language,
      status,
      interest_level,
      tags,
      notes,
      source
    } = req.body;

    // Check if customer exists
    const existingCustomer = await new Promise((resolve, reject) => {
      db.get(
        'SELECT * FROM clients WHERE id = ?',
        [id],
        (err, row) => {
          if (err) reject(err);
          else resolve(row);
        }
      );
    });

    if (!existingCustomer) {
      return res.status(404).json({
        success: false,
        error: 'Customer not found',
        error_ar: 'العميل غير موجود'
      });
    }

    // Update customer
    await new Promise((resolve, reject) => {
      db.run(
        `UPDATE clients SET
          name = ?, email = ?, phone = ?, company = ?, category = ?,
          language = ?, status = ?, interest_level = ?, tags = ?,
          notes = ?, source = ?, updated_at = CURRENT_TIMESTAMP
         WHERE id = ?`,
        [name, email, phone, company, category, language, status, interest_level, tags, notes, source, id],
        function(err) {
          if (err) reject(err);
          else resolve();
        }
      );
    });

    // Log activity
    await logCustomerActivity(id, 'customer_updated', {
      updated_by: 'admin',
      changes: req.body
    });

    res.json({
      success: true,
      data: {
        message: 'Customer updated successfully',
        message_ar: 'تم تحديث العميل بنجاح'
      }
    });

  } catch (error) {
    console.error('Error updating customer:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to update customer',
      error_ar: 'فشل في تحديث العميل'
    });
  }
});

// Delete customer
router.delete('/:id', auth, async (req, res) => {
  try {
    const { id } = req.params;

    // Check if customer exists
    const existingCustomer = await new Promise((resolve, reject) => {
      db.get(
        'SELECT * FROM clients WHERE id = ?',
        [id],
        (err, row) => {
          if (err) reject(err);
          else resolve(row);
        }
      );
    });

    if (!existingCustomer) {
      return res.status(404).json({
        success: false,
        error: 'Customer not found',
        error_ar: 'العميل غير موجود'
      });
    }

    // Delete customer (this will cascade to related records due to foreign keys)
    await new Promise((resolve, reject) => {
      db.run(
        'DELETE FROM clients WHERE id = ?',
        [id],
        function(err) {
          if (err) reject(err);
          else resolve();
        }
      );
    });

    res.json({
      success: true,
      data: {
        message: 'Customer deleted successfully',
        message_ar: 'تم حذف العميل بنجاح'
      }
    });

  } catch (error) {
    console.error('Error deleting customer:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to delete customer',
      error_ar: 'فشل في حذف العميل'
    });
  }
});

// Update customer engagement score
router.post('/:id/update-engagement', auth, async (req, res) => {
  try {
    const { id } = req.params;

    // Calculate engagement score based on recent activity
    const emailLogs = await new Promise((resolve, reject) => {
      db.all(
        'SELECT * FROM email_logs WHERE client_id = ? ORDER BY sent_at DESC LIMIT 10',
        [id],
        (err, rows) => {
          if (err) reject(err);
          else resolve(rows);
        }
      );
    });

    let engagementScore = 0;

    if (emailLogs.length > 0) {
      const totalSent = emailLogs.length;
      const totalOpened = emailLogs.filter(log => log.open_count > 0).length;
      const totalClicked = emailLogs.filter(log => log.click_count > 0).length;

      const openRate = (totalOpened / totalSent) * 100;
      const clickRate = (totalClicked / totalSent) * 100;

      // Calculate engagement score (0-100)
      engagementScore = Math.round((openRate * 0.6) + (clickRate * 0.4));
    }

    // Update engagement score and stats
    await new Promise((resolve, reject) => {
      db.run(
        `UPDATE clients SET
          engagement_score = ?,
          total_emails_sent = ?,
          total_emails_opened = ?,
          total_clicks = ?,
          last_activity = CURRENT_TIMESTAMP,
          updated_at = CURRENT_TIMESTAMP
         WHERE id = ?`,
        [
          engagementScore,
          emailLogs.length,
          emailLogs.reduce((sum, log) => sum + log.open_count, 0),
          emailLogs.reduce((sum, log) => sum + log.click_count, 0),
          id
        ],
        function(err) {
          if (err) reject(err);
          else resolve();
        }
      );
    });

    res.json({
      success: true,
      data: {
        engagement_score: engagementScore,
        message: 'Engagement score updated successfully',
        message_ar: 'تم تحديث نقاط التفاعل بنجاح'
      }
    });

  } catch (error) {
    console.error('Error updating engagement score:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to update engagement score',
      error_ar: 'فشل في تحديث نقاط التفاعل'
    });
  }
});

// Get customer analytics dashboard
router.get('/analytics/dashboard', auth, async (req, res) => {
  try {
    // Get total customers by status
    const customersByStatus = await new Promise((resolve, reject) => {
      db.all(
        'SELECT status, COUNT(*) as count FROM clients GROUP BY status',
        [],
        (err, rows) => {
          if (err) reject(err);
          else resolve(rows);
        }
      );
    });

    // Get customers by interest level
    const customersByInterest = await new Promise((resolve, reject) => {
      db.all(
        'SELECT interest_level, COUNT(*) as count FROM clients GROUP BY interest_level',
        [],
        (err, rows) => {
          if (err) reject(err);
          else resolve(rows);
        }
      );
    });

    // Get engagement distribution
    const engagementDistribution = await new Promise((resolve, reject) => {
      db.all(
        `SELECT
          CASE
            WHEN engagement_score >= 80 THEN 'high'
            WHEN engagement_score >= 50 THEN 'medium'
            ELSE 'low'
          END as engagement_level,
          COUNT(*) as count
         FROM clients
         GROUP BY engagement_level`,
        [],
        (err, rows) => {
          if (err) reject(err);
          else resolve(rows);
        }
      );
    });

    // Get recent activity summary
    const recentActivity = await new Promise((resolve, reject) => {
      db.all(
        `SELECT
          DATE(created_at) as date,
          COUNT(*) as activities
         FROM client_activities
         WHERE created_at >= date('now', '-7 days')
         GROUP BY DATE(created_at)
         ORDER BY date DESC`,
        [],
        (err, rows) => {
          if (err) reject(err);
          else resolve(rows);
        }
      );
    });

    res.json({
      success: true,
      data: {
        customers_by_status: customersByStatus,
        customers_by_interest: customersByInterest,
        engagement_distribution: engagementDistribution,
        recent_activity: recentActivity
      }
    });

  } catch (error) {
    console.error('Error fetching analytics dashboard:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch analytics dashboard',
      error_ar: 'فشل في استرجاع لوحة التحليلات'
    });
  }
});

// Generate unsubscribe token for client
router.post('/:id/generate-unsubscribe-token', auth, async (req, res) => {
  try {
    const { id } = req.params;
    const crypto = require('crypto');
    const token = crypto.randomBytes(32).toString('hex');

    // Update client with unsubscribe token
    await new Promise((resolve, reject) => {
      db.run(
        'UPDATE clients SET unsubscribe_token = ? WHERE id = ?',
        [token, id],
        function(err) {
          if (err) reject(err);
          else resolve();
        }
      );
    });

    res.json({
      success: true,
      data: {
        token,
        unsubscribe_url: `${req.protocol}://${req.get('host')}/api/tracking/unsubscribe/${token}`
      }
    });

  } catch (error) {
    console.error('Error generating unsubscribe token:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to generate unsubscribe token'
    });
  }
});

// Add sample customers for testing (only if no customers exist)
router.post('/seed-sample-data', auth, async (req, res) => {
  try {
    // Check if customers already exist
    const existingCustomers = await new Promise((resolve, reject) => {
      db.get('SELECT COUNT(*) as count FROM clients', [], (err, row) => {
        if (err) reject(err);
        else resolve(row);
      });
    });

    if (existingCustomers.count > 0) {
      return res.json({
        success: true,
        message: 'Sample data already exists',
        message_ar: 'البيانات التجريبية موجودة بالفعل'
      });
    }

    // Sample customers data
    const sampleCustomers = [
      {
        name: 'أحمد محمد',
        email: '<EMAIL>',
        phone: '+966501234567',
        company: 'شركة التقنية المتقدمة',
        category: 'corporate',
        language: 'ar',
        status: 'active',
        interest_level: 'high',
        tags: 'عميل مهم, تقنية, شركة كبيرة',
        notes: 'عميل مهتم بالحلول التقنية المتقدمة',
        source: 'website'
      },
      {
        name: 'Sarah Johnson',
        email: '<EMAIL>',
        phone: '+1234567890',
        company: 'Tech Solutions Inc',
        category: 'corporate',
        language: 'en',
        status: 'active',
        interest_level: 'medium',
        tags: 'technology, corporate, follow-up',
        notes: 'Interested in email marketing solutions',
        source: 'referral'
      },
      {
        name: 'فاطمة علي',
        email: '<EMAIL>',
        phone: '+966509876543',
        company: 'مؤسسة النور للتجارة',
        category: 'individual',
        language: 'ar',
        status: 'pending',
        interest_level: 'low',
        tags: 'تجارة, متابعة',
        notes: 'تحتاج لمتابعة إضافية',
        source: 'social_media'
      },
      {
        name: 'Michael Brown',
        email: '<EMAIL>',
        phone: '+1987654321',
        company: 'Brown Enterprises',
        category: 'vip',
        language: 'en',
        status: 'active',
        interest_level: 'high',
        tags: 'vip, enterprise, priority',
        notes: 'VIP customer with high engagement',
        source: 'manual'
      },
      {
        name: 'نورا خالد',
        email: '<EMAIL>',
        phone: '+966512345678',
        company: null,
        category: 'individual',
        language: 'ar',
        status: 'inactive',
        interest_level: 'unknown',
        tags: 'فرد, غير نشط',
        notes: 'لم تتفاعل مؤخراً',
        source: 'import'
      }
    ];

    // Insert sample customers
    for (const customer of sampleCustomers) {
      await new Promise((resolve, reject) => {
        db.run(
          `INSERT INTO clients (
            name, email, phone, company, category, language, status,
            interest_level, tags, notes, source, engagement_score
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
          [
            customer.name, customer.email, customer.phone, customer.company,
            customer.category, customer.language, customer.status,
            customer.interest_level, customer.tags, customer.notes,
            customer.source, Math.floor(Math.random() * 100)
          ],
          function(err) {
            if (err) reject(err);
            else resolve({ id: this.lastID });
          }
        );
      });
    }

    res.json({
      success: true,
      message: 'Sample data created successfully',
      message_ar: 'تم إنشاء البيانات التجريبية بنجاح',
      count: sampleCustomers.length
    });

  } catch (error) {
    console.error('Error creating sample data:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to create sample data',
      error_ar: 'فشل في إنشاء البيانات التجريبية'
    });
  }
});

module.exports = router;
