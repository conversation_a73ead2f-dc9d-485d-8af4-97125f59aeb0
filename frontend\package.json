{"name": "email-marketing-frontend", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.20.1", "i18next": "^23.7.6", "react-i18next": "^13.5.0", "i18next-browser-languagedetector": "^7.2.0", "axios": "^1.6.2", "react-query": "^3.39.3", "react-hook-form": "^7.48.2", "react-table": "^7.8.0", "recharts": "^2.8.0", "react-dropzone": "^14.2.3", "react-hot-toast": "^2.4.1", "lucide-react": "^0.294.0", "clsx": "^2.0.0"}, "devDependencies": {"@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@vitejs/plugin-react": "^4.1.1", "vite": "^5.0.0", "eslint": "^8.53.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.4", "tailwindcss": "^3.3.6", "autoprefixer": "^10.4.16", "postcss": "^8.4.32"}}