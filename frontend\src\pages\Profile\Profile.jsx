import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { 
  User, 
  Mail, 
  Lock, 
  Save, 
  Eye, 
  EyeOff,
  Camera,
  Edit
} from 'lucide-react';
import { useForm } from 'react-hook-form';
import toast from 'react-hot-toast';
import LoadingSpinner from '../../components/UI/LoadingSpinner';
import { useAuth } from '../../contexts/AuthContext';

const Profile = () => {
  const { t, i18n } = useTranslation();
  const { user, updateProfile, changePassword } = useAuth();
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('profile');
  const [showCurrentPassword, setShowCurrentPassword] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  const isRTL = i18n.language === 'ar';

  // Profile form
  const profileForm = useForm({
    defaultValues: {
      username: user?.username || '',
      email: user?.email || '',
      first_name: user?.first_name || '',
      last_name: user?.last_name || '',
      phone: user?.phone || '',
      bio: user?.bio || ''
    }
  });

  // Password form
  const passwordForm = useForm({
    defaultValues: {
      current_password: '',
      new_password: '',
      confirm_password: ''
    }
  });

  const handleProfileUpdate = async (data) => {
    try {
      setLoading(true);
      const result = await updateProfile(data);
      
      if (result.success) {
        toast.success(t('auth.profile_updated'));
      }
    } catch (error) {
      toast.error(t('auth.profile_error'));
    } finally {
      setLoading(false);
    }
  };

  const handlePasswordChange = async (data) => {
    if (data.new_password !== data.confirm_password) {
      passwordForm.setError('confirm_password', {
        type: 'manual',
        message: t('validation.passwords_not_match')
      });
      return;
    }

    try {
      setLoading(true);
      const result = await changePassword({
        currentPassword: data.current_password,
        newPassword: data.new_password
      });
      
      if (result.success) {
        passwordForm.reset();
        toast.success(t('auth.password_changed'));
      }
    } catch (error) {
      toast.error(t('auth.password_error'));
    } finally {
      setLoading(false);
    }
  };

  const tabs = [
    {
      id: 'profile',
      title: t('auth.profile'),
      icon: User,
      description: isRTL ? 'معلومات الملف الشخصي' : 'Personal information'
    },
    {
      id: 'password',
      title: t('auth.change_password'),
      icon: Lock,
      description: isRTL ? 'تغيير كلمة المرور' : 'Change password'
    }
  ];

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div>
        <h1 className="text-3xl font-bold text-gray-900">
          {t('navigation.profile')}
        </h1>
        <p className="mt-2 text-gray-600">
          {isRTL ? 'إدارة معلومات الملف الشخصي والإعدادات' : 'Manage your profile information and settings'}
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Profile Card */}
        <div className="lg:col-span-1">
          <div className="card">
            <div className="card-body text-center">
              {/* Avatar */}
              <div className="relative inline-block mb-4">
                <div className="h-24 w-24 bg-primary-600 rounded-full flex items-center justify-center mx-auto">
                  <User className="h-12 w-12 text-white" />
                </div>
                <button className="absolute bottom-0 right-0 h-8 w-8 bg-white rounded-full shadow-lg flex items-center justify-center border-2 border-gray-200 hover:bg-gray-50">
                  <Camera className="h-4 w-4 text-gray-600" />
                </button>
              </div>

              {/* User Info */}
              <h3 className="text-lg font-medium text-gray-900">
                {user?.username}
              </h3>
              <p className="text-sm text-gray-500 mb-4">
                {user?.email}
              </p>
              <span className="badge badge-success">
                {user?.role || 'Admin'}
              </span>

              {/* Quick Stats */}
              <div className="mt-6 pt-6 border-t border-gray-200">
                <div className="grid grid-cols-2 gap-4 text-center">
                  <div>
                    <div className="text-lg font-semibold text-gray-900">12</div>
                    <div className="text-xs text-gray-500">
                      {isRTL ? 'الرسائل' : 'Messages'}
                    </div>
                  </div>
                  <div>
                    <div className="text-lg font-semibold text-gray-900">150</div>
                    <div className="text-xs text-gray-500">
                      {isRTL ? 'العملاء' : 'Clients'}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Navigation */}
          <nav className="mt-6 space-y-1">
            {tabs.map((tab) => {
              const Icon = tab.icon;
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`
                    w-full flex items-center space-x-3 rtl:space-x-reverse px-3 py-2 text-sm font-medium rounded-md transition-colors
                    ${activeTab === tab.id
                      ? 'bg-primary-100 text-primary-700 border-r-2 border-primary-600'
                      : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                    }
                  `}
                >
                  <Icon className="h-5 w-5 flex-shrink-0" />
                  <div className="text-left rtl:text-right">
                    <div className="font-medium">{tab.title}</div>
                    <div className="text-xs text-gray-500 mt-1">{tab.description}</div>
                  </div>
                </button>
              );
            })}
          </nav>
        </div>

        {/* Content */}
        <div className="lg:col-span-3">
          {/* Profile Tab */}
          {activeTab === 'profile' && (
            <div className="card">
              <div className="card-header">
                <h3 className="text-lg font-medium text-gray-900">
                  {t('auth.edit_profile')}
                </h3>
                <p className="text-sm text-gray-600 mt-1">
                  {isRTL ? 'قم بتحديث معلومات ملفك الشخصي' : 'Update your profile information'}
                </p>
              </div>
              <form onSubmit={profileForm.handleSubmit(handleProfileUpdate)}>
                <div className="card-body space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        {t('auth.username')}
                      </label>
                      <div className="relative">
                        <div className={`absolute inset-y-0 ${isRTL ? 'right-0 pr-3' : 'left-0 pl-3'} flex items-center pointer-events-none`}>
                          <User className="h-5 w-5 text-gray-400" />
                        </div>
                        <input
                          type="text"
                          className={`input ${isRTL ? 'pr-10' : 'pl-10'}`}
                          {...profileForm.register('username', {
                            required: t('validation.required_field')
                          })}
                        />
                      </div>
                      {profileForm.formState.errors.username && (
                        <p className="mt-1 text-sm text-red-600">
                          {profileForm.formState.errors.username.message}
                        </p>
                      )}
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        {t('common.email')}
                      </label>
                      <div className="relative">
                        <div className={`absolute inset-y-0 ${isRTL ? 'right-0 pr-3' : 'left-0 pl-3'} flex items-center pointer-events-none`}>
                          <Mail className="h-5 w-5 text-gray-400" />
                        </div>
                        <input
                          type="email"
                          className={`input ${isRTL ? 'pr-10' : 'pl-10'}`}
                          {...profileForm.register('email', {
                            required: t('validation.required_field'),
                            pattern: {
                              value: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
                              message: t('validation.invalid_email')
                            }
                          })}
                        />
                      </div>
                      {profileForm.formState.errors.email && (
                        <p className="mt-1 text-sm text-red-600">
                          {profileForm.formState.errors.email.message}
                        </p>
                      )}
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        {isRTL ? 'الاسم الأول' : 'First Name'}
                      </label>
                      <input
                        type="text"
                        className="input"
                        {...profileForm.register('first_name')}
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        {isRTL ? 'الاسم الأخير' : 'Last Name'}
                      </label>
                      <input
                        type="text"
                        className="input"
                        {...profileForm.register('last_name')}
                      />
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      {t('common.phone')}
                    </label>
                    <input
                      type="tel"
                      className="input"
                      {...profileForm.register('phone')}
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      {isRTL ? 'نبذة شخصية' : 'Bio'}
                    </label>
                    <textarea
                      rows={4}
                      className="input"
                      placeholder={isRTL ? 'اكتب نبذة عن نفسك...' : 'Tell us about yourself...'}
                      {...profileForm.register('bio')}
                    />
                  </div>
                </div>

                <div className="card-footer">
                  <div className="flex justify-end">
                    <button
                      type="submit"
                      disabled={loading}
                      className="btn btn-primary flex items-center space-x-2 rtl:space-x-reverse"
                    >
                      {loading ? (
                        <LoadingSpinner size="small" />
                      ) : (
                        <Save className="h-4 w-4" />
                      )}
                      <span>{t('common.save')}</span>
                    </button>
                  </div>
                </div>
              </form>
            </div>
          )}

          {/* Password Tab */}
          {activeTab === 'password' && (
            <div className="card">
              <div className="card-header">
                <h3 className="text-lg font-medium text-gray-900">
                  {t('auth.change_password')}
                </h3>
                <p className="text-sm text-gray-600 mt-1">
                  {isRTL ? 'قم بتحديث كلمة المرور الخاصة بك' : 'Update your account password'}
                </p>
              </div>
              <form onSubmit={passwordForm.handleSubmit(handlePasswordChange)}>
                <div className="card-body space-y-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      {t('auth.current_password')}
                    </label>
                    <div className="relative">
                      <div className={`absolute inset-y-0 ${isRTL ? 'right-0 pr-3' : 'left-0 pl-3'} flex items-center pointer-events-none`}>
                        <Lock className="h-5 w-5 text-gray-400" />
                      </div>
                      <input
                        type={showCurrentPassword ? 'text' : 'password'}
                        className={`input ${isRTL ? 'pr-10 pl-10' : 'pl-10 pr-10'}`}
                        {...passwordForm.register('current_password', {
                          required: t('validation.required_field')
                        })}
                      />
                      <button
                        type="button"
                        className={`absolute inset-y-0 ${isRTL ? 'left-0 pl-3' : 'right-0 pr-3'} flex items-center`}
                        onClick={() => setShowCurrentPassword(!showCurrentPassword)}
                      >
                        {showCurrentPassword ? (
                          <EyeOff className="h-5 w-5 text-gray-400" />
                        ) : (
                          <Eye className="h-5 w-5 text-gray-400" />
                        )}
                      </button>
                    </div>
                    {passwordForm.formState.errors.current_password && (
                      <p className="mt-1 text-sm text-red-600">
                        {passwordForm.formState.errors.current_password.message}
                      </p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      {t('auth.new_password')}
                    </label>
                    <div className="relative">
                      <div className={`absolute inset-y-0 ${isRTL ? 'right-0 pr-3' : 'left-0 pl-3'} flex items-center pointer-events-none`}>
                        <Lock className="h-5 w-5 text-gray-400" />
                      </div>
                      <input
                        type={showNewPassword ? 'text' : 'password'}
                        className={`input ${isRTL ? 'pr-10 pl-10' : 'pl-10 pr-10'}`}
                        {...passwordForm.register('new_password', {
                          required: t('validation.required_field'),
                          minLength: {
                            value: 6,
                            message: t('validation.password_too_short')
                          }
                        })}
                      />
                      <button
                        type="button"
                        className={`absolute inset-y-0 ${isRTL ? 'left-0 pl-3' : 'right-0 pr-3'} flex items-center`}
                        onClick={() => setShowNewPassword(!showNewPassword)}
                      >
                        {showNewPassword ? (
                          <EyeOff className="h-5 w-5 text-gray-400" />
                        ) : (
                          <Eye className="h-5 w-5 text-gray-400" />
                        )}
                      </button>
                    </div>
                    {passwordForm.formState.errors.new_password && (
                      <p className="mt-1 text-sm text-red-600">
                        {passwordForm.formState.errors.new_password.message}
                      </p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      {t('auth.confirm_password')}
                    </label>
                    <div className="relative">
                      <div className={`absolute inset-y-0 ${isRTL ? 'right-0 pr-3' : 'left-0 pl-3'} flex items-center pointer-events-none`}>
                        <Lock className="h-5 w-5 text-gray-400" />
                      </div>
                      <input
                        type={showConfirmPassword ? 'text' : 'password'}
                        className={`input ${isRTL ? 'pr-10 pl-10' : 'pl-10 pr-10'}`}
                        {...passwordForm.register('confirm_password', {
                          required: t('validation.required_field')
                        })}
                      />
                      <button
                        type="button"
                        className={`absolute inset-y-0 ${isRTL ? 'left-0 pl-3' : 'right-0 pr-3'} flex items-center`}
                        onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                      >
                        {showConfirmPassword ? (
                          <EyeOff className="h-5 w-5 text-gray-400" />
                        ) : (
                          <Eye className="h-5 w-5 text-gray-400" />
                        )}
                      </button>
                    </div>
                    {passwordForm.formState.errors.confirm_password && (
                      <p className="mt-1 text-sm text-red-600">
                        {passwordForm.formState.errors.confirm_password.message}
                      </p>
                    )}
                  </div>

                  {/* Password Requirements */}
                  <div className="bg-gray-50 p-4 rounded-md">
                    <h4 className="text-sm font-medium text-gray-900 mb-2">
                      {isRTL ? 'متطلبات كلمة المرور:' : 'Password Requirements:'}
                    </h4>
                    <ul className="text-sm text-gray-600 space-y-1">
                      <li>{isRTL ? '• على الأقل 6 أحرف' : '• At least 6 characters'}</li>
                      <li>{isRTL ? '• يُفضل استخدام أحرف كبيرة وصغيرة' : '• Mix of uppercase and lowercase letters'}</li>
                      <li>{isRTL ? '• يُفضل استخدام أرقام ورموز' : '• Include numbers and symbols'}</li>
                    </ul>
                  </div>
                </div>

                <div className="card-footer">
                  <div className="flex justify-end">
                    <button
                      type="submit"
                      disabled={loading}
                      className="btn btn-primary flex items-center space-x-2 rtl:space-x-reverse"
                    >
                      {loading ? (
                        <LoadingSpinner size="small" />
                      ) : (
                        <Lock className="h-4 w-4" />
                      )}
                      <span>{t('auth.change_password')}</span>
                    </button>
                  </div>
                </div>
              </form>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default Profile;
