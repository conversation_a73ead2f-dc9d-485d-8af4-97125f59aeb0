const express = require('express');
const router = express.Router();
const { auth } = require('../middleware/auth');
const { db } = require('../config/database');

// Get all email templates
router.get('/', auth, async (req, res) => {
  try {
    const templates = await new Promise((resolve, reject) => {
      db.all(
        'SELECT * FROM email_templates WHERE is_active = 1 ORDER BY created_at DESC',
        [],
        (err, rows) => {
          if (err) reject(err);
          else resolve(rows);
        }
      );
    });

    // Parse variables JSON for each template
    const templatesWithVariables = templates.map(template => ({
      ...template,
      variables: template.variables ? JSON.parse(template.variables) : []
    }));

    res.json({
      success: true,
      data: templatesWithVariables
    });

  } catch (error) {
    console.error('Error fetching email templates:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch email templates',
      error_ar: 'فشل في استرجاع قوالب البريد الإلكتروني'
    });
  }
});

// Get single email template
router.get('/:id', auth, async (req, res) => {
  try {
    const { id } = req.params;

    const template = await new Promise((resolve, reject) => {
      db.get(
        'SELECT * FROM email_templates WHERE id = ?',
        [id],
        (err, row) => {
          if (err) reject(err);
          else resolve(row);
        }
      );
    });

    if (!template) {
      return res.status(404).json({
        success: false,
        error: 'Template not found',
        error_ar: 'القالب غير موجود'
      });
    }

    // Parse variables JSON
    template.variables = template.variables ? JSON.parse(template.variables) : [];

    res.json({
      success: true,
      data: template
    });

  } catch (error) {
    console.error('Error fetching email template:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch email template',
      error_ar: 'فشل في استرجاع قالب البريد الإلكتروني'
    });
  }
});

// Create new email template
router.post('/', auth, async (req, res) => {
  try {
    const {
      name,
      name_ar,
      description,
      description_ar,
      html_content,
      variables = [],
      category = 'general'
    } = req.body;

    // Validate required fields
    if (!name || !html_content) {
      return res.status(400).json({
        success: false,
        error: 'Template name and HTML content are required',
        error_ar: 'اسم القالب ومحتوى HTML مطلوبان'
      });
    }

    // Insert new template
    const result = await new Promise((resolve, reject) => {
      db.run(
        `INSERT INTO email_templates (
          name, name_ar, description, description_ar, html_content, variables, category
        ) VALUES (?, ?, ?, ?, ?, ?, ?)`,
        [name, name_ar, description, description_ar, html_content, JSON.stringify(variables), category],
        function(err) {
          if (err) reject(err);
          else resolve({ id: this.lastID });
        }
      );
    });

    res.status(201).json({
      success: true,
      data: {
        id: result.id,
        message: 'Template created successfully',
        message_ar: 'تم إنشاء القالب بنجاح'
      }
    });

  } catch (error) {
    console.error('Error creating email template:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to create email template',
      error_ar: 'فشل في إنشاء قالب البريد الإلكتروني'
    });
  }
});

// Update email template
router.put('/:id', auth, async (req, res) => {
  try {
    const { id } = req.params;
    const {
      name,
      name_ar,
      description,
      description_ar,
      html_content,
      variables,
      category,
      is_active
    } = req.body;

    // Check if template exists
    const existingTemplate = await new Promise((resolve, reject) => {
      db.get(
        'SELECT * FROM email_templates WHERE id = ?',
        [id],
        (err, row) => {
          if (err) reject(err);
          else resolve(row);
        }
      );
    });

    if (!existingTemplate) {
      return res.status(404).json({
        success: false,
        error: 'Template not found',
        error_ar: 'القالب غير موجود'
      });
    }

    // Update template
    await new Promise((resolve, reject) => {
      db.run(
        `UPDATE email_templates SET
          name = ?, name_ar = ?, description = ?, description_ar = ?,
          html_content = ?, variables = ?, category = ?, is_active = ?,
          updated_at = CURRENT_TIMESTAMP
         WHERE id = ?`,
        [name, name_ar, description, description_ar, html_content,
         JSON.stringify(variables), category, is_active !== undefined ? is_active : 1, id],
        function(err) {
          if (err) reject(err);
          else resolve();
        }
      );
    });

    res.json({
      success: true,
      data: {
        message: 'Template updated successfully',
        message_ar: 'تم تحديث القالب بنجاح'
      }
    });

  } catch (error) {
    console.error('Error updating email template:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to update email template',
      error_ar: 'فشل في تحديث قالب البريد الإلكتروني'
    });
  }
});

// Delete email template
router.delete('/:id', auth, async (req, res) => {
  try {
    const { id } = req.params;

    // Check if template exists
    const existingTemplate = await new Promise((resolve, reject) => {
      db.get(
        'SELECT * FROM email_templates WHERE id = ?',
        [id],
        (err, row) => {
          if (err) reject(err);
          else resolve(row);
        }
      );
    });

    if (!existingTemplate) {
      return res.status(404).json({
        success: false,
        error: 'Template not found',
        error_ar: 'القالب غير موجود'
      });
    }

    // Check if template is being used by messages
    const messagesUsingTemplate = await new Promise((resolve, reject) => {
      // First check if template_id column exists
      db.get(
        "SELECT COUNT(*) as count FROM pragma_table_info('messages') WHERE name='template_id'",
        [],
        (err, row) => {
          if (err) {
            reject(err);
          } else if (row.count === 0) {
            // Column doesn't exist, so no messages are using this template
            resolve({ count: 0 });
          } else {
            // Column exists, check for usage
            db.get(
              'SELECT COUNT(*) as count FROM messages WHERE template_id = ?',
              [id],
              (err, row) => {
                if (err) reject(err);
                else resolve(row);
              }
            );
          }
        }
      );
    });

    if (messagesUsingTemplate.count > 0) {
      return res.status(400).json({
        success: false,
        error: `Cannot delete template. ${messagesUsingTemplate.count} messages are using this template.`,
        error_ar: `لا يمكن حذف القالب. ${messagesUsingTemplate.count} رسالة تستخدم هذا القالب.`
      });
    }

    // Delete template
    await new Promise((resolve, reject) => {
      db.run(
        'DELETE FROM email_templates WHERE id = ?',
        [id],
        function(err) {
          if (err) reject(err);
          else resolve();
        }
      );
    });

    res.json({
      success: true,
      data: {
        message: 'Template deleted successfully',
        message_ar: 'تم حذف القالب بنجاح'
      }
    });

  } catch (error) {
    console.error('Error deleting email template:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to delete email template',
      error_ar: 'فشل في حذف قالب البريد الإلكتروني'
    });
  }
});

// Preview template with variables
router.post('/:id/preview', auth, async (req, res) => {
  try {
    const { id } = req.params;
    const { variables = {} } = req.body;

    const template = await new Promise((resolve, reject) => {
      db.get(
        'SELECT * FROM email_templates WHERE id = ?',
        [id],
        (err, row) => {
          if (err) reject(err);
          else resolve(row);
        }
      );
    });

    if (!template) {
      return res.status(404).json({
        success: false,
        error: 'Template not found',
        error_ar: 'القالب غير موجود'
      });
    }

    // Replace variables in HTML content
    let previewHtml = template.html_content;
    Object.keys(variables).forEach(key => {
      const regex = new RegExp(`{{${key}}}`, 'g');
      previewHtml = previewHtml.replace(regex, variables[key] || '');
    });

    res.json({
      success: true,
      data: {
        html: previewHtml,
        original_html: template.html_content
      }
    });

  } catch (error) {
    console.error('Error previewing template:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to preview template',
      error_ar: 'فشل في معاينة القالب'
    });
  }
});

module.exports = router;
