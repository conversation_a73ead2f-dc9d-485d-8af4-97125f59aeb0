{"name": "email-marketing-system", "version": "1.0.0", "description": "نظام إدارة التسويق الإلكتروني | Email Marketing Management System", "main": "index.js", "scripts": {"install-all": "npm run install-backend && npm run install-frontend", "install-backend": "cd backend && npm install", "install-frontend": "cd frontend && npm install", "dev": "echo \"Starting development servers...\" && echo \"1. Run 'npm run dev-backend' in one terminal\" && echo \"2. Run 'npm run dev-frontend' in another terminal\" && echo \"Or use: npm run dev-both\"", "dev-both": "concurrently --names \"BACKE<PERSON>,FRONTEND\" --prefix-colors \"blue,green\" \"npm run dev-backend\" \"npm run dev-frontend\"", "dev-backend": "cd backend && npm run dev", "dev-frontend": "cd frontend && npm run dev", "build": "npm run build-frontend", "build-frontend": "cd frontend && npm run build", "start": "npm run start-backend", "start-backend": "cd backend && npm start", "test": "npm run test-backend && npm run test-frontend", "test-backend": "cd backend && npm test", "test-frontend": "cd frontend && npm test", "lint": "npm run lint-backend && npm run lint-frontend", "lint-backend": "cd backend && npm run lint", "lint-frontend": "cd frontend && npm run lint", "clean": "npm run clean-backend && npm run clean-frontend", "clean-backend": "cd backend && rm -rf node_modules package-lock.json", "clean-frontend": "cd frontend && rm -rf node_modules package-lock.json dist", "setup": "npm run install-all && npm run setup-db", "setup-db": "cd backend && npm run init-db"}, "keywords": ["email-marketing", "nodejs", "react", "express", "sqlite", "multilingual", "rtl", "arabic", "english", "i18n", "tailwindcss", "vite"], "author": {"name": "Email Marketing System Team", "email": "<EMAIL>"}, "license": "MIT", "repository": {"type": "git", "url": "https://github.com/your-username/email-marketing-system.git"}, "bugs": {"url": "https://github.com/your-username/email-marketing-system/issues"}, "homepage": "https://github.com/your-username/email-marketing-system#readme", "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}, "devDependencies": {"concurrently": "^8.2.2"}, "workspaces": ["backend", "frontend"]}